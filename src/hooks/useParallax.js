import { useEffect, useRef, useState, useCallback } from 'react';

/**
 * Custom hook for parallax scroll effects
 * @param {number} speed - Parallax speed multiplier (default: 0.5)
 * @param {boolean} enabled - Whether parallax is enabled (default: true)
 * @returns {Object} - Object containing section and background refs
 */
export const useParallax = (speed = 0.5, enabled = true) => {
  const sectionRef = useRef(null);
  const backgroundRef = useRef(null);

  useEffect(() => {
    if (!enabled) return;

    let ticking = false;

    const handleScroll = () => {
      if (!sectionRef.current || !backgroundRef.current) return;

      const rect = sectionRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;

      // Only apply parallax when section is in viewport
      if (rect.top < windowHeight && rect.bottom > 0) {
        const scrolled = windowHeight - rect.top;
        const parallax = scrolled * speed;
        backgroundRef.current.style.transform = `translate3d(0, ${parallax}px, 0)`;
      }
    };

    const throttledScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', throttledScroll, { passive: true });
    handleScroll(); // Initial call

    return () => {
      window.removeEventListener('scroll', throttledScroll);
    };
  }, [speed, enabled]);

  return { sectionRef, backgroundRef };
};

/**
 * Custom hook for intersection observer animations
 * @param {number} threshold - Intersection threshold (default: 0.5)
 * @param {boolean} triggerOnce - Whether to trigger only once (default: true)
 * @returns {Object} - Object containing ref and visibility state
 */
export const useIntersectionObserver = (threshold = 0.5, triggerOnce = true) => {
  const ref = useRef(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          if (triggerOnce) {
            observer.unobserve(entry.target);
          }
        } else if (!triggerOnce) {
          setIsVisible(false);
        }
      },
      { threshold }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current);
      }
    };
  }, [threshold, triggerOnce]);

  return { ref, isVisible };
};

/**
 * Custom hook for scroll button management
 * @param {React.RefObject} containerRef - Reference to scrollable container
 * @returns {Object} - Object containing scroll states and functions
 */
export const useScrollButtons = (containerRef) => {
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);

  const updateScrollButtons = useCallback(() => {
    if (containerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = containerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  }, [containerRef]);

  const scrollLeft = useCallback(() => {
    if (containerRef.current) {
      containerRef.current.scrollBy({ left: -200, behavior: 'smooth' });
    }
  }, [containerRef]);

  const scrollRight = useCallback(() => {
    if (containerRef.current) {
      containerRef.current.scrollBy({ left: 200, behavior: 'smooth' });
    }
  }, [containerRef]);

  useEffect(() => {
    updateScrollButtons();

    const handleResize = () => updateScrollButtons();
    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
  }, [updateScrollButtons]);

  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', updateScrollButtons);
      return () => container.removeEventListener('scroll', updateScrollButtons);
    }
  }, [updateScrollButtons]);

  return {
    canScrollLeft,
    canScrollRight,
    scrollLeft,
    scrollRight,
    updateScrollButtons
  };
};

/**
 * Custom hook for sticky navigation state
 * @param {string} selector - CSS selector for the element to track
 * @param {number} offset - Offset from top when element becomes sticky (default: 72)
 * @returns {boolean} - Whether the element is in sticky state
 */
export const useStickyNavigation = (selector, offset = 72) => {
  const [isSticky, setIsSticky] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const element = document.querySelector(selector);
      if (element) {
        const rect = element.getBoundingClientRect();
        const isScrolled = rect.bottom <= offset;
        setIsSticky(isScrolled);
      }
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Check initial state
    return () => window.removeEventListener('scroll', handleScroll);
  }, [selector, offset]);

  return isSticky;
};
