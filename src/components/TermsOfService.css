/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

/* Terms of Service Page */
.terms-of-service {
  font-family: "Plus Jakarta Sans", sans-serif;
}

/* Animation Keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Hero Section */
.terms-hero {
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
  padding: 120px 0 80px 0;
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.terms-hero--visible {
  opacity: 1;
  transform: translateY(0);
}

.terms-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(0,106,193,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.5;
}

.terms-hero__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 1;
}

.terms-hero__content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.terms-hero__title {
  font-size: clamp(2.5rem, 4vw, 3.5rem);
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 24px 0;
  letter-spacing: -0.02em;
  line-height: 1.1;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s;
}

.terms-hero--visible .terms-hero__title {
  opacity: 1;
  transform: translateY(0);
}

.terms-hero__subtitle {
  font-size: 1.25rem;
  color: #6b7280;
  margin: 0 0 32px 0;
  line-height: 1.6;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
}

.terms-hero--visible .terms-hero__subtitle {
  opacity: 1;
  transform: translateY(0);
}

.terms-hero__meta {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.6s;
}

.terms-hero--visible .terms-hero__meta {
  opacity: 1;
  transform: translateY(0);
}

.terms-hero__revision {
  display: inline-block;
  background: rgba(0, 106, 193, 0.1);
  color: #006ac1;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Content Section */
.terms-content {
  padding: 100px 0;
  background: white;
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);
}

.terms-content--visible {
  opacity: 1;
  transform: translateY(0);
}

.terms-content__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.terms-content__grid {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 60px;
  align-items: start;
}

/* Table of Contents */
.terms-toc {
  position: sticky;
  top: 100px;
  opacity: 0;
  transform: translateX(-30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s;
}

.terms-content--visible .terms-toc {
  opacity: 1;
  transform: translateX(0);
}

.terms-toc__content {
  background: #f8faff;
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(0, 106, 193, 0.1);
}

.terms-toc__title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 20px 0;
}

.terms-toc__nav {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.terms-toc__link {
  color: #6b7280;
  text-decoration: none;
  font-size: 0.875rem;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  word-spacing: 0.05em;
}

.terms-toc__link:hover {
  background: rgba(0, 106, 193, 0.1);
  color: #006ac1;
  border-left-color: #006ac1;
  transform: translateX(4px);
}

/* Main Content */
.terms-main {
  opacity: 0;
  transform: translateX(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s;
}

.terms-content--visible .terms-main {
  opacity: 1;
  transform: translateX(0);
}

.terms-section {
  margin-bottom: 48px;
  scroll-margin-top: 100px;
}

.terms-section__title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 24px 0;
  letter-spacing: -0.02em;
  padding-bottom: 12px;
  border-bottom: 2px solid rgba(0, 106, 193, 0.1);
}

.terms-section__content {
  line-height: 1.7;
  word-spacing: 0.08em;
}

.terms-section__paragraph {
  font-size: 1rem;
  color: #374151;
  margin: 0 0 16px 0;
  line-height: 1.7;
  word-spacing: 0.1em;
  letter-spacing: 0.01em;
}

.terms-section__paragraph:last-child {
  margin-bottom: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .terms-content__grid {
    gap: 40px;
  }

  .terms-toc {
    position: static;
  }
}

@media (max-width: 768px) {
  .terms-hero {
    padding: 100px 0 60px 0;
  }

  .terms-content {
    padding: 80px 0;
  }

  .terms-content__grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .terms-toc__content {
    padding: 20px;
  }

  .terms-section__title {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .terms-hero {
    padding: 80px 0 40px 0;
  }

  .terms-hero__container,
  .terms-content__container {
    padding: 0 16px;
  }

  .terms-content {
    padding: 60px 0;
  }

  .terms-content__grid {
    gap: 32px;
  }

  .terms-toc__content {
    padding: 16px;
  }

  .terms-section {
    margin-bottom: 40px;
  }

  .terms-section__title {
    font-size: 1.375rem;
  }

  .terms-section__paragraph {
    font-size: 0.9375rem;
  }
}
