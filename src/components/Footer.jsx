import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import logo from '../assets/logo.svg';
import './Footer.css';

const Footer = () => {
  const [isVisible, setIsVisible] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    const section = document.querySelector('.footer');
    if (section) {
      observer.observe(section);
    }

    return () => {
      if (section) {
        observer.unobserve(section);
      }
    };
  }, []);

  const footerLinks = {
    solutions: {
      title: "Solutions",
      links: [
        { name: "All Features", href: "/features", isRoute: true },
        { name: "Pricing", href: "/pricing", isRoute: true }
      ]
    },
    company: {
      title: "Company",
      links: [
        { name: "Contact", href: "/contact", isRoute: true }
      ]
    },
    resources: {
      title: "Resources",
      links: [
        { name: "Videos", href: "/videos", isRoute: true },
        { name: "Blog", href: "/blog", isRoute: true }
      ]
    },
    legal: {
      title: "Legal",
      links: [
        { name: "Privacy Policy", href: "/privacy", isRoute: true },
        { name: "Terms of Service", href: "/terms", isRoute: true }
      ]
    }
  };

  const socialLinks = [
    { name: "YouTube", icon: "📺", href: "https://www.youtube.com/@123consultingsolutions" }
  ];

  return (
    <footer className={`footer ${isVisible ? 'footer--visible' : ''}`}>
      <div className="footer__container">
        {/* Main Footer Content */}
        <div className="footer__main">
          {/* Brand Section */}
          <div className="footer__brand">
            <div className="footer__logo">
              <img src={logo} alt="Telescope Logo" className="footer__logo-img" />
            </div>
            <p className="footer__description">
              The everything app for home care agencies. Streamline operations, improve care quality, and grow your business with confidence.
            </p>
            <div className="footer__social">
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.href}
                  className="footer__social-link"
                  aria-label={social.name}
                >
                  <span className="footer__social-icon">{social.icon}</span>
                </a>
              ))}
            </div>
          </div>

          {/* Links Sections */}
          <div className="footer__links">
            {Object.entries(footerLinks).map(([key, section], index) => (
              <div
                key={key}
                className="footer__section"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <h3 className="footer__section-title">{section.title}</h3>
                <ul className="footer__section-links">
                  {section.links.map((link, linkIndex) => (
                    <li key={linkIndex} className="footer__link-item">
                      {link.isRoute ? (
                        <button
                          onClick={() => navigate(link.href)}
                          className="footer__link footer__link--button"
                        >
                          {link.name}
                        </button>
                      ) : (
                        <a href={link.href} className="footer__link">
                          {link.name}
                        </a>
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* Newsletter Section */}
        <div className="footer__newsletter">
          <div className="footer__newsletter-content">
            <h3 className="footer__newsletter-title">Stay Updated</h3>
            <p className="footer__newsletter-description">
              Get the latest home care industry insights and product updates.
            </p>
          </div>
          <div className="footer__newsletter-form">
            <div className="footer__input-group">
              <input
                type="email"
                placeholder="Enter your email"
                className="footer__email-input"
              />
              <button className="footer__subscribe-btn">
                <span>Subscribe</span>
                <svg viewBox="0 0 20 20" fill="currentColor" className="footer__subscribe-icon">
                  <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="footer__bottom">
          <div className="footer__bottom-content">
            <p className="footer__copyright">
              © 2025 Telescope. All rights reserved.
            </p>
            <div className="footer__bottom-links">
              <button
                className="footer__bottom-link"
                onClick={() => navigate('/privacy')}
              >
                Privacy
              </button>
              <button
                className="footer__bottom-link"
                onClick={() => navigate('/terms')}
              >
                Terms
              </button>
              <a href="#" className="footer__bottom-link">Cookies</a>
            </div>
          </div>
          <div className="footer__policy">
            <p className="footer__policy-text">
              Telescope's use and transfer to any other app of information received from Google APIs will adhere to Google API Services User Data Policy, including the Limited Use requirements. TelescopeHR does not share any user data with third-party tools, including AI models.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
