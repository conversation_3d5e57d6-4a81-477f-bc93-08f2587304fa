@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap');

/* Footer - Professional & Comprehensive */
.footer {
  position: relative;
  background: linear-gradient(135deg, #1f2937 0%, #111827 50%, #0f172a 100%);
  font-family: "Plus Jakarta Sans", sans-serif;
  color: white;
  overflow: hidden;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.footer__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 2;
}

/* Main Footer Content */
.footer__main {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 80px;
  padding: 80px 0 60px 0;
  opacity: 0;
  transform: translateY(40px);
  transition: all 0.8s ease-out;
}

.footer--visible .footer__main {
  opacity: 1;
  transform: translateY(0);
}

/* Brand Section */
.footer__brand {
  max-width: 350px;
}

.footer__logo {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.footer__logo-img {
  height: 40px;
  width: auto;
  filter: brightness(0) invert(1);
  transition: all 0.3s ease;
}

.footer__logo-img:hover {
  filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255, 255, 255, 0.3));
}

.footer__description {
  font-size: 16px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 32px 0;
  font-weight: 400;
}

/* Social Links */
.footer__social {
  display: flex;
  gap: 16px;
}

.footer__social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  text-decoration: none;
}

.footer__social-link:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.footer__social-icon {
  font-size: 20px;
  line-height: 1;
}

/* Links Grid */
.footer__links {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;
}

.footer__section {
  opacity: 0;
  transform: translateY(30px);
  animation: sectionReveal 0.8s ease-out forwards;
}

.footer--visible .footer__section {
  animation-play-state: running;
}

.footer__section-title {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin: 0 0 20px 0;
  letter-spacing: -0.01em;
}

.footer__section-links {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.footer__link-item {
  margin: 0;
}

.footer__link {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: all 0.2s ease;
  font-weight: 400;
  line-height: 1.4;
}

.footer__link:hover {
  color: white;
  transform: translateX(4px);
}

.footer__link--button {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  text-align: left;
  font-family: inherit;
}

/* Newsletter Section */
.footer__newsletter {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  align-items: center;
  padding: 40px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease-out 0.2s;
}

.footer--visible .footer__newsletter {
  opacity: 1;
  transform: translateY(0);
}

.footer__newsletter-title {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin: 0 0 8px 0;
  letter-spacing: -0.01em;
}

.footer__newsletter-description {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  line-height: 1.5;
}

.footer__input-group {
  display: flex;
  gap: 12px;
  max-width: 400px;
}

.footer__email-input {
  flex: 1;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: white;
  font-size: 14px;
  font-family: var(--font-europa-grotesk);
  transition: all 0.3s ease;
}

.footer__email-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.footer__email-input:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.3);
}

.footer__subscribe-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #201c44 0%, #2d2654 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  font-family: var(--font-europa-grotesk);
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.footer__subscribe-btn:hover {
  background: linear-gradient(135deg, #2d2654 0%, #3a3366 100%);
  transform: translateY(-1px);
}

.footer__subscribe-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;
}

.footer__subscribe-btn:hover .footer__subscribe-icon {
  transform: translateX(2px);
}

/* Bottom Bar */
.footer__bottom {
  padding: 24px 0;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s ease-out 0.4s;
}

.footer--visible .footer__bottom {
  opacity: 1;
  transform: translateY(0);
}

.footer__bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer__copyright {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
}

.footer__bottom-links {
  display: flex;
  gap: 24px;
}

.footer__bottom-link {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer__bottom-link:hover {
  color: rgba(255, 255, 255, 0.9);
}

.footer__policy {
  margin-top: 20px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.footer__policy-text {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.5;
  margin: 0;
  text-align: center;
}

/* Animations */
@keyframes sectionReveal {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footer__main {
    gap: 60px;
  }

  .footer__links {
    gap: 32px;
  }
}

@media (max-width: 768px) {
  .footer__container {
    padding: 0 16px;
  }

  .footer__main {
    grid-template-columns: 1fr;
    gap: 40px;
    padding: 60px 0 40px 0;
  }

  .footer__links {
    grid-template-columns: repeat(2, 1fr);
    gap: 32px;
  }

  .footer__newsletter {
    grid-template-columns: 1fr;
    gap: 24px;
    text-align: center;
    padding: 32px 0;
  }

  .footer__input-group {
    margin: 0 auto;
  }

  .footer__bottom-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .footer__bottom-links {
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .footer__main {
    padding: 40px 0 32px 0;
  }

  .footer__container {
    padding: 0 12px;
  }

  .footer__links {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .footer__newsletter {
    padding: 24px 0;
  }

  .footer__input-group {
    flex-direction: column;
    width: 100%;
  }

  .footer__subscribe-btn {
    justify-content: center;
  }

  .footer__bottom {
    padding: 20px 0;
  }

  .footer__bottom-links {
    flex-wrap: wrap;
    justify-content: center;
    gap: 12px;
  }
}
