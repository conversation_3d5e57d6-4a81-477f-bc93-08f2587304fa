@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap');

/* ROI Section - Compelling Stats & Final CTA */
.roi-section {
  position: relative;
  padding: 120px 0;
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 50%, #3490e1 100%);
  font-family: "Plus Jakarta Sans", sans-serif;
  color: white;
  overflow: hidden;
}

.roi-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.roi-section__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 2;
}

/* ROI Content */
.roi-section__content {
  margin-bottom: 80px;
}

.roi-section__header {
  text-align: center;
  margin-bottom: 60px;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease-out;
}

.roi-section--visible .roi-section__header {
  opacity: 1;
  transform: translateY(0);
}

.roi-section__title {
  font-size: clamp(2.5rem, 5vw, 3.5rem);
  font-weight: 600;
  color: white;
  line-height: 1.2;
  margin: 0;
  letter-spacing: -0.02em;
}

/* Stats */
.roi-section__stats {
  display: flex;
  flex-direction: column;
  gap: 40px;
  max-width: 900px;
  margin: 0 auto;
}

.roi-section__stat {
  display: flex;
  align-items: flex-start;
  gap: 24px;
  padding: 32px;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  opacity: 0;
  transform: translateX(-40px);
  animation: statReveal 0.8s ease-out forwards;
  transition: all 0.3s ease;
}

.roi-section--visible .roi-section__stat {
  animation-play-state: running;
}

.roi-section__stat:hover {
  transform: translateX(0) translateY(-4px);
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.2);
}

.roi-section__stat-icon {
  font-size: 48px;
  line-height: 1;
  padding: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  height: 80px;
}

.roi-section__stat-content {
  flex: 1;
  padding-top: 8px;
}

.roi-section__stat-text {
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  font-weight: 400;
  letter-spacing: -0.005em;
}

.roi-section__highlight {
  color: #fbbf24;
  font-weight: 700;
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Final CTA */
.roi-section__cta {
  text-align: center;
  opacity: 0;
  transform: translateY(40px);
  transition: all 0.8s ease-out 0.4s;
}

.roi-section--visible .roi-section__cta {
  opacity: 1;
  transform: translateY(0);
}

.roi-section__cta-content {
  max-width: 600px;
  margin: 0 auto;
}

.roi-section__cta-title {
  font-size: clamp(2rem, 4vw, 2.75rem);
  font-weight: 600;
  color: white;
  line-height: 1.3;
  margin: 0 0 40px 0;
  letter-spacing: -0.02em;
}

/* CTA Button */
.roi-section__cta-btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 12px;
  padding: 18px 36px;
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: #1f2937;
  border: none;
  border-radius: 50px;
  font-size: 18px;
  font-weight: 600;
  font-family: var(--font-europa-grotesk);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 8px 32px rgba(251, 191, 36, 0.3),
    0 1px 0 rgba(255, 255, 255, 0.2) inset;
  overflow: hidden;
  margin-bottom: 16px;
}

.roi-section__cta-btn:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 
    0 12px 40px rgba(251, 191, 36, 0.4),
    0 1px 0 rgba(255, 255, 255, 0.3) inset;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.roi-section__cta-btn:active {
  transform: translateY(0) scale(1);
}

.roi-section__cta-text {
  position: relative;
  z-index: 2;
}

.roi-section__cta-icon {
  width: 20px;
  height: 20px;
  position: relative;
  z-index: 2;
  transition: transform 0.3s ease;
}

.roi-section__cta-btn:hover .roi-section__cta-icon {
  transform: translateX(4px);
}

.roi-section__cta-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.6s ease;
}

.roi-section__cta-btn:hover .roi-section__cta-ripple {
  width: 300px;
  height: 300px;
}

.roi-section__cta-subtitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  font-weight: 400;
}

/* Animations */
@keyframes statReveal {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .roi-section {
    padding: 80px 0;
  }
  
  .roi-section__container {
    padding: 0 16px;
  }
  
  .roi-section__content {
    margin-bottom: 60px;
  }
  
  .roi-section__header {
    margin-bottom: 40px;
  }
  
  .roi-section__stats {
    gap: 24px;
  }
  
  .roi-section__stat {
    padding: 24px 20px;
    gap: 16px;
    flex-direction: column;
    text-align: center;
  }
  
  .roi-section__stat-icon {
    font-size: 40px;
    width: 64px;
    height: 64px;
    margin: 0 auto;
  }
  
  .roi-section__stat-content {
    padding-top: 0;
  }
  
  .roi-section__cta-title {
    margin-bottom: 32px;
  }
  
  .roi-section__cta-btn {
    padding: 16px 32px;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .roi-section {
    padding: 60px 0;
  }
  
  .roi-section__container {
    padding: 0 12px;
  }
  
  .roi-section__content {
    margin-bottom: 40px;
  }
  
  .roi-section__header {
    margin-bottom: 32px;
  }
  
  .roi-section__stats {
    gap: 20px;
  }
  
  .roi-section__stat {
    padding: 20px 16px;
  }
  
  .roi-section__stat-icon {
    font-size: 36px;
    width: 56px;
    height: 56px;
  }
  
  .roi-section__cta-title {
    margin-bottom: 24px;
  }
  
  .roi-section__cta-btn {
    padding: 14px 28px;
    font-size: 15px;
    gap: 8px;
  }
  
  .roi-section__cta-icon {
    width: 16px;
    height: 16px;
  }
}
