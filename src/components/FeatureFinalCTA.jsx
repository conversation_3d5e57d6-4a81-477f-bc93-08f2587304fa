import { useNavigate } from 'react-router-dom';
import './FeatureFinalCTA.css';

const FeatureFinalCTA = ({
  title = "Ready to Transform Your Home Care Agency?",
  subtitle = "Join thousands of agencies already using Telescope to streamline operations and improve care quality.",
  primaryButton = {
    text: "Book Your Demo",
    href: "#"
  },
  secondaryButton = {
    text: "Start Free Trial",
    href: "#"
  }
}) => {
  const navigate = useNavigate();

  const handleButtonClick = (href) => {
    if (href === "#demo" || href === "#" || href.includes("demo")) {
      navigate('/contact');
    } else if (href === "#trial" || href.includes("trial")) {
      navigate('/contact');
    } else if (href.startsWith('/')) {
      navigate(href);
    } else {
      // For external links or other hrefs, use default behavior
      window.open(href, '_blank');
    }
  };
  return (
    <section className="feature-final-cta">
      <div className="feature-final-cta__container">
        <div className="feature-final-cta__grid">
          {/* Left side - Content */}
          <div className="feature-final-cta__content">
            <h2 className="feature-final-cta__title">{title}</h2>
            <p className="feature-final-cta__subtitle">{subtitle}</p>

            <div className="feature-final-cta__stats">
              <div className="feature-final-cta__stat">
                <span className="feature-final-cta__stat-number">50,000+</span>
                <span className="feature-final-cta__stat-label">Agencies Trust Us</span>
              </div>
              <div className="feature-final-cta__stat">
                <span className="feature-final-cta__stat-number">99.9%</span>
                <span className="feature-final-cta__stat-label">Uptime</span>
              </div>
              <div className="feature-final-cta__stat">
                <span className="feature-final-cta__stat-number">24/7</span>
                <span className="feature-final-cta__stat-label">Support</span>
              </div>
            </div>
          </div>

          {/* Right side - CTA Card */}
          <div className="feature-final-cta__card">
            <div className="feature-final-cta__card-header">
              <h3 className="feature-final-cta__card-title">Get Started Today</h3>
              <p className="feature-final-cta__card-subtitle">Choose your path to success</p>
            </div>

            <div className="feature-final-cta__buttons">
              <button
                onClick={() => handleButtonClick(primaryButton.href)}
                className="feature-final-cta__primary-btn"
              >
                <svg className="feature-final-cta__btn-icon" viewBox="0 0 24 24" fill="none">
                  <path d="M8 2v4M16 2v4M3 10h18M5 4h14a2 2 0 012 2v14a2 2 0 01-2 2H5a2 2 0 01-2-2V6a2 2 0 012-2z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                {primaryButton.text}
              </button>

              <button
                onClick={() => handleButtonClick(secondaryButton.href)}
                className="feature-final-cta__secondary-btn"
              >
                <svg className="feature-final-cta__btn-icon" viewBox="0 0 24 24" fill="none">
                  <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                {secondaryButton.text}
              </button>
            </div>

            <div className="feature-final-cta__features">
              <div className="feature-final-cta__feature">
                <svg className="feature-final-cta__feature-icon" viewBox="0 0 24 24" fill="none">
                  <path d="M20 6L9 17l-5-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span>No setup fees</span>
              </div>
              <div className="feature-final-cta__feature">
                <svg className="feature-final-cta__feature-icon" viewBox="0 0 24 24" fill="none">
                  <path d="M20 6L9 17l-5-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span>Cancel anytime</span>
              </div>
              <div className="feature-final-cta__feature">
                <svg className="feature-final-cta__feature-icon" viewBox="0 0 24 24" fill="none">
                  <path d="M20 6L9 17l-5-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span>Free migration</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeatureFinalCTA;
