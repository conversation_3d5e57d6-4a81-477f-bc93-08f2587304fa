/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

/* Features Page Container */
.features-page {
  min-height: 100vh;
  background: #ffffff;
  font-family: "Plus Jakarta Sans", sans-serif;
}

/* Ensure smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Fix any potential layout issues */
* {
  box-sizing: border-box;
}

/* Ensure proper spacing between sections */
.features-page > * {
  position: relative;
  z-index: 1;
}

/* Fix potential overflow issues */
.features-page {
  overflow-x: hidden;
}

/* Features Page Styles */
.features-page {
  min-height: 100vh;
  background: #ffffff;
  font-family: "Plus Jakarta Sans", sans-serif;
  overflow-x: hidden;
}

/* Features Navigation */
.features-nav {
  background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
  padding: 160px 0 20px 0; /* Increased top padding to account for sticky nav */
  border-bottom: 1px solid rgba(0, 106, 193, 0.1);
  position: relative;
}

.features-nav__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.features-nav__header {
  text-align: center;
  margin-bottom: 40px;
}

.features-nav__title {
  font-size: clamp(2.5rem, 5vw, 3.5rem);
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 16px 0;
  padding-top: 40px;
  line-height: 1.2;
  letter-spacing: -0.02em;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.features-nav__subtitle {
  font-size: 1.25rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
  max-width: 600px;
  margin: 0 auto;
  font-family: "Plus Jakarta Sans", sans-serif;
}

/* Sticky Tabs Wrapper */
.features-nav__tabs-sticky {
  position: fixed;
  top: 72px; /* Just below header (header height is 72px) */
  left: 0;
  right: 0;
  z-index: 999; /* Just below header (1000) but above content */
  background: rgba(248, 250, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  padding: 20px 0;
  border-bottom: 1px solid rgba(0, 106, 193, 0.2);
  box-shadow: 0 4px 20px rgba(0, 106, 193, 0.1);
  transition: all 0.3s ease;
  width: 100%;
}

.features-nav__tabs-sticky.scrolled {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: 0 4px 30px rgba(0, 106, 193, 0.15);
  border-bottom: 1px solid rgba(0, 106, 193, 0.3);
}

/* Horizontal Features Tabs */
.features-nav__tabs {
  background: white;
  border-radius: 12px;
  padding: 8px;
  box-shadow: 0 4px 20px rgba(0, 106, 193, 0.08);
  border: 1px solid rgba(0, 106, 193, 0.1);
  margin: 0 auto;
  max-width: 1200px;
  position: relative;
  overflow: hidden;
}

.features-nav__tabs-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.features-nav__scroll-btn {
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  border: none;
  border-radius: 8px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
  color: white;
}

.features-nav__scroll-btn:hover {
  background: linear-gradient(135deg, #1a7dd1 0%, #3490e1 100%);
  transform: scale(1.05);
}

.features-nav__scroll-btn:disabled {
  background: #e5e7eb;
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.features-nav__scroll-btn svg {
  width: 16px;
  height: 16px;
}

.features-nav__tabs-container {
  display: flex;
  gap: 4px;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  scroll-behavior: smooth;
  flex: 1;
  padding: 0 4px;
}

.features-nav__tabs-container::-webkit-scrollbar {
  display: none;
}

.features-nav__tab {
  background: transparent;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 0.9rem;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  position: relative;
  font-family: "Plus Jakarta Sans", sans-serif;
  letter-spacing: -0.01em;
  min-width: fit-content;
}

.features-nav__tab:hover {
  color: #006ac1;
  background: rgba(0, 106, 193, 0.05);
}

.features-nav__tab--active {
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(0, 106, 193, 0.2);
}

.features-nav__tab--active:hover {
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  color: white;
}

/* Feature Hero Section */
.feature-hero {
  padding: 40px 0 60px 0;
  background: white;
}

.feature-hero__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.feature-hero__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

/* Left Side - Text Content */
.feature-hero__text {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.feature-hero__badge {
  display: inline-flex;
  align-self: flex-start;
}

.feature-hero__badge-text {
  background: linear-gradient(135deg, rgba(0, 106, 193, 0.1) 0%, rgba(0, 106, 193, 0.05) 100%);
  color: #006ac1;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid rgba(0, 106, 193, 0.1);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-hero__title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.2;
  letter-spacing: -0.02em;
  margin: 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-hero__subtitle {
  font-size: 1.25rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-hero__benefits {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feature-hero__benefit {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  font-size: 1rem;
  color: #374151;
  line-height: 1.5;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-hero__check-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.feature-hero__cta {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.feature-hero__cta-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 16px 32px;
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  color: white;
  border: none;
  border-radius: 50px;
  font-size: 16px;
  font-weight: 500;
  font-family: "Plus Jakarta Sans", sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  box-shadow: 0 4px 16px rgba(0, 106, 193, 0.2);
}

.feature-hero__cta-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 106, 193, 0.3);
  background: linear-gradient(135deg, #1a7dd1 0%, #3490e1 100%);
}

.feature-hero__cta-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;
}

.feature-hero__cta-btn:hover .feature-hero__cta-icon {
  transform: translateX(2px);
}

.feature-hero__demo-btn {
  padding: 16px 32px;
  background: transparent;
  color: #006ac1;
  border: 2px solid rgba(0, 106, 193, 0.2);
  border-radius: 50px;
  font-size: 16px;
  font-weight: 500;
  font-family: "Plus Jakarta Sans", sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
}

.feature-hero__demo-btn:hover {
  background: rgba(0, 106, 193, 0.05);
  border-color: rgba(0, 106, 193, 0.3);
  transform: translateY(-1px);
}

/* Testimonial Section */
.feature-hero__testimonial {
  background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
  border-radius: 16px;
  padding: 24px;
  border-left: 4px solid #006ac1;
  margin-top: 8px;
}

.feature-hero__testimonial-quote {
  font-size: 1.125rem;
  color: #374151;
  line-height: 1.6;
  font-style: italic;
  margin-bottom: 12px;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-hero__testimonial-author {
  font-size: 0.9rem;
  color: #6b7280;
  font-weight: 500;
  font-family: "Plus Jakarta Sans", sans-serif;
}

/* How It Works Section */
.feature-hero__how-it-works {
  background: white;
  border: 1px solid rgba(0, 106, 193, 0.1);
  border-radius: 16px;
  padding: 24px;
  margin-top: 8px;
}

.feature-hero__how-it-works-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 16px 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-hero__how-it-works-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.feature-hero__how-it-works-step {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  font-size: 1rem;
  color: #374151;
  line-height: 1.5;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-hero__step-number {
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 600;
  flex-shrink: 0;
  margin-top: 2px;
}

/* Right Side - Images */
.feature-hero__images {
  display: flex;
  flex-direction: column;
  gap: 24px;
  position: relative;
}

/* Web Interface Container */
.feature-hero__web-container {
  background: #f5f5f7;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.feature-hero__mac-header {
  background: #e5e5e7;
  padding: 12px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.feature-hero__mac-buttons {
  display: flex;
  gap: 8px;
}

.feature-hero__mac-button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.feature-hero__mac-button:hover {
  opacity: 0.8;
}

.feature-hero__mac-button--close {
  background: #ff5f57;
}

.feature-hero__mac-button--minimize {
  background: #ffbd2e;
}

.feature-hero__mac-button--maximize {
  background: #28ca42;
}

.feature-hero__web-image {
  background: white;
  padding: 0;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-hero__web-img {
  width: 100%;
  height: auto;
  display: block;
}

/* Mobile Interface Container */
.feature-hero__mobile-container {
  align-self: flex-end;
  max-width: 200px;
  position: relative;
  z-index: 2;
}

.feature-hero__mobile-frame {
  background: #1a1a1a;
  border-radius: 24px;
  padding: 8px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.feature-hero__mobile-img {
  width: 100%;
  height: auto;
  border-radius: 16px;
  display: block;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .features-nav__tabs-sticky {
    top: 72px; /* Below header on tablet (same height) */
    padding: 16px 0;
  }

  .features-nav__tabs-sticky .features-nav__container {
    padding: 0 12px;
  }

  .features-nav__scroll-btn {
    width: 32px;
    height: 32px;
  }

  .features-nav__scroll-btn svg {
    width: 14px;
    height: 14px;
  }

  .features-nav__tab {
    padding: 10px 16px;
    font-size: 0.85rem;
  }

  .feature-hero__content {
    gap: 60px;
  }

  .feature-hero__mobile-container {
    max-width: 180px;
  }
}

@media (max-width: 768px) {
  .features-nav {
    padding: 140px 0 20px 0; /* Increased for mobile sticky nav */
  }

  .features-nav__container {
    padding: 0 20px;
  }

  .features-nav__header {
    margin-bottom: 30px;
  }

  .features-nav__title {
    padding-top: 30px;
  }

  .features-nav__tabs-sticky {
    top: 72px; /* Below mobile header (same height) */
    padding: 12px 0;
  }

  .features-nav__tabs-sticky .features-nav__container {
    padding: 0 20px;
  }

  .features-nav__tabs {
    border-radius: 8px;
  }

  .features-nav__tabs-wrapper {
    gap: 6px;
  }

  .features-nav__scroll-btn {
    width: 30px;
    height: 30px;
  }

  .features-nav__scroll-btn svg {
    width: 12px;
    height: 12px;
  }

  .features-nav__tab {
    padding: 8px 12px;
    font-size: 0.8rem;
    white-space: nowrap;
  }

  .feature-hero {
    padding: 60px 0;
  }

  .feature-hero__container {
    padding: 0 20px;
  }

  .feature-hero__content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .feature-hero__images {
    order: -1;
  }

  .feature-hero__mobile-container {
    max-width: 160px;
    align-self: center;
  }

  .feature-hero__cta {
    flex-direction: column;
    align-items: stretch;
  }

  .feature-hero__cta-btn,
  .feature-hero__demo-btn {
    text-align: center;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .features-nav {
    padding: 120px 0 15px 0; /* Increased for small mobile sticky nav */
  }

  .features-nav__container {
    padding: 0 16px;
  }

  .features-nav__header {
    margin-bottom: 25px;
  }

  .features-nav__title {
    padding-top: 20px;
  }

  .features-nav__tabs-sticky {
    top: 72px; /* Below mobile header (same height) */
    padding: 10px 0;
  }

  .features-nav__tabs-sticky .features-nav__container {
    padding: 0 16px;
  }

  .features-nav__tabs {
    border-radius: 6px;
    padding: 4px;
  }

  .features-nav__tabs-wrapper {
    gap: 4px;
  }

  .features-nav__scroll-btn {
    width: 28px;
    height: 28px;
  }

  .features-nav__scroll-btn svg {
    width: 10px;
    height: 10px;
  }

  .features-nav__tab {
    padding: 6px 10px;
    font-size: 0.7rem;
    min-width: auto;
    white-space: nowrap;
  }

  .feature-hero {
    padding: 40px 0;
  }

  .feature-hero__container {
    padding: 0 16px;
  }

  .feature-hero__content {
    gap: 30px;
  }

  .feature-hero__text {
    gap: 20px;
  }

  .feature-hero__benefits {
    gap: 12px;
  }

  .feature-hero__benefit {
    font-size: 0.9rem;
  }

  .feature-hero__mobile-container {
    max-width: 140px;
  }

  .feature-hero__cta-btn,
  .feature-hero__demo-btn {
    padding: 14px 24px;
    font-size: 15px;
  }

  .feature-hero__web-image {
    min-height: 200px;
  }

  .feature-hero__testimonial {
    padding: 20px;
  }

  .feature-hero__testimonial-quote {
    font-size: 1rem;
  }

  .feature-hero__how-it-works {
    padding: 20px;
  }

  .feature-hero__how-it-works-title {
    font-size: 1.125rem;
  }

  .feature-hero__how-it-works-step {
    font-size: 0.9rem;
  }
}

/* Animation for tab transitions */
.feature-hero__content {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Feature Trusted By Section */
.feature-trusted-by {
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
  padding: 80px 0;
  border-bottom: 1px solid rgba(0, 106, 193, 0.1);
}

.feature-trusted-by__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.feature-trusted-by__header {
  text-align: center;
  margin-bottom: 60px;
}

.feature-trusted-by__title {
  font-size: clamp(1.75rem, 4vw, 2.5rem);
  font-weight: 500;
  color: #1a1a1a;
  margin: 0;
  line-height: 1.3;
  letter-spacing: -0.01em;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-trusted-by__marquee {
  overflow: hidden;
  position: relative;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 20px 0;
  box-shadow: 0 4px 20px rgba(0, 106, 193, 0.08);
}

.feature-trusted-by__marquee-track {
  display: flex;
  gap: 60px;
  animation: marqueeScroll 40s linear infinite;
  width: max-content;
}

.feature-trusted-by__company {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  border: 1px solid rgba(0, 106, 193, 0.1);
  min-width: 200px;
  transition: all 0.3s ease;
}

.feature-trusted-by__company:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 106, 193, 0.15);
}

.feature-trusted-by__company-logo {
  font-size: 24px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #006ac1 0%, #0056a3 100%);
  border-radius: 8px;
  color: white;
  flex-shrink: 0;
}

.feature-trusted-by__company-name {
  font-size: 0.95rem;
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
  font-family: "Plus Jakarta Sans", sans-serif;
}

@keyframes marqueeScroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* Feature Problems Section */
.feature-problems {
  background: #ffffff;
  padding: 100px 0;
  position: relative;
}

.feature-problems__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.feature-problems__zigzag {
  display: flex;
  flex-direction: column;
  gap: 120px;
}

.feature-problems__item {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  position: relative;
}

.feature-problems__item--reverse {
  direction: rtl;
}

.feature-problems__item--reverse .feature-problems__content {
  direction: ltr;
}

.feature-problems__content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.feature-problems__problem {
  font-size: 1.125rem;
  color: #ef4444;
  font-weight: 500;
  font-family: "Plus Jakarta Sans", sans-serif;
  line-height: 1.5;
}

.feature-problems__solution {
  font-size: clamp(1.75rem, 3vw, 2.25rem);
  font-weight: 500;
  color: #1a1a1a;
  margin: 0;
  line-height: 1.3;
  letter-spacing: -0.01em;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-problems__checklist {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feature-problems__check-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  font-size: 1rem;
  color: #374151;
  line-height: 1.6;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-problems__check-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.feature-problems__cta {
  background: linear-gradient(135deg, #006ac1 0%, #0056a3 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 500;
  font-family: "Plus Jakarta Sans", sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  align-self: flex-start;
  box-shadow: 0 4px 15px rgba(0, 106, 193, 0.3);
}

.feature-problems__cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 106, 193, 0.4);
}

.feature-problems__cta-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;
}

.feature-problems__cta:hover .feature-problems__cta-icon {
  transform: translateX(4px);
}

.feature-problems__image {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.feature-problems__image-container {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  background: white;
  transition: all 0.3s ease;
  width: 100%;
  max-width: 600px;
}

.feature-problems__image-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* Mac-style Header */
.feature-problems__mac-header {
  position: relative;
  background: linear-gradient(180deg, #f6f6f6 0%, #e8e8e8 100%);
  border-bottom: 1px solid #d1d1d1;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-radius: 20px 20px 0 0;
}

.feature-problems__mac-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.feature-problems__mac-button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 0.5px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.feature-problems__mac-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.feature-problems__mac-button--close {
  background: linear-gradient(135deg, #ff5f57 0%, #ff4444 100%);
}

.feature-problems__mac-button--close:hover::before {
  content: '×';
  color: #8b0000;
  font-size: 10px;
  font-weight: bold;
  opacity: 1;
}

.feature-problems__mac-button--minimize {
  background: linear-gradient(135deg, #ffbd2e 0%, #ffaa00 100%);
}

.feature-problems__mac-button--minimize:hover::before {
  content: '−';
  color: #8b4500;
  font-size: 10px;
  font-weight: bold;
  opacity: 1;
}

.feature-problems__mac-button--maximize {
  background: linear-gradient(135deg, #28ca42 0%, #20a034 100%);
}

.feature-problems__mac-button--maximize:hover::before {
  content: '+';
  color: #0d4f1c;
  font-size: 10px;
  font-weight: bold;
  opacity: 1;
}

.feature-problems__mac-button:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.feature-problems__img {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 0 0 16px 16px;
  transition: transform 0.3s ease;
}

/* Responsive Design for Feature Sections */
@media (max-width: 768px) {
  /* Feature Trusted By Mobile */
  .feature-trusted-by {
    padding: 60px 0;
  }

  .feature-trusted-by__container {
    padding: 0 16px;
  }

  .feature-trusted-by__header {
    margin-bottom: 40px;
  }

  .feature-trusted-by__marquee-track {
    gap: 40px;
    animation-duration: 30s;
  }

  .feature-trusted-by__company {
    padding: 12px 20px;
    min-width: 180px;
  }

  .feature-trusted-by__company-logo {
    font-size: 20px;
    width: 28px;
    height: 28px;
  }

  .feature-trusted-by__company-name {
    font-size: 0.875rem;
  }

  /* Feature Problems Mobile */
  .feature-problems {
    padding: 80px 0;
  }

  .feature-problems__container {
    padding: 0 16px;
  }

  .feature-problems__zigzag {
    gap: 80px;
  }

  .feature-problems__item {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .feature-problems__item--reverse {
    direction: ltr;
  }

  .feature-problems__content {
    gap: 20px;
  }

  .feature-problems__problem {
    font-size: 1rem;
  }

  .feature-problems__checklist {
    gap: 12px;
  }

  .feature-problems__check-item {
    font-size: 0.9rem;
  }

  .feature-problems__cta {
    padding: 14px 28px;
    font-size: 0.9rem;
    align-self: center;
  }

  .feature-problems__image {
    order: -1;
  }

  .feature-problems__image-container {
    border-radius: 16px;
  }

  .feature-problems__mac-header {
    padding: 10px 12px;
    border-radius: 16px 16px 0 0;
  }

  .feature-problems__mac-button {
    width: 10px;
    height: 10px;
  }

  .feature-problems__mac-buttons {
    gap: 6px;
  }

  .feature-problems__img {
    border-radius: 0 0 12px 12px;
  }
}

/* Feature Testimonial Section */
.feature-testimonial {
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
  padding: 100px 0;
  position: relative;
}

.feature-testimonial__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.feature-testimonial__content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 80px;
  align-items: center;
}

.feature-testimonial__text {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.feature-testimonial__quote-icon {
  width: 48px;
  height: 48px;
}

.feature-testimonial__quote-svg {
  width: 100%;
  height: 100%;
}

.feature-testimonial__quote {
  font-size: clamp(1.5rem, 3vw, 2rem);
  font-weight: 400;
  color: #1a1a1a;
  line-height: 1.4;
  font-family: "Plus Jakarta Sans", sans-serif;
  font-style: italic;
  margin: 0;
}

.feature-testimonial__author {
  display: flex;
  align-items: center;
  gap: 16px;
}

.feature-testimonial__author-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.feature-testimonial__author-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a1a1a;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-testimonial__author-title {
  font-size: 1rem;
  font-weight: 500;
  color: #006ac1;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-testimonial__author-location {
  font-size: 0.9rem;
  color: #6b7280;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-testimonial__image {
  position: relative;
}

.feature-testimonial__image-container {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 106, 193, 0.15);
  background: linear-gradient(135deg, #006ac1 0%, #0056a3 100%);
  padding: 8px;
}

.feature-testimonial__img {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 16px;
  transition: transform 0.3s ease;
}

.feature-testimonial__image-decoration {
  position: absolute;
  top: -20px;
  right: -20px;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #006ac1 0%, #0056a3 100%);
  border-radius: 50%;
  opacity: 0.1;
  z-index: -1;
}

.feature-testimonial__image-decoration::before {
  content: '';
  position: absolute;
  top: -40px;
  left: -40px;
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #006ac1 0%, #0056a3 100%);
  border-radius: 50%;
  opacity: 0.05;
}

/* Responsive Design for Feature Testimonial */
@media (max-width: 768px) {
  .feature-testimonial {
    padding: 80px 0;
  }

  .feature-testimonial__container {
    padding: 0 16px;
  }

  .feature-testimonial__content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .feature-testimonial__text {
    gap: 24px;
  }

  .feature-testimonial__quote {
    font-size: 1.25rem;
  }

  .feature-testimonial__author {
    justify-content: center;
  }

  .feature-testimonial__image {
    max-width: 300px;
    margin: 0 auto;
  }
}

/* Feature Parallax Container - Full width breakout without horizontal scroll */
.feature-parallax-container {
  position: relative;
  font-family: "Plus Jakarta Sans", sans-serif;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  margin-right: calc(-50vw + 50%);
  padding: 0;
}

/* Parallax Background Section */
.feature-parallax-section {
  position: relative;
  height: 100vh;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: 0;
  padding: 0;
}

/* Background that moves with parallax */
.feature-parallax-background {
  position: absolute;
  top: -20%;
  left: 0;
  right: 0;
  width: 100%;
  height: 120%;
  will-change: transform;
  z-index: 1;
  margin: 0;
  padding: 0;
}

/* Background with gradient */
.feature-parallax-bg {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #006ac1 0%, #0056a3 100%);
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  position: relative;
  padding-top: 15vh;
}

.feature-parallax-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  z-index: -1;
}

.feature-not-done__tile {
  background: transparent;
  padding: 40px 60px;
  position: relative;
  z-index: 2;
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.feature-not-done__tile--visible {
  animation: fadeInUp 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}



.feature-not-done__text {
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: 800;
  color: white;
  margin: 0;
  text-align: center;
  font-style: italic;
  font-family: "Plus Jakarta Sans", sans-serif;
  letter-spacing: -0.04em;
  text-shadow:
    0 4px 20px rgba(0, 0, 0, 0.6),
    0 2px 10px rgba(0, 0, 0, 0.4);
  position: relative;
  z-index: 2;
  line-height: 0.9;
}

/* Professional minimal fadeInUp animation */
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* How It Works Section - Slides over parallax (following home page pattern) */
.feature-how-it-works {
  position: relative;
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
  z-index: 10;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40px 40px 0 0;
  width: 100%;
  margin: 0;
  padding: 100px 0;
  top: -40px;
  box-shadow: 0 -20px 40px rgba(0, 0, 0, 0.1);
}

.feature-how-it-works__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.feature-how-it-works__header {
  text-align: center;
  margin-bottom: 80px;
}

.feature-how-it-works__title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 500;
  color: #1a1a1a;
  margin: 0 0 16px 0;
  line-height: 1.2;
  letter-spacing: -0.02em;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-how-it-works__subtitle {
  font-size: 1.25rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-how-it-works__steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  margin-bottom: 80px;
}

.feature-how-it-works__step {
  display: flex;
  gap: 24px;
  padding: 32px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  border: 1px solid rgba(0, 106, 193, 0.1);
  transition: all 0.3s ease;
  position: relative;
}

.feature-how-it-works__step:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 106, 193, 0.15);
  border-color: rgba(0, 106, 193, 0.2);
}

.feature-how-it-works__step-number {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #006ac1 0%, #0056a3 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  font-weight: 600;
  font-family: "Plus Jakarta Sans", sans-serif;
  flex-shrink: 0;
}

.feature-how-it-works__step-content {
  flex: 1;
}

.feature-how-it-works__step-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 12px 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-how-it-works__step-description {
  font-size: 1rem;
  color: #374151;
  line-height: 1.6;
  margin: 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-how-it-works__cta {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  padding: 48px;
  text-align: center;
  border: 1px solid rgba(0, 106, 193, 0.1);
  box-shadow: 0 10px 30px rgba(0, 106, 193, 0.1);
}

.feature-how-it-works__cta-title {
  font-size: clamp(1.5rem, 3vw, 2rem);
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 16px 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-how-it-works__cta-description {
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0 0 32px 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-how-it-works__cta-btn {
  background: linear-gradient(135deg, #006ac1 0%, #0056a3 100%);
  color: white;
  border: none;
  padding: 18px 36px;
  border-radius: 12px;
  font-size: 1.125rem;
  font-weight: 500;
  font-family: "Plus Jakarta Sans", sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 8px 25px rgba(0, 106, 193, 0.3);
}

.feature-how-it-works__cta-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(0, 106, 193, 0.4);
}

.feature-how-it-works__cta-icon {
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
}

.feature-how-it-works__cta-btn:hover .feature-how-it-works__cta-icon {
  transform: translateX(4px);
}

/* Responsive Design for New Sections */
@media (max-width: 768px) {
  /* Feature Parallax Mobile */
  .feature-parallax-section {
    height: 80vh;
    min-height: 500px;
  }

  .feature-parallax-bg {
    padding-top: 10vh;
  }

  .feature-not-done__tile {
    padding: 30px 40px;
  }

  .feature-not-done__text {
    font-size: clamp(2.5rem, 12vw, 4rem);
    font-weight: 800;
    line-height: 0.85;
  }

  /* How It Works Mobile */
  .feature-how-it-works {
    padding: 80px 0;
    border-radius: 30px 30px 0 0;
  }

  .feature-how-it-works__container {
    padding: 0 16px;
  }

  .feature-how-it-works__header {
    margin-bottom: 60px;
  }

  .feature-how-it-works__steps {
    grid-template-columns: 1fr;
    gap: 24px;
    margin-bottom: 60px;
  }

  .feature-how-it-works__step {
    padding: 24px;
    gap: 16px;
  }

  .feature-how-it-works__step-number {
    width: 40px;
    height: 40px;
    font-size: 1.125rem;
  }

  .feature-how-it-works__step-title {
    font-size: 1.125rem;
  }

  .feature-how-it-works__step-description {
    font-size: 0.9rem;
  }

  .feature-how-it-works__cta {
    padding: 32px 24px;
  }

  .feature-how-it-works__cta-btn {
    padding: 16px 28px;
    font-size: 1rem;
  }
}

/* Feature Why Use Section */
.feature-why-use {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #f8faff 100%);
  padding: 100px 0 50px 0;
  position: relative;
  overflow: hidden;
  margin-top: -50px;
  padding-top: 150px;
  clip-path: polygon(0 50px, 100% 0, 100% 100%, 0 100%);
}

.feature-why-use::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(0, 106, 193, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(0, 106, 193, 0.03) 0%, transparent 50%);
  z-index: 0;
}

.feature-why-use__container {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.feature-why-use__zigzag {
  display: flex;
  flex-direction: column;
  gap: 120px;
}

.feature-why-use__item:nth-child(2) {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 106, 193, 0.1);
}

.feature-why-use__item:nth-child(2):hover {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 106, 193, 0.15);
}

.feature-why-use__item {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  position: relative;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 24px;
  padding: 60px;
  box-shadow:
    0 20px 40px rgba(0, 106, 193, 0.08),
    0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.feature-why-use__item:hover {
  transform: translateY(-5px);
  box-shadow:
    0 30px 60px rgba(0, 106, 193, 0.12),
    0 1px 0 rgba(255, 255, 255, 0.9);
}

.feature-why-use__item--reverse {
  direction: rtl;
}

.feature-why-use__item--reverse .feature-why-use__content {
  direction: ltr;
}

.feature-why-use__content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.feature-why-use__title {
  font-size: clamp(1.75rem, 3vw, 2.25rem);
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  line-height: 1.3;
  letter-spacing: -0.01em;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-why-use__subtitle {
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-why-use__benefits {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feature-why-use__benefit {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  font-size: 1rem;
  color: #374151;
  line-height: 1.6;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-why-use__check-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.feature-why-use__cta {
  background: linear-gradient(135deg, #006ac1 0%, #0056a3 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 500;
  font-family: "Plus Jakarta Sans", sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  align-self: flex-start;
  box-shadow: 0 4px 15px rgba(0, 106, 193, 0.3);
}

.feature-why-use__cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 106, 193, 0.4);
}

.feature-why-use__cta-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;
}

.feature-why-use__cta:hover .feature-why-use__cta-icon {
  transform: translateX(4px);
}

.feature-why-use__image {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.feature-why-use__image-container {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  background: white;
  transition: all 0.3s ease;
  width: 100%;
  max-width: 600px;
}

.feature-why-use__image-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* Mac-style Header */
.feature-why-use__mac-header {
  position: relative;
  background: linear-gradient(180deg, #f6f6f6 0%, #e8e8e8 100%);
  border-bottom: 1px solid #d1d1d1;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-radius: 20px 20px 0 0;
}

.feature-why-use__mac-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.feature-why-use__mac-button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 0.5px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.feature-why-use__mac-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.feature-why-use__mac-button--close {
  background: linear-gradient(135deg, #ff5f57 0%, #ff4444 100%);
}

.feature-why-use__mac-button--close:hover::before {
  content: '×';
  color: #8b0000;
  font-size: 10px;
  font-weight: bold;
  opacity: 1;
}

.feature-why-use__mac-button--minimize {
  background: linear-gradient(135deg, #ffbd2e 0%, #ffaa00 100%);
}

.feature-why-use__mac-button--minimize:hover::before {
  content: '−';
  color: #8b4500;
  font-size: 10px;
  font-weight: bold;
  opacity: 1;
}

.feature-why-use__mac-button--maximize {
  background: linear-gradient(135deg, #28ca42 0%, #20a034 100%);
}

.feature-why-use__mac-button--maximize:hover::before {
  content: '+';
  color: #0d4f1c;
  font-size: 10px;
  font-weight: bold;
  opacity: 1;
}

.feature-why-use__mac-button:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.feature-why-use__img {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 0 0 16px 16px;
  transition: transform 0.2s ease;
  will-change: transform;
  transform: translateZ(0);
}

/* Manager Benefits Parallax Section */
.feature-manager-parallax-container {
  position: relative;
  font-family: "Plus Jakarta Sans", sans-serif;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  margin-right: calc(-50vw + 50%);
  padding: 0;
  margin-top: -50px;
}

.feature-manager-parallax-section {
  position: relative;
  height: 120vh;
  min-height: 650px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: 0;
  padding: 50px 0;
  background: linear-gradient(135deg, #f8faff 0%, #e6f3ff 100%);
}

.feature-manager-parallax-background {
  position: absolute;
  top: -20%;
  left: 0;
  right: 0;
  width: 100%;
  height: 120%;
  will-change: transform;
  z-index: 1;
  margin: 0;
  padding: 0;
}

.feature-manager-parallax-bg {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f8faff 0%, #e6f3ff 100%);
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  position: relative;
  padding-top: 15vh;
}

.feature-manager-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 80px;
  align-items: center;
  max-width: 1200px;
  width: 100%;
  padding: 0 24px;
  z-index: 2;
  position: relative;
  top: 20%
}

.feature-manager-text {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.feature-manager-title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  line-height: 1.2;
  letter-spacing: -0.02em;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-manager-subtitle {
  font-size: 1.25rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-manager-cta {
  background: linear-gradient(135deg, #006ac1 0%, #0056a3 100%);
  color: white;
  border: none;
  padding: 18px 36px;
  border-radius: 12px;
  font-size: 1.125rem;
  font-weight: 500;
  font-family: "Plus Jakarta Sans", sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 12px;
  align-self: flex-start;
  box-shadow: 0 8px 25px rgba(0, 106, 193, 0.3);
}

.feature-manager-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(0, 106, 193, 0.4);
}

.feature-manager-cta-icon {
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
}

.feature-manager-cta:hover .feature-manager-cta-icon {
  transform: translateX(4px);
}

.feature-manager-icons {
  display: flex;
  flex-direction: column;
  gap: 40px;
  align-items: center;
  opacity: 0;
  transform: translateX(50px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.feature-manager-icons--visible {
  opacity: 1;
  transform: translateX(0);
}

.feature-manager-icon {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 30px rgba(0, 106, 193, 0.2);
  transition: all 0.3s ease;
}

.feature-manager-icon--1 {
  animation-delay: 0.2s;
}

.feature-manager-icon--2 {
  animation-delay: 0.4s;
}

.feature-manager-icons--visible .feature-manager-icon {
  animation: iconPulse 2s ease-in-out infinite;
}

.feature-manager-arrow {
  width: 40px;
  height: 40px;
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 10px 30px rgba(0, 106, 193, 0.2);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 15px 40px rgba(0, 106, 193, 0.3);
  }
}



/* Ratings Parallax Container */
.feature-ratings-parallax-container {
  position: relative;
  font-family: "Plus Jakarta Sans", sans-serif;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  margin-right: calc(-50vw + 50%);
  padding: 0;
}

.feature-ratings-parallax-section {
  position: relative;
  height: 150vh;
  min-height: 900px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: 0;
  padding: 0;
}

.feature-ratings-parallax-background {
  position: absolute;
  top: -20%;
  left: 0;
  right: 0;
  width: 100%;
  height: 120%;
  will-change: transform;
  z-index: 5;
  margin: 0;
  padding: 0;
}

.feature-ratings-parallax-bg {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #006ac1 0%, #0056a3 100%);
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.feature-ratings__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 10;
}

.feature-ratings__content {
  text-align: center;
}

.feature-ratings__header {
  margin-bottom: 60px;
}

.feature-ratings__title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 600;
  color: white;
  margin: 0 0 16px 0;
  line-height: 1.2;
  letter-spacing: -0.02em;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-ratings__subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin: 0;
  font-family: "Plus Jakarta Sans", sans-serif;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.feature-ratings__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.feature-ratings__card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 40px 30px;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.feature-ratings__card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.2);
}

.feature-ratings__stars {
  display: flex;
  justify-content: center;
  gap: 4px;
  margin-bottom: 16px;
}

.feature-ratings__star {
  width: 24px;
  height: 24px;
}

.feature-ratings__score {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 8px;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-ratings__platform {
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  margin-bottom: 8px;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-ratings__reviews {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  font-family: "Plus Jakarta Sans", sans-serif;
}

/* Final CTA Section */
.feature-final-cta {
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
  padding: 100px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-final-cta__container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 24px;
  text-align: center;
}

.feature-final-cta__content {
  display: flex;
  flex-direction: column;
  gap: 32px;
  align-items: center;
}

.feature-final-cta__title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  line-height: 1.2;
  letter-spacing: -0.02em;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-final-cta__subtitle {
  font-size: 1.25rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
  font-family: "Plus Jakarta Sans", sans-serif;
  max-width: 600px;
}

.feature-final-cta__buttons {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
  justify-content: center;
}

.feature-final-cta__primary {
  background: linear-gradient(135deg, #006ac1 0%, #0056a3 100%);
  color: white;
  border: none;
  padding: 20px 40px;
  border-radius: 12px;
  font-size: 1.125rem;
  font-weight: 600;
  font-family: "Plus Jakarta Sans", sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 8px 25px rgba(0, 106, 193, 0.3);
}

.feature-final-cta__primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(0, 106, 193, 0.4);
}

.feature-final-cta__secondary {
  background: transparent;
  color: #006ac1;
  border: 2px solid #006ac1;
  padding: 18px 38px;
  border-radius: 12px;
  font-size: 1.125rem;
  font-weight: 600;
  font-family: "Plus Jakarta Sans", sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
}

.feature-final-cta__secondary:hover {
  background: #006ac1;
  color: white;
  transform: translateY(-3px);
}

.feature-final-cta__icon {
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
}

.feature-final-cta__primary:hover .feature-final-cta__icon {
  transform: translateX(4px);
}

/* Everything Just Works Section */
.feature-everything-works {
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
  padding: 100px 0;
  position: relative;
}

.feature-everything-works__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.feature-everything-works__header {
  text-align: center;
  margin-bottom: 80px;
}

.feature-everything-works__title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 16px 0;
  line-height: 1.2;
  letter-spacing: -0.02em;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-everything-works__subtitle {
  font-size: 1.25rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
  font-family: "Plus Jakarta Sans", sans-serif;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.feature-everything-works__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
  margin-bottom: 80px;
}

.feature-everything-works__card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 106, 193, 0.1);
  border-radius: 16px;
  padding: 32px;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 106, 193, 0.05);
}

.feature-everything-works__card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 106, 193, 0.15);
  border-color: rgba(0, 106, 193, 0.2);
}

.feature-everything-works__icon {
  font-size: 3rem;
  margin-bottom: 16px;
  display: block;
}

.feature-everything-works__card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 12px 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-everything-works__card-description {
  font-size: 1rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-everything-works__cta {
  text-align: center;
  background: rgba(0, 106, 193, 0.05);
  border-radius: 20px;
  padding: 48px;
  border: 1px solid rgba(0, 106, 193, 0.1);
}

.feature-everything-works__cta-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 24px 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-everything-works__cta-btn {
  background: linear-gradient(135deg, #006ac1 0%, #0056a3 100%);
  color: white;
  border: none;
  padding: 18px 36px;
  border-radius: 12px;
  font-size: 1.125rem;
  font-weight: 500;
  font-family: "Plus Jakarta Sans", sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 8px 25px rgba(0, 106, 193, 0.3);
}

.feature-everything-works__cta-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(0, 106, 193, 0.4);
}

.feature-everything-works__cta-icon {
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
}

.feature-everything-works__cta-btn:hover .feature-everything-works__cta-icon {
  transform: translateX(4px);
}

/* FAQ Section */
.feature-faq {
  background: #ffffff;
  padding: 100px 0;
  position: relative;
}

.feature-faq__container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 24px;
}

.feature-faq__header {
  text-align: center;
  margin-bottom: 60px;
}

.feature-faq__title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 16px 0;
  line-height: 1.2;
  letter-spacing: -0.02em;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-faq__subtitle {
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-faq__list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feature-faq__item {
  background: rgba(248, 250, 255, 0.8);
  border: 1px solid rgba(0, 106, 193, 0.1);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.feature-faq__item:hover {
  border-color: rgba(0, 106, 193, 0.2);
  box-shadow: 0 4px 20px rgba(0, 106, 193, 0.1);
}

.feature-faq__question {
  width: 100%;
  background: none;
  border: none;
  padding: 24px;
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.125rem;
  font-weight: 500;
  color: #1a1a1a;
  font-family: "Plus Jakarta Sans", sans-serif;
  transition: all 0.3s ease;
}

.feature-faq__question:hover {
  background: rgba(0, 106, 193, 0.05);
}

.feature-faq__icon {
  width: 24px;
  height: 24px;
  color: #006ac1;
  transition: transform 0.3s ease;
  flex-shrink: 0;
  margin-left: 16px;
}

.feature-faq__answer {
  padding: 0 24px 24px 24px;
  border-top: 1px solid rgba(0, 106, 193, 0.1);
  background: rgba(255, 255, 255, 0.8);
}

.feature-faq__answer p {
  font-size: 1rem;
  color: #374151;
  line-height: 1.6;
  margin: 16px 0 0 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

/* Mobile Responsive Styles for New Sections */
@media (max-width: 768px) {
  /* Feature Why Use Mobile */
  .feature-why-use {
    padding: 80px 0;
    padding-top: 120px;
    margin-top: -30px;
    clip-path: polygon(0 30px, 100% 0, 100% 100%, 0 100%);
  }

  .feature-why-use__container {
    padding: 0 16px;
  }

  .feature-why-use__zigzag {
    gap: 60px;
  }

  .feature-why-use__item {
    grid-template-columns: 1fr;
    gap: 30px;
    text-align: center;
    padding: 40px 30px;
  }

  .feature-why-use__item--reverse {
    direction: ltr;
  }

  .feature-why-use__content {
    gap: 20px;
  }

  .feature-why-use__benefits {
    gap: 12px;
  }

  .feature-why-use__benefit {
    font-size: 0.9rem;
  }

  .feature-why-use__cta {
    padding: 14px 28px;
    font-size: 0.9rem;
    align-self: center;
  }

  .feature-why-use__image {
    order: -1;
  }

  .feature-why-use__image-container {
    border-radius: 16px;
  }

  .feature-why-use__mac-header {
    padding: 10px 12px;
    border-radius: 16px 16px 0 0;
  }

  .feature-why-use__mac-button {
    width: 10px;
    height: 10px;
  }

  .feature-why-use__mac-buttons {
    gap: 6px;
  }

  .feature-why-use__img {
    border-radius: 0 0 12px 12px;
  }

  /* Manager Parallax Mobile */
  .feature-manager-parallax-container {
    margin-top: -30px;
  }

  .feature-manager-parallax-section {
    height: 100vh;
    min-height: 500px;
    padding: 40px 0;
  }

  .feature-manager-parallax-bg {
    padding-top: 10vh;
  }

  .feature-manager-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .feature-manager-icons {
    flex-direction: row;
    gap: 20px;
    justify-content: center;
  }

  .feature-manager-icon {
    width: 60px;
    height: 60px;
  }

  .feature-manager-arrow {
    width: 30px;
    height: 30px;
  }

  /* Ratings Parallax Mobile */
  .feature-ratings-parallax-section {
    height: 120vh;
    min-height: 700px;
  }

  .feature-ratings__container {
    padding: 0 16px;
  }

  .feature-ratings__header {
    margin-bottom: 40px;
  }

  .feature-ratings__grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 24px;
  }

  .feature-ratings__card {
    padding: 30px 20px;
  }

  .feature-ratings__score {
    font-size: 2rem;
  }

  .feature-ratings__platform {
    font-size: 1.125rem;
  }

  /* Final CTA Mobile */
  .feature-final-cta {
    padding: 80px 0;
    margin-top: -60px;
  }

  .feature-final-cta__container {
    padding: 0 16px;
  }

  .feature-final-cta__content {
    gap: 24px;
  }

  .feature-final-cta__buttons {
    flex-direction: column;
    gap: 16px;
    width: 100%;
  }

  .feature-final-cta__primary,
  .feature-final-cta__secondary {
    width: 100%;
    justify-content: center;
  }

  /* Everything Just Works Mobile */
  .feature-everything-works {
    padding: 80px 0;
  }

  .feature-everything-works__container {
    padding: 0 16px;
  }

  .feature-everything-works__header {
    margin-bottom: 60px;
  }

  .feature-everything-works__grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 60px;
  }

  .feature-everything-works__card {
    padding: 24px;
  }

  .feature-everything-works__icon {
    font-size: 2.5rem;
    margin-bottom: 12px;
  }

  .feature-everything-works__card-title {
    font-size: 1.125rem;
  }

  .feature-everything-works__card-description {
    font-size: 0.9rem;
  }

  .feature-everything-works__cta {
    padding: 32px 24px;
  }

  .feature-everything-works__cta-btn {
    padding: 16px 28px;
    font-size: 1rem;
  }

  /* FAQ Mobile */
  .feature-faq {
    padding: 80px 0;
  }

  .feature-faq__container {
    padding: 0 16px;
  }

  .feature-faq__header {
    margin-bottom: 40px;
  }

  .feature-faq__question {
    padding: 20px;
    font-size: 1rem;
  }

  .feature-faq__icon {
    width: 20px;
    height: 20px;
    margin-left: 12px;
  }

  .feature-faq__answer {
    padding: 0 20px 20px 20px;
  }

  .feature-faq__answer p {
    font-size: 0.9rem;
  }
}
