/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

.feature-how-it-works {
  background: #ffffff;
  padding: 100px 0;
}

.feature-how-it-works__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.feature-how-it-works__header {
  text-align: center;
  margin-bottom: 80px;
}

.feature-how-it-works__title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 20px 0;
  line-height: 1.2;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-how-it-works__subtitle {
  font-size: 1.25rem;
  color: #6b7280;
  margin: 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-how-it-works__steps {
  display: flex;
  flex-direction: column;
  gap: 40px;
  margin-bottom: 80px;
}

.feature-how-it-works__step {
  display: flex;
  align-items: flex-start;
  gap: 24px;
  padding: 30px;
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
  border-radius: 16px;
  border: 1px solid rgba(0, 106, 193, 0.1);
  transition: all 0.3s ease;
}

.feature-how-it-works__step:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 106, 193, 0.1);
}

.feature-how-it-works__step-number {
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  color: white;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  font-weight: 600;
  flex-shrink: 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-how-it-works__step-content {
  flex: 1;
}

.feature-how-it-works__step-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 12px 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-how-it-works__step-description {
  font-size: 1rem;
  color: #374151;
  line-height: 1.6;
  margin: 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-how-it-works__cta {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 20px;
  padding: 60px 40px;
  text-align: center;
  border: 1px solid rgba(0, 106, 193, 0.1);
}

.feature-how-it-works__cta-content {
  max-width: 600px;
  margin: 0 auto;
}

.feature-how-it-works__cta-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 16px 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-how-it-works__cta-description {
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0 0 32px 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-how-it-works__cta-btn {
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  color: white;
  border: none;
  padding: 18px 36px;
  border-radius: 50px;
  font-size: 1.125rem;
  font-weight: 500;
  font-family: "Plus Jakarta Sans", sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 15px rgba(0, 106, 193, 0.2);
}

.feature-how-it-works__cta-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 106, 193, 0.3);
}

.feature-how-it-works__cta-icon {
  width: 18px;
  height: 18px;
  transition: transform 0.3s ease;
}

.feature-how-it-works__cta-btn:hover .feature-how-it-works__cta-icon {
  transform: translateX(4px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .feature-how-it-works {
    padding: 80px 0;
  }

  .feature-how-it-works__container {
    padding: 0 16px;
  }

  .feature-how-it-works__header {
    margin-bottom: 60px;
  }

  .feature-how-it-works__steps {
    margin-bottom: 60px;
  }

  .feature-how-it-works__step {
    flex-direction: column;
    text-align: center;
    gap: 20px;
    padding: 24px;
  }

  .feature-how-it-works__step-number {
    align-self: center;
  }

  .feature-how-it-works__cta {
    padding: 40px 24px;
  }

  .feature-how-it-works__cta-btn {
    padding: 16px 32px;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .feature-how-it-works {
    padding: 60px 0;
  }

  .feature-how-it-works__step {
    padding: 20px;
  }

  .feature-how-it-works__step-number {
    width: 40px;
    height: 40px;
    font-size: 1.125rem;
  }

  .feature-how-it-works__cta {
    padding: 30px 20px;
  }
}
