/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

/* Feature Everything Works Section */
.feature-everything-works {
  background: #ffffff;
  padding: 100px 0;
  position: relative;
}

.feature-everything-works__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.feature-everything-works__header {
  text-align: center;
  margin-bottom: 80px;
}

.feature-everything-works__title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 20px 0;
  line-height: 1.2;
  letter-spacing: -0.02em;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-everything-works__subtitle {
  font-size: 1.25rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
  max-width: 600px;
  margin: 0 auto;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-everything-works__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
}

.feature-everything-works__item {
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
  border: 1px solid rgba(0, 106, 193, 0.1);
  border-radius: 16px;
  padding: 32px 24px;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.feature-everything-works__item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.feature-everything-works__item:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(0, 106, 193, 0.15);
  border-color: rgba(0, 106, 193, 0.2);
}

.feature-everything-works__item:hover::before {
  transform: scaleX(1);
}

.feature-everything-works__icon {
  font-size: 3rem;
  margin-bottom: 20px;
  display: block;
  line-height: 1;
}

.feature-everything-works__item-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 12px 0;
  line-height: 1.3;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-everything-works__item-description {
  font-size: 1rem;
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

/* Responsive Design */
@media (max-width: 768px) {
  .feature-everything-works {
    padding: 80px 0;
  }

  .feature-everything-works__container {
    padding: 0 16px;
  }

  .feature-everything-works__header {
    margin-bottom: 60px;
  }

  .feature-everything-works__grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
  }

  .feature-everything-works__item {
    padding: 28px 20px;
  }

  .feature-everything-works__icon {
    font-size: 2.5rem;
    margin-bottom: 16px;
  }

  .feature-everything-works__item-title {
    font-size: 1.125rem;
  }

  .feature-everything-works__item-description {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .feature-everything-works {
    padding: 60px 0;
  }

  .feature-everything-works__grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .feature-everything-works__item {
    padding: 24px 18px;
  }

  .feature-everything-works__icon {
    font-size: 2rem;
    margin-bottom: 14px;
  }

  .feature-everything-works__item-title {
    font-size: 1rem;
  }

  .feature-everything-works__item-description {
    font-size: 0.85rem;
  }
}

/* Animation for items appearing */
@media (prefers-reduced-motion: no-preference) {
  .feature-everything-works__item {
    animation: fadeInUp 0.6s ease-out;
  }

  .feature-everything-works__item:nth-child(2) {
    animation-delay: 0.1s;
  }

  .feature-everything-works__item:nth-child(3) {
    animation-delay: 0.2s;
  }

  .feature-everything-works__item:nth-child(4) {
    animation-delay: 0.3s;
  }

  .feature-everything-works__item:nth-child(5) {
    animation-delay: 0.4s;
  }

  .feature-everything-works__item:nth-child(6) {
    animation-delay: 0.5s;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus states for accessibility */
.feature-everything-works__item:focus-within {
  outline: 2px solid #006ac1;
  outline-offset: 2px;
}

/* Enhanced grid layout for larger screens */
@media (min-width: 1400px) {
  .feature-everything-works__grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Special styling for featured items */
.feature-everything-works__item--featured {
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  color: white;
}

.feature-everything-works__item--featured .feature-everything-works__item-title {
  color: white;
}

.feature-everything-works__item--featured .feature-everything-works__item-description {
  color: rgba(255, 255, 255, 0.9);
}

.feature-everything-works__item--featured::before {
  background: rgba(255, 255, 255, 0.3);
}
