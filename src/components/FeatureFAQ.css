/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

/* Feature FAQ Section */
.feature-faq {
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
  padding: 100px 0;
  position: relative;
}

.feature-faq__container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 24px;
}

.feature-faq__header {
  text-align: center;
  margin-bottom: 60px;
}

.feature-faq__title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 20px 0;
  line-height: 1.2;
  letter-spacing: -0.02em;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-faq__subtitle {
  font-size: 1.25rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-faq__list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feature-faq__item {
  background: white;
  border: 1px solid rgba(0, 106, 193, 0.1);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 106, 193, 0.05);
}

.feature-faq__item:hover {
  border-color: rgba(0, 106, 193, 0.2);
  box-shadow: 0 4px 20px rgba(0, 106, 193, 0.1);
}

.feature-faq__question {
  width: 100%;
  background: none;
  border: none;
  padding: 24px 28px;
  text-align: left;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  transition: all 0.3s ease;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-faq__question:hover {
  background: rgba(0, 106, 193, 0.02);
}

.feature-faq__question--open {
  background: rgba(0, 106, 193, 0.05);
  border-bottom: 1px solid rgba(0, 106, 193, 0.1);
}

.feature-faq__question-text {
  font-size: 1.125rem;
  font-weight: 500;
  color: #1a1a1a;
  line-height: 1.4;
  flex: 1;
  text-align: left;
}

.feature-faq__question-icon {
  width: 20px;
  height: 20px;
  color: #006ac1;
  transition: transform 0.3s ease;
  flex-shrink: 0;
}

.feature-faq__question--open .feature-faq__question-icon {
  transform: rotate(180deg);
}

.feature-faq__answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  opacity: 0;
}

.feature-faq__answer--open {
  max-height: 500px; /* Adjust based on content */
  opacity: 1;
}

.feature-faq__answer-content {
  padding: 0 28px 0 28px;
  transition: all 0.3s ease;
}

.feature-faq__answer--open .feature-faq__answer-content {
  padding: 0 28px 24px 28px;
}

.feature-faq__answer-text {
  font-size: 1rem;
  color: #374151;
  line-height: 1.6;
  margin: 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

/* Responsive Design */
@media (max-width: 768px) {
  .feature-faq {
    padding: 80px 0;
  }

  .feature-faq__container {
    padding: 0 16px;
  }

  .feature-faq__header {
    margin-bottom: 40px;
  }

  .feature-faq__question {
    padding: 20px 24px;
    gap: 16px;
  }

  .feature-faq__question-text {
    font-size: 1rem;
  }

  .feature-faq__question-icon {
    width: 18px;
    height: 18px;
  }

  .feature-faq__answer-content {
    padding: 0 24px 0 24px;
  }

  .feature-faq__answer--open .feature-faq__answer-content {
    padding: 0 24px 20px 24px;
  }

  .feature-faq__answer-text {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .feature-faq {
    padding: 60px 0;
  }

  .feature-faq__question {
    padding: 18px 20px;
    gap: 12px;
  }

  .feature-faq__question-text {
    font-size: 0.95rem;
  }

  .feature-faq__question-icon {
    width: 16px;
    height: 16px;
  }

  .feature-faq__answer-content {
    padding: 0 20px 0 20px;
  }

  .feature-faq__answer--open .feature-faq__answer-content {
    padding: 0 20px 18px 20px;
  }

  .feature-faq__answer-text {
    font-size: 0.85rem;
  }

  .feature-faq__list {
    gap: 12px;
  }

  .feature-faq__item {
    border-radius: 12px;
  }
}

/* Animation for smooth expand/collapse */
@media (prefers-reduced-motion: no-preference) {
  .feature-faq__answer {
    transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .feature-faq__answer {
    transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease;
  }

  .feature-faq__answer-content {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .feature-faq__question-icon {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* Focus states for accessibility */
.feature-faq__question:focus {
  outline: 2px solid #006ac1;
  outline-offset: 2px;
}

.feature-faq__question:focus:not(:focus-visible) {
  outline: none;
}

/* Enhanced hover effects */
.feature-faq__item:hover .feature-faq__question-icon {
  color: #1a7dd1;
}

.feature-faq__question--open .feature-faq__question-text {
  color: #006ac1;
}

/* Loading state for dynamic content */
.feature-faq__item--loading {
  opacity: 0.6;
  pointer-events: none;
}

.feature-faq__item--loading .feature-faq__question-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
