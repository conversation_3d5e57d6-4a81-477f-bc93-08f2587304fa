import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { getImage } from '../utils/images';
import './CoreFeatures.css';

const CoreFeatures = () => {
  const [isVisible, setIsVisible] = useState(false);
  const navigate = useNavigate();

  // Get images from centralized system
  const featureImages = {
    evvGps: getImage('coreFeatures', 'evvGps'),
    caregiverApp: getImage('coreFeatures', 'caregiverApp'),
    scheduling: getImage('coreFeatures', 'scheduling'),
    taskTracking: getImage('coreFeatures', 'taskTracking'),
    eSignature: getImage('coreFeatures', 'eSignature')
  };

  console.log('Core Features Images:', featureImages);

  // Feature navigation mapping
  const featureNavigation = {
    'Caregiver Mobile App': 'caregiver-app',
    'Smart Scheduling': 'smart-scheduling',
    'Client eSignature': 'esignature',
    'Hiring & Onboarding': 'hiring-onboarding',
    'Training & Certification': 'training-certification'
  };

  const navigateToFeature = (featureTitle) => {
    const featureId = featureNavigation[featureTitle];
    if (featureId) {
      navigate(`/features/${featureId}`);
    }
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    const section = document.querySelector('.core-features');
    if (section) {
      observer.observe(section);
    }

    return () => {
      if (section) {
        observer.unobserve(section);
      }
    };
  }, []);

  const features = [
    {
      badge: "Mobile-First Care",
      title: "Caregiver Mobile App",
      description: "Empower your caregivers with everything they need in their pocket. Clock in/out, document care, and communicate seamlessly from anywhere.",
      checklist: [
        "One-tap clock in/out with GPS verification",
        "Digital care plan access and documentation",
        "Instant messaging with office and families",
        "Photo documentation for care activities"
      ],
      cta: "See Demo",
      icon: "📱",
      imageKey: "caregiverApp",
      isSmall: true
    },
    {
      badge: "Intelligent Scheduling",
      title: "Smart Scheduling",
      description: "Reduce scheduling conflicts by 90% with AI-powered assignment matching. Fill gaps faster and optimize caregiver routes automatically.",
      checklist: [
        "AI-powered caregiver-client matching",
        "Automatic route optimization for efficiency",
        "Real-time schedule conflict detection",
        "One-click gap filling and shift coverage"
      ],
      cta: "Try It Free",
      icon: "📅",
      imageKey: "scheduling"
    },
    {
      badge: "Digital Signatures",
      title: "Client eSignature",
      description: "Get visit approvals and care plan signatures digitally. Reduce paperwork, speed up processes, and maintain secure digital records.",
      checklist: [
        "Secure digital signature collection",
        "Automated care plan approvals",
        "Legal compliance and audit trails",
        "Instant document sharing with families"
      ],
      cta: "Get Started",
      icon: "✍️",
      imageKey: "eSignature"
    },
    {
      badge: "Streamlined Hiring",
      title: "Hiring & Onboarding",
      description: "Automate your hiring process from application to first shift. Background checks, training modules, and compliance tracking all in one place.",
      checklist: [
        "Automated background check processing",
        "Digital onboarding workflows",
        "Compliance document management",
        "Training progress tracking"
      ],
      cta: "Start Hiring",
      icon: "👥",
      imageKey: "caregiverApp"
    },
    {
      badge: "Skills Development",
      title: "Training & Certification",
      description: "Keep your team certified and compliant with built-in training modules. Track progress, manage certifications, and ensure quality care.",
      checklist: [
        "Interactive training modules",
        "Certification tracking and alerts",
        "Compliance training management",
        "Progress reporting and analytics"
      ],
      cta: "View Training",
      icon: "🎓",
      imageKey: "taskTracking"
    }
  ];

  return (
    <section className={`core-features ${isVisible ? 'core-features--visible' : ''}`}>
      <div className="core-features__container">
        <div className="core-features__header">
          <div className="core-features__label">
            Core Features
          </div>
          <h2 className="core-features__title">
            Everything you need to run your home care agency
          </h2>
        </div>

        <div className="core-features__zigzag">
          {features.map((feature, index) => (
            <div
              key={index}
              className={`core-features__item ${index % 2 === 1 ? 'core-features__item--reverse' : ''}`}
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              {/* Text Content */}
              <div className="core-features__content">
                <div className="core-features__badge">
                  <span className="core-features__badge-icon">{feature.icon}</span>
                  {feature.badge}
                </div>

                <h3 className="core-features__item-title">
                  {feature.title}
                </h3>

                <p className="core-features__item-description">
                  {feature.description}
                </p>

                <ul className="core-features__checklist">
                  {feature.checklist.map((item, itemIndex) => (
                    <li key={itemIndex} className="core-features__checklist-item">
                      <svg className="core-features__check-icon" viewBox="0 0 24 24" fill="none">
                        <path d="M20 6L9 17l-5-5" stroke="#10b981" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      {item}
                    </li>
                  ))}
                </ul>

                <button
                  className="core-features__cta"
                  onClick={() => navigateToFeature(feature.title)}
                >
                  {feature.cta}
                  <svg className="core-features__cta-icon" viewBox="0 0 24 24" fill="none">
                    <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
              </div>

              {/* Image Content */}
              <div className="core-features__image">
                <div className={`core-features__image-container ${feature.isSmall ? 'core-features__image-container--small' : ''}`}>
                  {/* Mac-style Header */}
                  <div className="core-features__mac-header">
                    <div className="core-features__mac-buttons">
                      <div className="core-features__mac-button core-features__mac-button--close"></div>
                      <div className="core-features__mac-button core-features__mac-button--minimize"></div>
                      <div className="core-features__mac-button core-features__mac-button--maximize"></div>
                    </div>
                  </div>

                  {featureImages[feature.imageKey].src ? (
                    <img
                      src={featureImages[feature.imageKey].src}
                      alt={featureImages[feature.imageKey].alt}
                      className="core-features__feature-image"
                      onError={(e) => {
                        // Fallback to placeholder if actual image fails
                        console.log('Image failed to load:', featureImages[feature.imageKey].src);
                        e.target.style.display = 'none';
                        e.target.nextSibling.style.display = 'flex';
                      }}
                    />
                  ) : null}

                  {/* Fallback placeholder */}
                  <div className="core-features__image-placeholder" style={{ display: 'none' }}>
                    <div className="core-features__image-icon">{feature.icon}</div>
                    <div className="core-features__image-label">{feature.title}</div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* View All Features CTA */}
        <div className="core-features__cta-section">
          <div className="core-features__cta-content">
            <h3 className="core-features__cta-title">
              Ready to see all 13 features in action?
            </h3>
            <p className="core-features__cta-description">
              Discover how Telescope's complete feature set can transform your home care agency
            </p>
            <button
              className="core-features__view-all-btn"
              onClick={() => navigate('/features')}
            >
              View All Features
              <svg className="core-features__view-all-icon" viewBox="0 0 24 24" fill="none">
                <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CoreFeatures;
