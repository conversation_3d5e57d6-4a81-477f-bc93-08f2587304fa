/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

/* Contact Us Page */
.contact-us {
  font-family: "Plus Jakarta Sans", sans-serif;
}

/* Hero Section */
.contact-hero {
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
  padding: 120px 0 100px 0;
  position: relative;
  overflow: hidden;
}

.contact-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(0,106,193,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.5;
}

.contact-hero__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 1;
}

.contact-hero__grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.contact-hero__content {
  text-align: left;
}

.contact-hero__badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: rgba(0, 106, 193, 0.1);
  color: #006ac1;
  padding: 8px 16px;
  border-radius: 50px;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 24px;
}

.contact-hero__badge-icon {
  width: 16px;
  height: 16px;
}

.contact-hero__title {
  font-size: clamp(2.5rem, 4vw, 3.5rem);
  font-weight: 500;
  color: #1a1a1a;
  margin: 0 0 24px 0;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.contact-hero__subtitle {
  font-size: 1.25rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0 0 40px 0;
}

.contact-hero__stats {
  display: flex;
  gap: 32px;
  margin-top: 40px;
}

.contact-hero__stat {
  text-align: center;
}

.contact-hero__stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #006ac1;
  line-height: 1;
  display: block;
}

.contact-hero__stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 4px;
  display: block;
}

/* Hero Visual */
.contact-hero__visual {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.contact-hero__image-container {
  position: relative;
  width: 100%;
  max-width: 400px;
}

.contact-hero__main-visual {
  position: relative;
  z-index: 2;
}

.contact-hero__phone-mockup {
  width: 280px;
  height: 560px;
  background: #1a1a1a;
  border-radius: 40px;
  padding: 8px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  margin: 0 auto;
}

.contact-hero__phone-screen {
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 32px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.contact-hero__phone-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px 8px 24px;
  background: white;
}

.contact-hero__phone-time {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1a1a1a;
}

.contact-hero__phone-status {
  display: flex;
  gap: 4px;
  align-items: center;
}

.contact-hero__phone-signal,
.contact-hero__phone-battery {
  width: 18px;
  height: 10px;
  background: #1a1a1a;
  border-radius: 2px;
}

/* Dynamic Island Styles */
.contact-hero__dynamic-island {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%) scale(0.8);
  background: #1a1a1a;
  border-radius: 20px;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  z-index: 10;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  cursor: pointer;
  opacity: 0;
}

.contact-hero--visible .contact-hero__dynamic-island {
  opacity: 1;
  transform: translateX(-50%) scale(1);
  transition-delay: 1s;
}

.contact-hero__dynamic-island:hover {
  transform: translateX(-50%) scale(1.02);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.15);
}

.contact-hero__dynamic-island--default {
  width: 120px;
  height: 32px;
}

.contact-hero__dynamic-island--call,
.contact-hero__dynamic-island--notification,
.contact-hero__dynamic-island--music,
.contact-hero__dynamic-island--timer {
  width: 220px;
  height: 48px;
  border-radius: 24px;
}

.contact-hero__island-default {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 100%;
  padding: 0 16px;
  animation: islandPulse 3s ease-in-out infinite;
}

.contact-hero__island-camera {
  width: 8px;
  height: 8px;
  background: radial-gradient(circle, #444 30%, #222 100%);
  border-radius: 50%;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.3);
}

.contact-hero__island-speaker {
  width: 16px;
  height: 4px;
  background: linear-gradient(to right, #333, #222, #333);
  border-radius: 2px;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2);
}

.contact-hero__island-expanded {
  display: flex;
  align-items: center;
  gap: 10px;
  height: 100%;
  padding: 10px 16px;
  animation: islandExpand 0.6s ease-out;
}

.contact-hero__island-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  flex-shrink: 0;
}

.contact-hero__island-content {
  flex: 1;
  min-width: 0;
}

.contact-hero__island-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  line-height: 1;
  margin-bottom: 2px;
}

.contact-hero__island-subtitle {
  font-size: 0.625rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1;
}

.contact-hero__island-actions {
  display: flex;
  gap: 6px;
}

.contact-hero__island-action {
  width: 26px;
  height: 26px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: bold;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.contact-hero__island-action:hover {
  transform: scale(1.1);
}

.contact-hero__island-action--decline {
  background: #FF3B30;
  color: white;
}

.contact-hero__island-action--accept {
  background: #34C759;
  color: white;
}

.contact-hero__island-waveform {
  display: flex;
  align-items: center;
  gap: 2px;
  height: 20px;
}

.contact-hero__wave {
  width: 2px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 1px;
  animation: waveAnimation 1.5s ease-in-out infinite;
}

.contact-hero__wave:nth-child(1) {
  height: 8px;
  animation-delay: 0s;
}

.contact-hero__wave:nth-child(2) {
  height: 16px;
  animation-delay: 0.2s;
}

.contact-hero__wave:nth-child(3) {
  height: 12px;
  animation-delay: 0.4s;
}

@keyframes islandExpand {
  0% {
    width: 120px;
    height: 32px;
    border-radius: 20px;
  }
  100% {
    width: 220px;
    height: 48px;
    border-radius: 24px;
  }
}

@keyframes waveAnimation {
  0%, 100% {
    transform: scaleY(0.5);
    opacity: 0.7;
  }
  50% {
    transform: scaleY(1);
    opacity: 1;
  }
}

@keyframes islandPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.contact-hero__phone-content {
  flex: 1;
  padding: 20px;
  background: #f8faff;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.contact-hero__chat-bubble {
  max-width: 80%;
  padding: 12px 16px;
  border-radius: 18px;
  position: relative;
}

.contact-hero__chat-bubble--received {
  background: white;
  align-self: flex-start;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.contact-hero__chat-bubble--sent {
  background: #006ac1;
  color: white;
  align-self: flex-end;
}

.contact-hero__chat-text {
  font-size: 0.875rem;
  line-height: 1.4;
  margin-bottom: 4px;
}

.contact-hero__chat-time {
  font-size: 0.75rem;
  opacity: 0.7;
}

/* Floating Cards */
.contact-hero__floating-card {
  position: absolute;
  background: white;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 8px 30px rgba(0, 106, 193, 0.15);
  border: 1px solid rgba(0, 106, 193, 0.1);
  display: flex;
  align-items: center;
  gap: 12px;
  z-index: 5;
  opacity: 0;
  transform: translateY(30px) scale(0.9);
  transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);
}

.contact-hero--visible .contact-hero__floating-card {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.contact-hero__floating-card--1 {
  top: 10%;
  left: -10%;
  transition-delay: 0.8s;
}

.contact-hero--visible .contact-hero__floating-card--1 {
  animation: float 6s ease-in-out infinite 1.8s;
}

.contact-hero__floating-card--2 {
  top: 60%;
  right: -15%;
  transition-delay: 1s;
}

.contact-hero--visible .contact-hero__floating-card--2 {
  animation: float 6s ease-in-out infinite 2s;
}

.contact-hero__floating-card--3 {
  bottom: 10%;
  left: -5%;
  transition-delay: 1.2s;
}

.contact-hero--visible .contact-hero__floating-card--3 {
  animation: float 6s ease-in-out infinite 2.2s;
}

.contact-hero__card-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.contact-hero__card-icon svg {
  width: 20px;
  height: 20px;
  color: white;
}

.contact-hero__card-text {
  min-width: 0;
}

.contact-hero__card-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 2px 0;
}

.contact-hero__card-subtitle {
  font-size: 0.75rem;
  color: #6b7280;
  margin: 0;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* Contact Content */
.contact-content {
  padding: 120px 0;
  background: white;
}

.contact-content__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.contact-content__header {
  text-align: center;
  margin-bottom: 80px;
}

.contact-content__title {
  font-size: clamp(2rem, 3vw, 2.5rem);
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 16px 0;
  letter-spacing: -0.02em;
}

.contact-content__subtitle {
  font-size: 1.125rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.6;
}

.contact-content__grid {
  display: grid;
  grid-template-columns: 1fr 1.2fr;
  gap: 80px;
  align-items: start;
}

/* Contact Methods */
.contact-methods {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.contact-method__card {
  background: white;
  border: 1px solid rgba(0, 106, 193, 0.1);
  border-radius: 20px;
  padding: 32px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 106, 193, 0.05);
  position: relative;
  overflow: hidden;
}

.contact-method__card:hover {
  border-color: rgba(0, 106, 193, 0.2);
  box-shadow: 0 8px 30px rgba(0, 106, 193, 0.1);
  transform: translateY(-4px);
}

.contact-method__card--primary {
  border-color: rgba(0, 106, 193, 0.2);
  background: linear-gradient(135deg, rgba(0, 106, 193, 0.02) 0%, rgba(26, 125, 209, 0.02) 100%);
}

.contact-method__card--primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #006ac1 0%, #1a7dd1 100%);
}

.contact-method__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.contact-method__icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contact-method__icon svg {
  width: 24px;
  height: 24px;
  color: white;
}

.contact-method__badge {
  background: rgba(0, 106, 193, 0.1);
  color: #006ac1;
  padding: 4px 12px;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 500;
}

.contact-method__title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 12px 0;
}

.contact-method__description {
  font-size: 1rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0 0 20px 0;
}

.contact-method__detail {
  margin-top: 20px;
}

.contact-method__link {
  color: #006ac1;
  text-decoration: none;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.contact-method__link:hover {
  color: #1a7dd1;
  transform: translateX(4px);
}

.contact-method__link svg {
  width: 16px;
  height: 16px;
}

.contact-method__link--button {
  background: #006ac1;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  text-decoration: none;
}

.contact-method__link--button:hover {
  background: #1a7dd1;
  color: white;
  transform: translateY(-2px);
}

.contact-method__schedule {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.contact-method__schedule-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(0, 106, 193, 0.1);
}

.contact-method__schedule-item:last-child {
  border-bottom: none;
}

.contact-method__day {
  font-size: 0.875rem;
  color: #6b7280;
}

.contact-method__time {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1a1a1a;
}

/* Contact Form */
.contact-form {
  background: white;
  border: 1px solid rgba(0, 106, 193, 0.1);
  border-radius: 24px;
  box-shadow: 0 8px 40px rgba(0, 106, 193, 0.08);
  position: relative;
  overflow: hidden;
}

.contact-form__visual {
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  pointer-events: none;
}

.contact-form__decoration {
  position: relative;
  width: 100%;
  height: 100%;
}

.contact-form__decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(0, 106, 193, 0.1) 0%, rgba(26, 125, 209, 0.05) 100%);
}

.contact-form__decoration-circle--1 {
  width: 120px;
  height: 120px;
  top: -60px;
  right: -60px;
  animation: float 8s ease-in-out infinite;
}

.contact-form__decoration-circle--2 {
  width: 80px;
  height: 80px;
  top: 20px;
  right: 20px;
  animation: float 8s ease-in-out infinite reverse;
  animation-delay: 2s;
}

.contact-form__decoration-circle--3 {
  width: 40px;
  height: 40px;
  top: 80px;
  right: 80px;
  animation: float 8s ease-in-out infinite;
  animation-delay: 4s;
}

.contact-form__content {
  padding: 40px;
  position: relative;
  z-index: 1;
}

.contact-form__header {
  margin-bottom: 32px;
}

.contact-form__badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: rgba(0, 106, 193, 0.1);
  color: #006ac1;
  padding: 8px 16px;
  border-radius: 50px;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 16px;
}

.contact-form__badge-icon {
  width: 16px;
  height: 16px;
}

.contact-form__title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 12px 0;
  letter-spacing: -0.02em;
}

.contact-form__subtitle {
  font-size: 1rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.6;
}

.contact-form__form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.contact-form__row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.contact-form__field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.contact-form__label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.contact-form__input,
.contact-form__select,
.contact-form__textarea {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  font-family: inherit;
  transition: all 0.3s ease;
  background: white;
}

.contact-form__input:focus,
.contact-form__select:focus,
.contact-form__textarea:focus {
  outline: none;
  border-color: #006ac1;
  box-shadow: 0 0 0 3px rgba(0, 106, 193, 0.1);
}

.contact-form__textarea {
  resize: vertical;
  min-height: 120px;
}

.contact-form__submit {
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: 0 4px 20px rgba(0, 106, 193, 0.25);
}

.contact-form__submit:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 106, 193, 0.35);
}

.contact-form__submit:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.contact-form__submit-icon,
.contact-form__loading-icon {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

.contact-form__success,
.contact-form__error {
  padding: 16px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.875rem;
  font-weight: 500;
}

.contact-form__success {
  background: #f0fdf4;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.contact-form__error {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.contact-form__success-icon {
  width: 20px;
  height: 20px;
  color: #16a34a;
  flex-shrink: 0;
}

/* Contact Actions */
.contact-actions {
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
  padding: 120px 0;
  position: relative;
}

.contact-actions::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="10" height="10" patternUnits="userSpaceOnUse"><circle cx="5" cy="5" r="1" fill="rgba(0,106,193,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.5;
}

.contact-actions__container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 1;
}

.contact-actions__header {
  text-align: center;
  margin-bottom: 60px;
}

.contact-actions__title {
  font-size: clamp(2rem, 3vw, 2.5rem);
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 16px 0;
  letter-spacing: -0.02em;
}

.contact-actions__subtitle {
  font-size: 1.125rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.6;
}

.contact-actions__grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
}

.contact-actions__card {
  background: white;
  border: 1px solid rgba(0, 106, 193, 0.1);
  border-radius: 24px;
  padding: 40px;
  transition: all 0.3s ease;
  box-shadow: 0 8px 30px rgba(0, 106, 193, 0.08);
  position: relative;
  overflow: hidden;
}

.contact-actions__card:hover {
  border-color: rgba(0, 106, 193, 0.2);
  box-shadow: 0 12px 40px rgba(0, 106, 193, 0.15);
  transform: translateY(-8px);
}

.contact-actions__card--featured {
  border-color: rgba(0, 106, 193, 0.2);
  background: linear-gradient(135deg, rgba(0, 106, 193, 0.02) 0%, rgba(26, 125, 209, 0.02) 100%);
}

.contact-actions__card--featured::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #006ac1 0%, #1a7dd1 100%);
}

.contact-actions__card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.contact-actions__icon {
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contact-actions__icon svg {
  width: 28px;
  height: 28px;
  color: white;
}

.contact-actions__badge {
  background: rgba(0, 106, 193, 0.1);
  color: #006ac1;
  padding: 6px 12px;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 500;
}

.contact-actions__card-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 12px 0;
  letter-spacing: -0.02em;
}

.contact-actions__description {
  font-size: 1rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0 0 24px 0;
}

.contact-actions__features {
  list-style: none;
  padding: 0;
  margin: 0 0 32px 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.contact-actions__feature {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.875rem;
  color: #374151;
}

.contact-actions__feature svg {
  width: 16px;
  height: 16px;
  color: #10b981;
  flex-shrink: 0;
}

.contact-actions__button {
  width: 100%;
  background: transparent;
  color: #006ac1;
  border: 2px solid #006ac1;
  padding: 16px 24px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  font-family: inherit;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.contact-actions__button:hover {
  background: #006ac1;
  color: white;
  transform: translateY(-2px);
  text-decoration: none;
  box-shadow: 0 8px 25px rgba(0, 106, 193, 0.25);
}

.contact-actions__button--primary {
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  color: white;
  border: none;
}

.contact-actions__button--primary:hover {
  background: linear-gradient(135deg, #1a7dd1 0%, #006ac1 100%);
  color: white;
  box-shadow: 0 8px 25px rgba(0, 106, 193, 0.35);
}

.contact-actions__button svg {
  width: 18px;
  height: 18px;
  transition: transform 0.3s ease;
}

.contact-actions__button:hover svg {
  transform: translateX(4px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .contact-hero__grid {
    gap: 60px;
  }

  .contact-hero__stats {
    gap: 24px;
  }

  .contact-hero__floating-card {
    display: none;
  }

  .contact-content__grid {
    gap: 60px;
  }

  .contact-actions__grid {
    gap: 32px;
  }
}

@media (max-width: 768px) {
  .contact-hero {
    padding: 100px 0 80px 0;
  }

  .contact-hero__grid {
    grid-template-columns: 1fr;
    gap: 60px;
    text-align: center;
  }

  .contact-hero__content {
    text-align: center;
  }

  .contact-hero__stats {
    justify-content: center;
  }

  .contact-hero__phone-mockup {
    width: 240px;
    height: 480px;
  }

  .contact-content {
    padding: 100px 0;
  }

  .contact-content__header {
    margin-bottom: 60px;
  }

  .contact-content__grid {
    grid-template-columns: 1fr;
    gap: 60px;
  }

  .contact-form__content {
    padding: 32px 24px;
  }

  .contact-form__row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .contact-actions {
    padding: 100px 0;
  }

  .contact-actions__grid {
    grid-template-columns: 1fr;
    gap: 32px;
  }

  .contact-actions__card {
    padding: 32px;
  }
}

@media (max-width: 480px) {
  .contact-hero {
    padding: 80px 0 60px 0;
  }

  .contact-hero__container,
  .contact-content__container,
  .contact-actions__container {
    padding: 0 16px;
  }

  .contact-hero__stats {
    flex-direction: column;
    gap: 16px;
    align-items: center;
  }

  .contact-hero__phone-mockup {
    width: 200px;
    height: 400px;
  }

  .contact-content {
    padding: 80px 0;
  }

  .contact-content__header {
    margin-bottom: 40px;
  }

  .contact-content__grid {
    gap: 40px;
  }

  .contact-methods {
    gap: 20px;
  }

  .contact-method__card {
    padding: 24px;
  }

  .contact-form__content {
    padding: 24px 20px;
  }

  .contact-actions {
    padding: 80px 0;
  }

  .contact-actions__header {
    margin-bottom: 40px;
  }

  .contact-actions__card {
    padding: 24px;
  }

  .contact-actions__icon {
    width: 48px;
    height: 48px;
  }

  .contact-actions__icon svg {
    width: 24px;
    height: 24px;
  }

  .contact-actions__card-title {
    font-size: 1.25rem;
  }

  .contact-hero__dynamic-island--default {
    width: 100px;
    height: 28px;
  }

  .contact-hero__dynamic-island--call,
  .contact-hero__dynamic-island--notification,
  .contact-hero__dynamic-island--music,
  .contact-hero__dynamic-island--timer {
    width: 200px;
    height: 44px;
  }

  .contact-hero__island-icon {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .contact-hero__dynamic-island--default {
    width: 80px;
    height: 24px;
  }

  .contact-hero__dynamic-island--call,
  .contact-hero__dynamic-island--notification,
  .contact-hero__dynamic-island--music,
  .contact-hero__dynamic-island--timer {
    width: 180px;
    height: 40px;
  }

  .contact-hero__island-icon {
    width: 24px;
    height: 24px;
    font-size: 10px;
  }

  .contact-hero__island-title {
    font-size: 0.625rem;
  }

  .contact-hero__island-subtitle {
    font-size: 0.5rem;
  }
}
