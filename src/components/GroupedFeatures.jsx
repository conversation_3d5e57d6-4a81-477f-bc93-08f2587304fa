import { useEffect, useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import './GroupedFeatures.css';

const GroupedFeatures = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [isScrollHijacked, setIsScrollHijacked] = useState(false);
  const [scrollProgress, setScrollProgress] = useState(0);
  const bentoGridRef = useRef(null);
  const sectionRef = useRef(null);
  const navigate = useNavigate();

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    const section = sectionRef.current;
    if (section) {
      observer.observe(section);
    }

    return () => {
      if (section) {
        observer.unobserve(section);
      }
    };
  }, []);

  // Enhanced scroll hijacking effect
  useEffect(() => {
    let isScrolling = false;

    const handleWheel = (e) => {
      const bentoGrid = bentoGridRef.current;
      const section = sectionRef.current;

      if (!bentoGrid || !section || isScrolling) return;

      const sectionRect = section.getBoundingClientRect();
      const windowHeight = window.innerHeight;

      // Check if section is in the center of viewport (more precise detection)
      const sectionCenter = sectionRect.top + sectionRect.height / 2;
      const isInActiveZone = sectionCenter >= 0 && sectionCenter <= windowHeight;

      if (isInActiveZone) {
        const scrollTop = bentoGrid.scrollTop;
        const scrollHeight = bentoGrid.scrollHeight;
        const clientHeight = bentoGrid.clientHeight;
        const maxScroll = scrollHeight - clientHeight;

        // Only hijack if there's content to scroll
        if (maxScroll > 10) { // 10px threshold for better detection
          const isAtTop = scrollTop <= 5;
          const isAtBottom = scrollTop >= maxScroll - 5;

          // Determine scroll direction and hijack accordingly
          if (e.deltaY > 0) { // Scrolling down
            if (!isAtBottom) {
              e.preventDefault();
              e.stopPropagation();

              isScrolling = true;
              bentoGrid.scrollTop += e.deltaY * 0.8;
              setIsScrollHijacked(true);

              setTimeout(() => { isScrolling = false; }, 50);
              return;
            }
          } else { // Scrolling up
            if (!isAtTop) {
              e.preventDefault();
              e.stopPropagation();

              isScrolling = true;
              bentoGrid.scrollTop += e.deltaY * 0.8;
              setIsScrollHijacked(true);

              setTimeout(() => { isScrolling = false; }, 50);
              return;
            }
          }
        }
      }

      setIsScrollHijacked(false);
    };

    // Also handle touch events for mobile
    const handleTouchStart = (e) => {
      const touch = e.touches[0];
      window.lastTouchY = touch.clientY;
    };

    const handleTouchMove = (e) => {
      const bentoGrid = bentoGridRef.current;
      const section = sectionRef.current;

      if (!bentoGrid || !section || !window.lastTouchY) return;

      const touch = e.touches[0];
      const deltaY = window.lastTouchY - touch.clientY;
      window.lastTouchY = touch.clientY;

      const sectionRect = section.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      const sectionCenter = sectionRect.top + sectionRect.height / 2;
      const isInActiveZone = sectionCenter >= 0 && sectionCenter <= windowHeight;

      if (isInActiveZone) {
        const scrollTop = bentoGrid.scrollTop;
        const scrollHeight = bentoGrid.scrollHeight;
        const clientHeight = bentoGrid.clientHeight;
        const maxScroll = scrollHeight - clientHeight;

        if (maxScroll > 10) {
          const isAtTop = scrollTop <= 5;
          const isAtBottom = scrollTop >= maxScroll - 5;

          if ((deltaY > 0 && !isAtBottom) || (deltaY < 0 && !isAtTop)) {
            e.preventDefault();
            bentoGrid.scrollTop += deltaY;
            setIsScrollHijacked(true);
          }
        }
      }
    };

    // Add event listeners
    window.addEventListener('wheel', handleWheel, { passive: false });
    window.addEventListener('touchstart', handleTouchStart, { passive: true });
    window.addEventListener('touchmove', handleTouchMove, { passive: false });

    return () => {
      window.removeEventListener('wheel', handleWheel);
      window.removeEventListener('touchstart', handleTouchStart);
      window.removeEventListener('touchmove', handleTouchMove);
    };
  }, []);

  const features = [
    {
      name: "Custom Forms",
      description: "Streamline assessments, documentation & more",
      icon: "📝",
      size: "large"
    },
    {
      name: "Text Messaging",
      description: "Send shift updates & reminders in seconds",
      icon: "💬",
      size: "medium"
    },
    {
      name: "Client & Caregiver Surveys",
      description: "Improve satisfaction and outcomes",
      icon: "⭐",
      size: "medium"
    },
    {
      name: "Client & Family Portals",
      description: "Share care plans, notes & orientation",
      icon: "👥",
      size: "large"
    },
    {
      name: "Hiring & Onboarding",
      description: "Automate recruitment & orientation",
      icon: "🎯",
      size: "small"
    },
    {
      name: "Caregiver Training",
      description: "Built-in classes for caregiver success",
      icon: "🎓",
      size: "medium"
    },
    {
      name: "Background Screening",
      description: "Instant checks (OIG, Nursys, more)",
      icon: "🔍",
      size: "small"
    },
    {
      name: "Visit Documentation",
      description: "Track every shift, every detail",
      icon: "📋",
      size: "large"
    },
    {
      name: "Invoicing",
      description: "Get paid on time, every time",
      icon: "💰",
      size: "medium"
    },
    {
      name: "Reporting & Analytics",
      description: "Data-driven insights for growth",
      icon: "📊",
      size: "large"
    },
    {
      name: "Mobile Notifications",
      description: "Stay connected on the go",
      icon: "🔔",
      size: "small"
    },
    {
      name: "Calendar Integration",
      description: "Sync with your favorite calendar",
      icon: "📆",
      size: "medium"
    }
  ];

  return (
    <section
      ref={sectionRef}
      className={`grouped-features ${isVisible ? 'grouped-features--visible' : ''} ${isScrollHijacked ? 'grouped-features--hijacked' : ''}`}
    >
      <div className="grouped-features__container">
        <div className="grouped-features__content">
          {/* Left Side: Summary */}
          <div className="grouped-features__summary">
            <div className="grouped-features__badge">
              <span className="grouped-features__badge-icon">⚡</span>
              Complete Platform
            </div>

            <h2 className="grouped-features__title">
              Everything organized for your success
            </h2>

            <p className="grouped-features__description">
              Powerful features designed to streamline every aspect of your home care business. From client management to compliance, we've got you covered.
            </p>

            <div className="grouped-features__stats">
              <div className="grouped-features__stat">
                <div className="grouped-features__stat-number">95%</div>
                <div className="grouped-features__stat-label">Client Satisfaction</div>
              </div>
              <div className="grouped-features__stat">
                <div className="grouped-features__stat-number">40hrs</div>
                <div className="grouped-features__stat-label">Saved Per Week</div>
              </div>
            </div>

            <button
              className="grouped-features__cta"
              onClick={() => navigate('/features')}
            >
              Explore All Features
              <svg className="grouped-features__cta-icon" viewBox="0 0 24 24" fill="none">
                <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
          </div>

          {/* Right Side: Bento Grid */}
          <div className="grouped-features__bento-container">
            {isScrollHijacked && (
              <div className="grouped-features__scroll-indicator">
                <div className="grouped-features__scroll-text">Scroll to explore features</div>
                <div className="grouped-features__scroll-progress">
                  <div
                    className="grouped-features__scroll-progress-bar"
                    style={{ width: `${scrollProgress}%` }}
                  ></div>
                </div>
              </div>
            )}
            <div className="grouped-features__bento-blur grouped-features__bento-blur--top"></div>

            <div ref={bentoGridRef} className="grouped-features__bento-grid">
              {features.map((feature, index) => (
                <div
                  key={index}
                  className={`grouped-features__card grouped-features__card--${feature.size}`}
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="grouped-features__card-icon">
                    {feature.icon}
                  </div>
                  <h3 className="grouped-features__card-title">
                    {feature.name}
                  </h3>
                  <p className="grouped-features__card-description">
                    {feature.description}
                  </p>
                </div>
              ))}
            </div>

            <div className="grouped-features__bento-blur grouped-features__bento-blur--bottom"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default GroupedFeatures;
