import { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import './Pricing.css';

const Pricing = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    industry: '',
    employees: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);

  // Animation states
  const [isHeroVisible, setIsHeroVisible] = useState(false);
  const [isContentVisible, setIsContentVisible] = useState(false);
  const [isFaqVisible, setIsFaqVisible] = useState(false);
  const [isCtaVisible, setIsCtaVisible] = useState(false);

  // Refs for intersection observer
  const heroRef = useRef(null);
  const contentRef = useRef(null);
  const faqRef = useRef(null);
  const ctaRef = useRef(null);

  // Intersection Observer for animations
  useEffect(() => {
    const observerOptions = {
      threshold: 0.2,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          if (entry.target === heroRef.current) {
            setIsHeroVisible(true);
          } else if (entry.target === contentRef.current) {
            setIsContentVisible(true);
          } else if (entry.target === faqRef.current) {
            setIsFaqVisible(true);
          } else if (entry.target === ctaRef.current) {
            setIsCtaVisible(true);
          }
        }
      });
    }, observerOptions);

    if (heroRef.current) observer.observe(heroRef.current);
    if (contentRef.current) observer.observe(contentRef.current);
    if (faqRef.current) observer.observe(faqRef.current);
    if (ctaRef.current) observer.observe(ctaRef.current);

    // Initial hero animation
    setTimeout(() => setIsHeroVisible(true), 100);

    return () => {
      if (heroRef.current) observer.unobserve(heroRef.current);
      if (contentRef.current) observer.unobserve(contentRef.current);
      if (faqRef.current) observer.unobserve(faqRef.current);
      if (ctaRef.current) observer.unobserve(ctaRef.current);
    };
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSubmitStatus('success');
      setFormData({
        name: '',
        email: '',
        phone: '',
        industry: '',
        employees: ''
      });
    } catch (error) {
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const industries = [
    'Technology',
    'Healthcare',
    'Finance',
    'Education',
    'Manufacturing',
    'Retail',
    'Construction',
    'Real Estate',
    'Marketing & Advertising',
    'Consulting',
    'Non-profit',
    'Government',
    'Other'
  ];

  const employeeSizes = [
    '1-10 employees',
    '11-50 employees',
    '51-200 employees',
    '201-500 employees',
    '501-1000 employees',
    '1000+ employees'
  ];

  const features = [
    'Post jobs to multiple boards in one click',
    'Streamline hiring with easy candidate tracking',
    'Attract top talent with a branded career site',
    'Be the first to reach the applicant with automated texting',
    'Ensure best fits with skill assessments',
    'Save time with hassle-free interview scheduling',
    'Train to retain using our learning management system',
    'Enhance communication with instant text updates',
    'Boost retention with automated 30, 60, 90 day employee check-ins'
  ];

  const faqs = [
    {
      question: "Do you provide implementation and customer support?",
      answer: "Our Customer Success team is available 24/7 through email, text, chat, and phone and will help you get up and running and guide you to customize Telescope to your organization's needs. You will have a Customer Success team member support you and your staff anytime you need after implementation."
    },
    {
      question: "How much does it cost to use Telescope?",
      answer: "Pricing depends on the number of employees, contract length, and the modules you decide to purchase. For detailed pricing information, please reach out to get a quote."
    },
    {
      question: "Is there a setup fee?",
      answer: "There's no setup fee and no additional or hidden costs!"
    },
    {
      question: "Can I try Telescope before paying?",
      answer: "Yes, absolutely! You can test out Telescope free of charge for a full 14 days by clicking here."
    },
    {
      question: "How long will it take to implement Telescope in my company?",
      answer: "Thanks to our best practice frameworks, Telescope is ready to use from day 1!"
    },
    {
      question: "What languages is Telescope available in?",
      answer: "The Telescope platform is available in 2 languages: English and Spanish with more coming. Our sales team, customer support team, and our Success Center are available in English and Spanish as well."
    }
  ];

  return (
    <div className="pricing">
      {/* Hero Section */}
      <section className={`pricing-hero ${isHeroVisible ? 'pricing-hero--visible' : ''}`} ref={heroRef}>
        <div className="pricing-hero__container">
          <div className="pricing-hero__content">
            <h1 className="pricing-hero__title">Let us give you a quote!</h1>
            <p className="pricing-hero__subtitle">
              Our all-in-one recruitment software has everything you need to hire successfully.
              TelescopeHR is perfect for companies of all sizes — from startups to enterprises.
            </p>
          </div>
        </div>
      </section>

      {/* Features & Form Section */}
      <section className={`pricing-content ${isContentVisible ? 'pricing-content--visible' : ''}`} ref={contentRef}>
        <div className="pricing-content__container">
          <div className="pricing-content__grid">
            {/* Features List */}
            <div className="pricing-features">
              <h2 className="pricing-features__title">Everything you need to hire successfully</h2>
              <ul className="pricing-features__list">
                {features.map((feature, index) => (
                  <li key={index} className="pricing-features__item">
                    <svg className="pricing-features__icon" viewBox="0 0 24 24" fill="none">
                      <path d="M20 6L9 17l-5-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>

            {/* Quote Form */}
            <div className="pricing-form">
              <div className="pricing-form__content">
                <h3 className="pricing-form__title">Get Your Custom Quote</h3>
                <p className="pricing-form__subtitle">
                  Tell us about your company and we'll provide a tailored pricing solution.
                </p>

                <form onSubmit={handleSubmit} className="pricing-form__form">
                  <div className="pricing-form__field">
                    <label className="pricing-form__label">Your Name *</label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      placeholder="Your Name"
                      className="pricing-form__input"
                      required
                    />
                  </div>

                  <div className="pricing-form__field">
                    <label className="pricing-form__label">Your Email *</label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="Your email"
                      className="pricing-form__input"
                      required
                    />
                  </div>

                  <div className="pricing-form__field">
                    <label className="pricing-form__label">Your Phone Number *</label>
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      placeholder="Your phone number"
                      className="pricing-form__input"
                      required
                    />
                  </div>

                  <div className="pricing-form__field">
                    <label className="pricing-form__label">Industries *</label>
                    <select
                      name="industry"
                      value={formData.industry}
                      onChange={handleInputChange}
                      className="pricing-form__select"
                      required
                    >
                      <option value="">Choose Industry Type</option>
                      {industries.map((industry, index) => (
                        <option key={index} value={industry}>{industry}</option>
                      ))}
                    </select>
                  </div>

                  <div className="pricing-form__field">
                    <label className="pricing-form__label">Number of Employees *</label>
                    <select
                      name="employees"
                      value={formData.employees}
                      onChange={handleInputChange}
                      className="pricing-form__select"
                      required
                    >
                      <option value="">Select company size</option>
                      {employeeSizes.map((size, index) => (
                        <option key={index} value={size}>{size}</option>
                      ))}
                    </select>
                  </div>

                  <div className="pricing-form__policy">
                    <p className="pricing-form__policy-text">
                      Telescope's use and transfer to any other app of information received from Google APIs will adhere to Google API Services User Data Policy, including the Limited Use requirements. TelescopeHR does not share any user data with third-party tools, including AI models.
                    </p>
                  </div>

                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="pricing-form__submit"
                  >
                    {isSubmitting ? 'Sending...' : 'Get My Quote'}
                    <svg className="pricing-form__submit-icon" viewBox="0 0 24 24" fill="none">
                      <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </button>

                  {submitStatus === 'success' && (
                    <div className="pricing-form__success">
                      Thank you! We'll be in touch with your custom quote soon.
                    </div>
                  )}

                  {submitStatus === 'error' && (
                    <div className="pricing-form__error">
                      Something went wrong. Please try again.
                    </div>
                  )}
                </form>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className={`pricing-faq ${isFaqVisible ? 'pricing-faq--visible' : ''}`} ref={faqRef}>
        <div className="pricing-faq__container">
          <div className="pricing-faq__header">
            <h2 className="pricing-faq__title">Frequently Asked Questions</h2>
            <p className="pricing-faq__subtitle">
              Everything you need to know about Telescope pricing and implementation.
            </p>
          </div>

          <div className="pricing-faq__list">
            {faqs.map((faq, index) => (
              <div key={index} className="pricing-faq__item">
                <h3 className="pricing-faq__question">{faq.question}</h3>
                <p className="pricing-faq__answer">{faq.answer}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className={`pricing-cta ${isCtaVisible ? 'pricing-cta--visible' : ''}`} ref={ctaRef}>
        <div className="pricing-cta__container">
          <div className="pricing-cta__content">
            <h2 className="pricing-cta__title">Ready to Transform Your Hiring Process?</h2>
            <p className="pricing-cta__subtitle">
              See Telescope in action with a personalized demo tailored to your company's needs.
            </p>
            <button
              className="pricing-cta__button"
              onClick={() => navigate('/contact')}
            >
              <svg className="pricing-cta__icon" viewBox="0 0 24 24" fill="none">
                <path d="M8 2v4M16 2v4M3 10h18M5 4h14a2 2 0 012 2v14a2 2 0 01-2 2H5a2 2 0 01-2-2V6a2 2 0 012-2z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Book Your Demo
            </button>
            <div className="pricing-cta__features">
              <div className="pricing-cta__feature">
                <svg className="pricing-cta__feature-icon" viewBox="0 0 24 24" fill="none">
                  <path d="M20 6L9 17l-5-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span>No setup fees</span>
              </div>
              <div className="pricing-cta__feature">
                <svg className="pricing-cta__feature-icon" viewBox="0 0 24 24" fill="none">
                  <path d="M20 6L9 17l-5-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span>14-day free trial</span>
              </div>
              <div className="pricing-cta__feature">
                <svg className="pricing-cta__feature-icon" viewBox="0 0 24 24" fill="none">
                  <path d="M20 6L9 17l-5-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span>24/7 support</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Pricing;
