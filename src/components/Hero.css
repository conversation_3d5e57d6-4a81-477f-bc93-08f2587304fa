/* @import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap'); */

/* Global Text Selection */
::selection {
  background-color: #006ac1;
  color: white;
}

::-moz-selection {
  background-color: #006ac1;
  color: white;
}

/* Hero Section - Ultra Lightweight */
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #fafbff 0%, #f8faff 50%, #f5f7ff 100%);
  font-family: "Plus Jakarta Sans", sans-serif;
  overflow: hidden;
  padding: 120px 0 80px;
}

.hero__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
  z-index: 2;
}

/* Hero Content - Minimal Animation */
.hero__content {
  max-width: 900px;
  margin-bottom: 80px;
  opacity: 0;
  animation: fadeIn 0.8s ease-out 0.3s forwards;
}

.hero__content--loaded {
  animation-play-state: running;
}



/* Bold Animated Headline */
.hero__headline-wrapper {
  position: relative;
  margin-bottom: 32px;
}

.hero__headline {
  font-size: clamp(3rem, 6vw, 5rem);
  font-weight: 600;
  line-height: 1.3;
  margin: 0;
  letter-spacing: -0.02em;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.3em;
}

.hero__headline-word {
  display: inline-block;
  /* background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 50%, #3490e1 100%); */
  background: linear-gradient(135deg, #201c44 0%, #2d2654 50%, #3a3366 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  opacity: 0;
  animation: fadeInUp 0.6s ease-out forwards;
}

.hero__headline-word--1 { animation-delay: 0.1s; }
.hero__headline-word--2 { animation-delay: 0.2s; }
.hero__headline-word--3 { animation-delay: 0.3s; }
.hero__headline-word--4 { animation-delay: 0.4s; }
.hero__headline-word--5 { animation-delay: 0.5s; }
.hero__headline-word--6 { animation-delay: 0.6s; }

.hero__headline-underline {
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 4px;
  background: linear-gradient(90deg, #006ac1, #3490e1, #006ac1);
  border-radius: 2px;
  animation: underlineGrow 0.8s ease-out 0.8s forwards;
}

/* Simplified Subheadline */
.hero__subheadline {
  margin-bottom: 40px;
  opacity: 0;
  animation: fadeIn 0.8s ease-out 0.8s forwards;
}

.hero__tagline {
  font-size: clamp(1.5rem, 3vw, 2rem);
  font-weight: 500;
  color: #374151;
  margin: 0 0 20px 0;
  letter-spacing: -0.01em;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.5em;
}

.hero__tagline-part {
  display: inline-block;
  opacity: 0;
  animation: fadeIn 0.6s ease-out forwards;
}

.hero__tagline-part--1 { animation-delay: 1s; }
.hero__tagline-part--2 { animation-delay: 1.2s; }
.hero__tagline-part--3 { animation-delay: 1.4s; }

.hero__description {
  font-size: clamp(1.125rem, 2.2vw, 1.375rem);
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
  letter-spacing: -0.005em;
  font-weight: 400;
  opacity: 0;
  animation: fadeIn 0.6s ease-out 1.4s forwards;
}

/* Simplified CTA Section */
.hero__cta {
  margin-bottom: 40px;
  opacity: 0;
  animation: fadeIn 0.6s ease-out 1.6s forwards;
}

.hero__cta-btn {
  display: inline-flex;
  align-items: center;
  gap: 16px;
  padding: 20px 40px;
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 30%, #3490e1 60%, #4ea3f1 100%);
  color: white;
  border: none;
  border-radius: 60px;
  font-weight: 500;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 30px rgba(32, 28, 68, 0.3), 0 4px 12px rgba(32, 28, 68, 0.2);
  letter-spacing: -0.005em;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  transform: scale(1);
}

.hero__cta-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #1a7dd1 0%, #3490e1 30%, #4ea3f1 60%, #68b6ff 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.hero__cta-btn:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 15px 40px rgba(32, 28, 68, 0.4), 0 8px 20px rgba(32, 28, 68, 0.25);
}

.hero__cta-btn:hover::before {
  opacity: 1;
}

.hero__cta-btn:active {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(32, 28, 68, 0.3);
}

.hero__cta-text,
.hero__cta-icon-wrapper {
  position: relative;
  z-index: 1;
}

.hero__cta-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transition: all 0.4s ease;
}

.hero__cta-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.hero__cta-btn:hover .hero__cta-icon {
  transform: translateX(4px) rotate(5deg);
}

.hero__cta-btn:hover .hero__cta-icon-wrapper {
  background: rgba(255, 255, 255, 0.3);
  transform: rotate(360deg);
}

/* Simplified Ripple Effect */
.hero__cta-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: ripple 4s infinite;
}



/* Enhanced Trust Indicators */
.hero__trust-indicators {
  display: flex;
  gap: 32px;
  margin-top: 40px;
  justify-content: center;
  flex-wrap: wrap;
}

.hero__trust-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 1px solid rgba(32, 28, 68, 0.1);
  box-shadow: 0 2px 10px rgba(32, 28, 68, 0.08);
  opacity: 0;
  animation: fadeIn 0.8s ease-out forwards;
  transition: all 0.2s ease;
}

.hero__trust-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(32, 28, 68, 0.12);
}

.hero__trust-item:nth-child(1) { animation-delay: 2s; }
.hero__trust-item:nth-child(2) { animation-delay: 2.2s; }
.hero__trust-item:nth-child(3) { animation-delay: 2.4s; }

.hero__trust-icon {
  width: 24px;
  height: 24px;
  color: #006ac1;
  margin-bottom: 8px;
}



.hero__trust-number {
  font-size: 20px;
  font-weight: 600;
  color: #006ac1;
  line-height: 1;
  margin-bottom: 4px;
}

.hero__trust-label {
  font-size: 11px;
  color: #6b7280;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
}

/* Professional Badges */
.hero__badges {
  display: flex;
  gap: 12px;
  margin-top: 24px;
  justify-content: center;
  flex-wrap: wrap;
  opacity: 0;
  transform: translateY(20px);
  animation: slideUpFade 0.8s cubic-bezier(0.4, 0, 0.2, 1) 2.6s forwards;
}

.hero__badge {
  position: relative;
  padding: 8px 16px;
  background: linear-gradient(135deg, rgba(32, 28, 68, 0.05) 0%, rgba(45, 38, 84, 0.08) 100%);
  border: 1px solid rgba(32, 28, 68, 0.15);
  border-radius: 25px;
  backdrop-filter: blur(10px);
  overflow: hidden;
  transition: all 0.3s ease;
}

.hero__badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(32, 28, 68, 0.15);
  border-color: rgba(32, 28, 68, 0.25);
}

.hero__badge-text {
  font-size: 10px;
  font-weight: 500;
  color: #006ac1;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  position: relative;
  z-index: 1;
}

.hero__badge-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: badgeShine 3s infinite;
}

/* Simplified Software Image */
.hero__image {
  width: 100%;
  max-width: 900px;
  position: relative;
  opacity: 0;
  animation: imageReveal 0.8s ease-out 1.8s forwards;
}

.hero__image--loaded {
  animation-play-state: running;
}

.hero__image-container {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(32, 28, 68, 0.15), 0 8px 16px rgba(32, 28, 68, 0.1);
  background: white;
  padding: 0;
  transition: all 0.3s ease;
}

.hero__image-container:hover {
  transform: translateY(-4px);
  box-shadow: 0 25px 50px rgba(32, 28, 68, 0.2), 0 10px 20px rgba(32, 28, 68, 0.12);
}

/* Mac-style Header */
.hero__mac-header {
  position: relative;
  background: linear-gradient(180deg, #f6f6f6 0%, #e8e8e8 100%);
  border-bottom: 1px solid #d1d1d1;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-radius: 20px 20px 0 0;
}

.hero__mac-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.hero__mac-button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 0.5px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.hero__mac-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.hero__mac-button--close {
  background: linear-gradient(135deg, #ff5f57 0%, #ff4444 100%);
}

.hero__mac-button--close:hover::before {
  content: '×';
  color: #8b0000;
  font-size: 10px;
  font-weight: bold;
  opacity: 1;
}

.hero__mac-button--minimize {
  background: linear-gradient(135deg, #ffbd2e 0%, #ffaa00 100%);
}

.hero__mac-button--minimize:hover::before {
  content: '−';
  color: #8b4500;
  font-size: 10px;
  font-weight: bold;
  opacity: 1;
}

.hero__mac-button--maximize {
  background: linear-gradient(135deg, #28ca42 0%, #20a034 100%);
}

.hero__mac-button--maximize:hover::before {
  content: '+';
  color: #0d4f1c;
  font-size: 10px;
  font-weight: bold;
  opacity: 1;
}

.hero__mac-button:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero__image-glow {
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  background: rgba(32, 28, 68, 0.05);
  border-radius: 30px;
  filter: blur(15px);
  z-index: -1;
}

.hero__software-img {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 0 0 16px 16px;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 8px;
  background: white;
}

.hero__image-container:hover .hero__software-img {
  transform: scale(1.03);
}

.hero__image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(32, 28, 68, 0.03) 0%, rgba(45, 38, 84, 0.01) 100%);
  border-radius: 20px;
  pointer-events: none;
}

/* Floating UI Elements */
.hero__floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.hero__floating-card {
  position: absolute;
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 14px 18px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: 16px;
  border: 1px solid rgba(32, 28, 68, 0.1);
  box-shadow: 0 12px 30px rgba(32, 28, 68, 0.15), 0 4px 8px rgba(32, 28, 68, 0.1);
  font-size: 13px;
  font-weight: 500;
  color: #006ac1;
  opacity: 0;
  animation: floatingCardReveal 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.hero__floating-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(32, 28, 68, 0.02) 0%, rgba(45, 38, 84, 0.04) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.hero__floating-card:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 16px 40px rgba(32, 28, 68, 0.2), 0 6px 12px rgba(32, 28, 68, 0.15);
}

.hero__floating-card:hover::before {
  opacity: 1;
}



.hero__floating-card--1 {
  top: 10%;
  left: -12%;
  animation-delay: 2.5s;
  animation-name: floatingCardReveal, floatUpDown;
  animation-duration: 1.2s, 4s;
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1), ease-in-out;
  animation-iteration-count: 1, infinite;
  animation-delay: 2.5s, 3.7s;
}

.hero__floating-card--2 {
  top: 55%;
  right: -10%;
  animation-delay: 3s;
  animation-name: floatingCardReveal, floatUpDown;
  animation-duration: 1.2s, 5s;
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1), ease-in-out;
  animation-iteration-count: 1, infinite;
  animation-delay: 3s, 4.2s;
}

.hero__floating-card--3 {
  bottom: 25%;
  left: -8%;
  animation-delay: 3.5s;
  animation-name: floatingCardReveal, floatUpDown;
  animation-duration: 1.2s, 6s;
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1), ease-in-out;
  animation-iteration-count: 1, infinite;
  animation-delay: 3.5s, 4.7s;
}

.hero__floating-card--4 {
  top: 30%;
  right: -15%;
  animation-delay: 4s;
  animation-name: floatingCardReveal, floatUpDown;
  animation-duration: 1.2s, 7s;
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1), ease-in-out;
  animation-iteration-count: 1, infinite;
  animation-delay: 4s, 5.2s;
}

.hero__floating-card--5 {
  bottom: 5%;
  right: -5%;
  animation-delay: 4.5s;
  animation-name: floatingCardReveal, floatUpDown;
  animation-duration: 1.2s, 5.5s;
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1), ease-in-out;
  animation-iteration-count: 1, infinite;
  animation-delay: 4.5s, 5.7s;
}

.hero__floating-icon {
  width: 22px;
  height: 22px;
  color: #006ac1;
  position: relative;
  z-index: 1;
  filter: drop-shadow(0 2px 4px rgba(32, 28, 68, 0.2));
}

/* Image Decorations */
.hero__image-decorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.hero__image-decoration {
  position: absolute;
  background: linear-gradient(45deg, rgba(32, 28, 68, 0.1), rgba(45, 38, 84, 0.05));
  border-radius: 50%;
  animation: decorationFloat 10s ease-in-out infinite;
}

.hero__image-decoration--1 {
  width: 30px;
  height: 30px;
  top: -15px;
  left: -15px;
  animation-delay: 0s;
}

.hero__image-decoration--2 {
  width: 25px;
  height: 25px;
  top: -10px;
  right: -10px;
  animation-delay: 2.5s;
}

.hero__image-decoration--3 {
  width: 35px;
  height: 35px;
  bottom: -15px;
  left: -15px;
  animation-delay: 5s;
}

.hero__image-decoration--4 {
  width: 20px;
  height: 20px;
  bottom: -10px;
  right: -10px;
  animation-delay: 7.5s;
}

/* Decorative Elements */
.hero__decorative-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.hero__glow-orb {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(32, 28, 68, 0.1) 0%, rgba(45, 38, 84, 0.05) 50%, transparent 100%);
  filter: blur(20px);
  animation: orbFloat 8s ease-in-out infinite;
}

.hero__glow-orb--1 {
  width: 200px;
  height: 200px;
  top: 20%;
  right: 10%;
  animation-delay: 0s;
}

.hero__glow-orb--2 {
  width: 150px;
  height: 150px;
  bottom: 30%;
  left: 15%;
  animation-delay: 4s;
}

.hero__geometric-shape {
  position: absolute;
  border: 2px solid rgba(32, 28, 68, 0.1);
  animation: shapeRotate 12s linear infinite;
}

.hero__geometric-shape--1 {
  width: 60px;
  height: 60px;
  top: 40%;
  right: 5%;
  transform: rotate(45deg);
  animation-delay: 0s;
}

.hero__geometric-shape--2 {
  width: 40px;
  height: 40px;
  bottom: 40%;
  left: 10%;
  border-radius: 50%;
  animation-delay: 6s;
}

/* Minimal Background Elements */
.hero__bg-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  overflow: hidden;
}

.hero__bg-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(32, 28, 68, 0.03);
}

.hero__bg-circle--1 {
  width: 150px;
  height: 150px;
  top: 10%;
  left: -5%;
}

.hero__bg-circle--2 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  right: -3%;
}

.hero__bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, rgba(32, 28, 68, 0.015) 0%, transparent 70%);
}





/* Lightweight Keyframe Animations */
@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes underlineGrow {
  to {
    width: 150px;
  }
}

@keyframes imageReveal {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes ripple {
  0% {
    width: 0;
    height: 0;
    opacity: 0.8;
  }
  100% {
    width: 200px;
    height: 200px;
    opacity: 0;
  }
}



/* New Ultra-Modern Animations */
@keyframes heroReveal {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes decorationFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes glowGrow {
  to {
    width: 300px;
  }
}

@keyframes ctaGlow {
  0%, 100% {
    opacity: 0;
  }
  50% {
    opacity: 0.3;
  }
}

@keyframes trustPulse {
  0%, 100% {
    opacity: 0;
    transform: scale(1);
  }
  50% {
    opacity: 0.2;
    transform: scale(1.05);
  }
}

@keyframes badgeShine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes floatingGlow {
  0%, 100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

@keyframes slideInRight {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  .hero {
    padding: 100px 0 60px;
    min-height: 90vh;
  }

  .hero__container {
    padding: 0 16px;
  }

  .hero__content {
    margin-bottom: 50px;
  }

  .hero__headline {
    gap: 0.2em;
  }

  .hero__headline-underline {
    bottom: -8px;
    height: 4px;
  }

  .hero__subheadline {
    margin-bottom: 36px;
  }

  .hero__tagline {
    flex-direction: column;
    gap: 0.2em;
  }

  .hero__cta-btn {
    padding: 16px 32px;
    font-size: 16px;
    gap: 12px;
  }

  .hero__trust-indicators {
    gap: 24px;
    margin-top: 24px;
  }

  .hero__trust-number {
    font-size: 20px;
  }

  .hero__image-container {
    border-radius: 16px;
    padding: 0;
  }

  .hero__software-img {
    border-radius: 0 0 12px 12px;
    padding: 6px;
  }

  .hero__mac-header {
    padding: 10px 12px;
    border-radius: 16px 16px 0 0;
  }

  .hero__mac-button {
    width: 10px;
    height: 10px;
  }

  .hero__mac-buttons {
    gap: 6px;
  }

  .hero__floating-card {
    padding: 8px 12px;
    font-size: 12px;
  }

  .hero__floating-icon {
    font-size: 14px;
  }

  .hero__bg-circle--1 {
    width: 250px;
    height: 250px;
  }

  .hero__bg-circle--2 {
    width: 200px;
    height: 200px;
  }

  .hero__bg-circle--3 {
    width: 150px;
    height: 150px;
  }
}

@media (max-width: 480px) {
  .hero {
    padding: 80px 0 40px;
  }

  .hero__container {
    padding: 0 12px;
  }

  .hero__content {
    margin-bottom: 40px;
  }

  .hero__headline-underline {
    bottom: -6px;
    height: 3px;
  }

  .hero__cta-btn {
    padding: 14px 28px;
    font-size: 15px;
    gap: 10px;
  }

  .hero__cta-icon-wrapper {
    width: 20px;
    height: 20px;
  }

  .hero__cta-icon {
    width: 14px;
    height: 14px;
  }

  .hero__trust-indicators {
    gap: 20px;
  }

  .hero__trust-number {
    font-size: 18px;
  }

  .hero__trust-label {
    font-size: 11px;
  }

  .hero__floating-card {
    display: none; /* Hide floating cards on very small screens */
  }

  .hero__performance-indicators {
    bottom: 20px;
    gap: 12px;
  }

  .hero__performance-item {
    padding: 6px 12px;
    font-size: 11px;
  }

  .hero__floating-actions {
    display: none;
  }
}


