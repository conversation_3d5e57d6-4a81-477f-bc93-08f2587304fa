import { useEffect, useState } from 'react';
import './TrustedBy.css';

const TrustedBy = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    const section = document.querySelector('.trusted-by');
    if (section) {
      observer.observe(section);
    }

    return () => {
      if (section) {
        observer.unobserve(section);
      }
    };
  }, []);

  // Company logos/names for the marquee - using actual agency logos
  const companies = [
    { name: "Endearing Care Services", logo: "/agencies/Endearing Care Services.png" },
    { name: "Fairbranch Health Services", logo: "/agencies/Fairbranch Health Services.png" },
    { name: "Genuine Global Care", logo: "/agencies/Genuine Global Care.png" },
    { name: "Heart to Hart", logo: "/agencies/Heart to Hart.png" },
    { name: "<PERSON>'s Heart Homecare", logo: "/agencies/Helen's Heart Homecare.png" },
    { name: "Helping Hands Superior Care", logo: "/agencies/Helping Hands Superior Care.png" },
    { name: "In the Beginning", logo: "/agencies/In the Beginning Logo.jpg" },
    { name: "Infinito", logo: "/agencies/Infinito Logo.png" },
    { name: "Key to Care", logo: "/agencies/Key to Care.png" },
    { name: "KM Home Care", logo: "/agencies/KM Home Care Logo.png" },
    { name: "Life View Homecare Services", logo: "/agencies/Life View Homecare Services.jpeg" },
    { name: "Light Bearers Home Care", logo: "/agencies/Light Bearers Home Care.png" },
    { name: "Medlink Homecare", logo: "/agencies/Medlink Homecare.jpg" },
    { name: "Perfect Touch", logo: "/agencies/Perfect Touch Logo.jpg" },
    { name: "Positive Home Care Services", logo: "/agencies/Positive Home Care Services (1).png" },
    { name: "RNJ Oasis Homecare Services", logo: "/agencies/RNJ Oasis Homecare Services.png" },
    { name: "Royal Faith", logo: "/agencies/Royal Faith.png" },
    { name: "S&N Home Care LLC", logo: "/agencies/S&N Home Care LLC Logo.png" },
    { name: "Utopia Hospice", logo: "/agencies/Utopia Hospice (3).png" }
  ];

  return (
    <section className={`trusted-by ${isVisible ? 'trusted-by--visible' : ''}`}>
      <div className="trusted-by__container">
        <div className="trusted-by__header">
          <h2 className="trusted-by__title">Trusted by 500 agencies</h2>
        </div>

        <div className="trusted-by__marquee">
          <div className="trusted-by__marquee-track">
            {/* First set of companies */}
            {companies.map((company, index) => (
              <div key={`first-${index}`} className="trusted-by__company">
                <div className="trusted-by__company-logo">
                  <img
                    src={company.logo}
                    alt={`${company.name} logo`}
                    className="trusted-by__company-logo-img"
                  />
                </div>
                <span className="trusted-by__company-name">{company.name}</span>
              </div>
            ))}

            {/* Duplicate set for seamless loop */}
            {companies.map((company, index) => (
              <div key={`second-${index}`} className="trusted-by__company">
                <div className="trusted-by__company-logo">
                  <img
                    src={company.logo}
                    alt={`${company.name} logo`}
                    className="trusted-by__company-logo-img"
                  />
                </div>
                <span className="trusted-by__company-name">{company.name}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TrustedBy;
