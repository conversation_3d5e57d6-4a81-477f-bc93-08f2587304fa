import './FeatureHowItWorks.css';

const FeatureHowItWorks = ({ 
  title = "How It Works",
  subtitle = "Get started with EVV compliance in three simple steps",
  steps = [],
  cta = {
    title: "Built for Compliance",
    description: "Whether you're in Texas, California, or any EVV-required state — Telescope ensures your agency remains fully compliant and audit-ready.",
    buttonText: "Ready to simplify EVV and protect your agency?"
  }
}) => {
  if (!steps || steps.length === 0) return null;

  return (
    <section className="feature-how-it-works">
      <div className="feature-how-it-works__container">
        <div className="feature-how-it-works__header">
          <h2 className="feature-how-it-works__title">{title}</h2>
          <p className="feature-how-it-works__subtitle">{subtitle}</p>
        </div>

        <div className="feature-how-it-works__steps">
          {steps.map((step, index) => (
            <div key={index} className="feature-how-it-works__step">
              <div className="feature-how-it-works__step-number">
                {index + 1}
              </div>
              <div className="feature-how-it-works__step-content">
                <h3 className="feature-how-it-works__step-title">
                  Step {index + 1}
                </h3>
                <p className="feature-how-it-works__step-description">
                  {step}
                </p>
              </div>
            </div>
          ))}
        </div>

        {cta && (
          <div className="feature-how-it-works__cta">
            <div className="feature-how-it-works__cta-content">
              <h3 className="feature-how-it-works__cta-title">
                {cta.title}
              </h3>
              <p className="feature-how-it-works__cta-description">
                {cta.description}
              </p>
              <button className="feature-how-it-works__cta-btn">
                {cta.buttonText}
                <svg className="feature-how-it-works__cta-icon" viewBox="0 0 24 24" fill="none">
                  <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default FeatureHowItWorks;
