@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap');

/* Grouped Features Section - Bento Grid */
.grouped-features {
  padding: 120px 0;
  background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
  font-family: "Plus Jakarta Sans", sans-serif;
  position: relative;
  transition: all 0.3s ease;
}

.grouped-features--hijacked {
  background: linear-gradient(135deg, #f0f4ff 0%, #e5e7eb 100%);
}

.grouped-features__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.grouped-features__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6rem;
  align-items: center;
}

/* Left Side: Summary */
.grouped-features__summary {
  padding: 2rem 0;
}

.grouped-features__badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: linear-gradient(135deg, rgba(0, 106, 193, 0.1) 0%, rgba(0, 106, 193, 0.05) 100%);
  color: #006ac1;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(0, 106, 193, 0.1);
}

.grouped-features__badge-icon {
  font-size: 16px;
  line-height: 1;
}

.grouped-features__title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 400;
  color: #1a1a1a;
  margin: 0 0 1.5rem 0;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.grouped-features__description {
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.7;
  margin: 0 0 2rem 0;
}

.grouped-features__cta {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  font-family: "Plus Jakarta Sans", sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  letter-spacing: -0.01em;
}

.grouped-features__cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(32, 28, 68, 0.3);
}

.grouped-features__cta-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;
}

.grouped-features__cta:hover .grouped-features__cta-icon {
  transform: translateX(2px);
}

/* Statistics Section */
.grouped-features__stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  margin: 2rem 0;
  padding: 2rem 0;
  border-top: 1px solid rgba(32, 28, 68, 0.1);
  border-bottom: 1px solid rgba(32, 28, 68, 0.1);
}

.grouped-features__stat {
  text-align: center;
  opacity: 0;
  transform: translateY(20px);
  animation: statSlideIn 0.6s ease-out forwards;
}

.grouped-features--visible .grouped-features__stat:nth-child(1) {
  animation-delay: 0.3s;
}

.grouped-features--visible .grouped-features__stat:nth-child(2) {
  animation-delay: 0.5s;
}

@keyframes statSlideIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.grouped-features__stat-number {
  font-size: 2.5rem;
  font-weight: 600;
  color: #006ac1;
  line-height: 1;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.grouped-features__stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Right Side: Bento Grid Container */
.grouped-features__bento-container {
  position: relative;
  height: 600px;
  overflow: hidden;
  border-radius: 24px;
  transition: all 0.3s ease;
}

.grouped-features--hijacked .grouped-features__bento-container {
  box-shadow: 0 0 0 2px rgba(32, 28, 68, 0.1);
}

/* Scroll Indicator */
.grouped-features__scroll-indicator {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  z-index: 3;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 12px 16px;
  border: 1px solid rgba(32, 28, 68, 0.1);
  animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.grouped-features__scroll-text {
  font-size: 0.875rem;
  color: #006ac1;
  font-weight: 500;
  margin-bottom: 8px;
  text-align: center;
}

.grouped-features__scroll-progress {
  height: 4px;
  background: rgba(32, 28, 68, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.grouped-features__scroll-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #006ac1 0%, #1a7dd1 100%);
  border-radius: 2px;
  transition: width 0.1s ease-out;
}

/* Blur Effects */
.grouped-features__bento-blur {
  position: absolute;
  left: 0;
  right: 0;
  height: 80px;
  z-index: 2;
  pointer-events: none;
}

.grouped-features__bento-blur--top {
  top: 0;
  background: linear-gradient(180deg, rgba(248, 250, 255, 0.9) 0%, rgba(248, 250, 255, 0.7) 50%, transparent 100%);
  backdrop-filter: blur(8px);
}

.grouped-features__bento-blur--bottom {
  bottom: 0;
  background: linear-gradient(0deg, rgba(248, 250, 255, 0.9) 0%, rgba(248, 250, 255, 0.7) 50%, transparent 100%);
  backdrop-filter: blur(8px);
}

/* Bento Grid */
.grouped-features__bento-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(8, 1fr);
  gap: 16px;
  height: 100%;
  padding: 20px 20px 120px 20px;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  scroll-behavior: smooth;
  position: relative;
}

.grouped-features__bento-grid::-webkit-scrollbar {
  display: none;
}

/* Add scroll indicator */
.grouped-features__bento-grid::after {
  content: '';
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 4px;
  background: rgba(32, 28, 68, 0.2);
  border-radius: 2px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.grouped-features--hijacked .grouped-features__bento-grid::after {
  opacity: 1;
}

/* Card Sizes */
.grouped-features__card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(32, 28, 68, 0.08);
  border: 1px solid rgba(32, 28, 68, 0.06);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  opacity: 0;
  transform: translateY(30px);
  animation: cardSlideIn 0.6s ease-out forwards;
}

.grouped-features--visible .grouped-features__card {
  animation-play-state: running;
}

@keyframes cardSlideIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.grouped-features__card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(32, 28, 68, 0.15);
  border-color: rgba(32, 28, 68, 0.12);
}

/* Card Size Variants */
.grouped-features__card--small {
  grid-column: span 1;
  grid-row: span 1;
}

.grouped-features__card--medium {
  grid-column: span 2;
  grid-row: span 1;
}

.grouped-features__card--large {
  grid-column: span 2;
  grid-row: span 2;
}

/* Card Content */
.grouped-features__card-icon {
  font-size: 2rem;
  margin-bottom: 12px;
  line-height: 1;
}

.grouped-features__card--large .grouped-features__card-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.grouped-features__card-title {
  font-size: 1rem;
  font-weight: 600;
  color: #006ac1;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.grouped-features__card--large .grouped-features__card-title {
  font-size: 1.25rem;
  margin-bottom: 12px;
}

.grouped-features__card-description {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.4;
  margin: 0;
}

.grouped-features__card--large .grouped-features__card-description {
  font-size: 1rem;
  line-height: 1.5;
}

/* Visibility Animation */
.grouped-features {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.grouped-features--visible {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .grouped-features {
    padding: 80px 0;
  }

  .grouped-features__container {
    padding: 0 16px;
  }

  .grouped-features__content {
    grid-template-columns: 1fr;
    gap: 4rem;
  }

  .grouped-features__summary {
    text-align: center;
  }

  .grouped-features__stats {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin: 1.5rem 0;
    padding: 1.5rem 0;
  }

  .grouped-features__stat-number {
    font-size: 2rem;
  }

  .grouped-features__bento-container {
    height: 500px;
  }

  .grouped-features__bento-grid {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(6, 1fr);
    gap: 12px;
    padding: 16px;
  }

  .grouped-features__card--medium,
  .grouped-features__card--large {
    grid-column: span 1;
    grid-row: span 1;
  }
}

@media (max-width: 480px) {
  .grouped-features {
    padding: 60px 0;
  }

  .grouped-features__content {
    gap: 3rem;
  }

  .grouped-features__stats {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin: 1rem 0;
    padding: 1rem 0;
  }

  .grouped-features__stat-number {
    font-size: 1.75rem;
  }

  .grouped-features__stat-label {
    font-size: 0.75rem;
  }

  .grouped-features__bento-container {
    height: 400px;
  }

  .grouped-features__bento-grid {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(9, 1fr);
    gap: 10px;
    padding: 12px;
  }

  .grouped-features__card--small,
  .grouped-features__card--medium,
  .grouped-features__card--large {
    grid-column: span 1;
    grid-row: span 1;
  }
}
