import { useEffect, useState } from 'react';
import { getImage } from '../utils/images';
import './WhyTelescope.css';

const WhyTelescope = () => {
  const [isVisible, setIsVisible] = useState(false);
  const mainImage = getImage('whyTelescope', 'mainImage');


  console.log(mainImage, 'lets test')

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    const section = document.querySelector('.why-telescope');
    if (section) {
      observer.observe(section);
    }

    return () => {
      if (section) {
        observer.unobserve(section);
      }
    };
  }, []);

  return (
    <section className={`why-telescope ${isVisible ? 'why-telescope--visible' : ''}`}>
      <div className="why-telescope__container">
        <div className="why-telescope__content">
          <div className="why-telescope__text">
            <div className="why-telescope__label">
              Why Telescope
            </div>
  
  

            <h2 className="why-telescope__headline">
              Home care software that works as hard as you do.
            </h2>

            <p className="why-telescope__body">
              Whether you're managing visits, hiring caregivers, or keeping clients happy — Telescope streamlines it all.
            </p>
          </div>

          <div className="why-telescope__image">
            <div className="why-telescope__image-container">
              {/* Mac-style Header */}
              <div className="why-telescope__mac-header">
                <div className="why-telescope__mac-buttons">
                  <div className="why-telescope__mac-button why-telescope__mac-button--close"></div>
                  <div className="why-telescope__mac-button why-telescope__mac-button--minimize"></div>
                  <div className="why-telescope__mac-button why-telescope__mac-button--maximize"></div>
                </div>
              </div>

              <img
                src={mainImage.src }
                alt={mainImage.alt}
                className="why-telescope__main-image"
                // onLoad={(e) => {

                //   console.log(mainImage.src !== mainImage.placeholder, mainImage.src, mainImage.placeholder)
                //   // Replace with actual image when available
                //   if (mainImage.src !== mainImage.placeholder) {
                //     e.target.src = mainImage.src;
                //   }
                // }}
                // onError={(e) => {
                //   // Fallback to placeholder if actual image fails
                //   e.target.src = mainImage.placeholder;
                // }}
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhyTelescope;
