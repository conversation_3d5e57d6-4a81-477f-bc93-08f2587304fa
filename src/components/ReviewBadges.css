@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap');

/* Review Badges Section - Clean & Trustworthy */
.review-badges {
  position: relative;
  padding: 60px 0;
  background: #ffffff;
  font-family: "Plus Jakarta Sans", sans-serif;
  border-top: 1px solid rgba(32, 28, 68, 0.08);
  border-bottom: 1px solid rgba(32, 28, 68, 0.08);
}

.review-badges__container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 24px;
  text-align: center;
}

/* Header */
.review-badges__header {
  margin-bottom: 40px;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s ease-out;
}

.review-badges--visible .review-badges__header {
  opacity: 1;
  transform: translateY(0);
}

.review-badges__title {
  font-size: 18px;
  font-weight: 500;
  color: #6b7280;
  margin: 0;
  letter-spacing: -0.005em;
}

/* Badges Grid */
.review-badges__grid {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 60px;
  flex-wrap: wrap;
}

.review-badges__item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px 24px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(32, 28, 68, 0.08);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(32, 28, 68, 0.06);
  opacity: 0;
  transform: translateY(30px);
  animation: badgeReveal 0.8s ease-out forwards;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.review-badges--visible .review-badges__item {
  animation-play-state: running;
}

.review-badges__item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 106, 193, 0.12);
  border-color: rgba(0, 106, 193, 0.15);
}

/* Badge Content */
.review-badges__logo {
  font-size: 32px;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, rgba(0, 106, 193, 0.05) 0%, rgba(26, 125, 209, 0.08) 100%);
  border-radius: 12px;
  border: 1px solid rgba(0, 106, 193, 0.1);
}

.review-badges__info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.review-badges__name {
  font-size: 16px;
  font-weight: 600;
  color: #006ac1;
  line-height: 1.2;
  letter-spacing: -0.01em;
}

.review-badges__stars {
  display: flex;
  align-items: center;
  gap: 8px;
}

.review-badges__rating {
  color: #fbbf24;
  font-size: 14px;
  line-height: 1;
}

.review-badges__score {
  font-size: 13px;
  font-weight: 500;
  color: #6b7280;
}

/* Animations */
@keyframes badgeReveal {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .review-badges {
    padding: 40px 0;
  }
  
  .review-badges__container {
    padding: 0 16px;
  }
  
  .review-badges__header {
    margin-bottom: 32px;
  }
  
  .review-badges__grid {
    gap: 24px;
    flex-direction: column;
  }
  
  .review-badges__item {
    padding: 16px 20px;
    width: 100%;
    max-width: 280px;
  }
  
  .review-badges__logo {
    font-size: 28px;
    width: 44px;
    height: 44px;
  }
  
  .review-badges__title {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .review-badges {
    padding: 32px 0;
  }
  
  .review-badges__container {
    padding: 0 12px;
  }
  
  .review-badges__header {
    margin-bottom: 24px;
  }
  
  .review-badges__grid {
    gap: 16px;
  }
  
  .review-badges__item {
    padding: 12px 16px;
  }
  
  .review-badges__logo {
    font-size: 24px;
    width: 40px;
    height: 40px;
  }
  
  .review-badges__name {
    font-size: 15px;
  }
  
  .review-badges__rating {
    font-size: 13px;
  }
  
  .review-badges__score {
    font-size: 12px;
  }
}
