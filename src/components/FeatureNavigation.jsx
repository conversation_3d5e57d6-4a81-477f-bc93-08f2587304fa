import { useRef } from 'react';
import { useScrollButtons, useStickyNavigation } from '../hooks/useParallax';
import './FeatureNavigation.css';

const FeatureNavigation = ({ 
  features, 
  activeTab, 
  onTabChange,
  title = "All Features",
  subtitle = "Discover how Telescope's 13 core features transform home care operations"
}) => {
  const tabsContainerRef = useRef(null);
  const tabsStickyRef = useRef(null);
  
  const {
    canScrollLeft,
    canScrollRight,
    scrollLeft,
    scrollRight
  } = useScrollButtons(tabsContainerRef);
  
  const isTabsScrolled = useStickyNavigation('.features-nav');

  return (
    <>
      {/* Features Navigation Header */}
      <div className="features-nav">
        <div className="features-nav__container">
          <div className="features-nav__header">
            <h1 className="features-nav__title">{title}</h1>
            <p className="features-nav__subtitle">{subtitle}</p>
          </div>
        </div>
      </div>

      {/* Sticky Tabs Wrapper */}
      <div
        className={`features-nav__tabs-sticky ${isTabsScrolled ? 'scrolled' : ''}`}
        ref={tabsStickyRef}
      >
        <div className="features-nav__container">
          <div className="features-nav__tabs">
            <div className="features-nav__tabs-wrapper">
              {/* Left scroll button */}
              <button
                className="features-nav__scroll-btn"
                onClick={scrollLeft}
                disabled={!canScrollLeft}
                aria-label="Scroll tabs left"
              >
                <svg viewBox="0 0 24 24" fill="none">
                  <path d="M15 18l-6-6 6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>

              {/* Tabs container */}
              <div
                className="features-nav__tabs-container"
                ref={tabsContainerRef}
              >
                {features.map((feature, index) => (
                  <button
                    key={feature.id}
                    className={`features-nav__tab ${activeTab === index ? 'features-nav__tab--active' : ''}`}
                    onClick={() => onTabChange(index)}
                  >
                    {feature.title}
                  </button>
                ))}
              </div>

              {/* Right scroll button */}
              <button
                className="features-nav__scroll-btn"
                onClick={scrollRight}
                disabled={!canScrollRight}
                aria-label="Scroll tabs right"
              >
                <svg viewBox="0 0 24 24" fill="none">
                  <path d="M9 18l6-6-6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default FeatureNavigation;
