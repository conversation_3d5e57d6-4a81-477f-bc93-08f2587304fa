import { useState, useEffect, useRef } from 'react';
import './ContactUs.css';

const ContactUs = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    phone: '',
    message: '',
    subject: 'General Inquiry'
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);

  // Dynamic Island states
  const [dynamicIslandState, setDynamicIslandState] = useState('default');
  const [islandContent, setIslandContent] = useState(null);

  // Animation states
  const [isHeroVisible, setIsHeroVisible] = useState(false);
  const [isContentVisible, setIsContentVisible] = useState(false);
  const [isActionsVisible, setIsActionsVisible] = useState(false);

  // Refs for intersection observer
  const heroRef = useRef(null);
  const contentRef = useRef(null);
  const actionsRef = useRef(null);

  // Dynamic Island content states
  const islandStates = [
    {
      type: 'default',
      duration: 3000,
      content: null
    },
    {
      type: 'call',
      duration: 4000,
      content: {
        title: 'Incoming Call',
        subtitle: 'Telescope Support',
        icon: '📞',
        color: '#00C851'
      }
    },
    {
      type: 'notification',
      duration: 3500,
      content: {
        title: 'New Message',
        subtitle: 'Demo scheduled!',
        icon: '💬',
        color: '#007AFF'
      }
    },
    {
      type: 'music',
      duration: 4000,
      content: {
        title: 'On Hold Music',
        subtitle: 'Connecting...',
        icon: '🎵',
        color: '#FF6B35'
      }
    },
    {
      type: 'timer',
      duration: 3000,
      content: {
        title: 'Demo Timer',
        subtitle: '15 min remaining',
        icon: '⏱️',
        color: '#FF9500'
      }
    }
  ];

  useEffect(() => {
    let currentIndex = 0;

    const cycleIsland = () => {
      const currentState = islandStates[currentIndex];
      setDynamicIslandState(currentState.type);
      setIslandContent(currentState.content);

      setTimeout(() => {
        currentIndex = (currentIndex + 1) % islandStates.length;
        cycleIsland();
      }, currentState.duration);
    };

    // Start cycling after initial load
    const initialDelay = setTimeout(cycleIsland, 2000);

    return () => clearTimeout(initialDelay);
  }, []);

  // Intersection Observer for animations
  useEffect(() => {
    const observerOptions = {
      threshold: 0.2,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          if (entry.target === heroRef.current) {
            setIsHeroVisible(true);
          } else if (entry.target === contentRef.current) {
            setIsContentVisible(true);
          } else if (entry.target === actionsRef.current) {
            setIsActionsVisible(true);
          }
        }
      });
    }, observerOptions);

    if (heroRef.current) observer.observe(heroRef.current);
    if (contentRef.current) observer.observe(contentRef.current);
    if (actionsRef.current) observer.observe(actionsRef.current);

    // Initial hero animation
    setTimeout(() => setIsHeroVisible(true), 100);

    return () => {
      if (heroRef.current) observer.unobserve(heroRef.current);
      if (contentRef.current) observer.unobserve(contentRef.current);
      if (actionsRef.current) observer.unobserve(actionsRef.current);
    };
  }, []);

  const handleIslandClick = () => {
    // Cycle to next state immediately when clicked
    const currentIndex = islandStates.findIndex(state => state.type === dynamicIslandState);
    const nextIndex = (currentIndex + 1) % islandStates.length;
    const nextState = islandStates[nextIndex];

    setDynamicIslandState(nextState.type);
    setIslandContent(nextState.content);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSubmitStatus('success');
      setFormData({
        name: '',
        email: '',
        company: '',
        phone: '',
        message: '',
        subject: 'General Inquiry'
      });
    } catch (error) {
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="contact-us">
      {/* Hero Section */}
      <section className={`contact-hero ${isHeroVisible ? 'contact-hero--visible' : ''}`} ref={heroRef}>
        <div className="contact-hero__container">
          <div className="contact-hero__grid">
            <div className="contact-hero__content">
              <div className="contact-hero__badge">
                <svg className="contact-hero__badge-icon" viewBox="0 0 24 24" fill="none">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                Let's Connect
              </div>
              <h1 className="contact-hero__title">Reach out, we love to chat</h1>
              <p className="contact-hero__subtitle">
                We're a lively team eager to help, whether it's for hiring or just a friendly hello.
                Get in touch – we promise top-notch service with a side of cheer!
              </p>

              {/* Quick Stats */}
              <div className="contact-hero__stats">
                <div className="contact-hero__stat">
                  <div className="contact-hero__stat-number"> &lt; 24hrs</div>
                  <div className="contact-hero__stat-label">Response Time</div>
                </div>
                <div className="contact-hero__stat">
                  <div className="contact-hero__stat-number">50,000+</div>
                  <div className="contact-hero__stat-label">Happy Clients</div>
                </div>
                <div className="contact-hero__stat">
                  <div className="contact-hero__stat-number">24/7</div>
                  <div className="contact-hero__stat-label">Support Available</div>
                </div>
              </div>
            </div>

            <div className="contact-hero__visual">
              <div className="contact-hero__image-container">
                <div className="contact-hero__floating-card contact-hero__floating-card--1">
                  <div className="contact-hero__card-icon">
                    <svg viewBox="0 0 24 24" fill="none">
                      <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <polyline points="22,6 12,13 2,6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <div className="contact-hero__card-text">
                    <div className="contact-hero__card-title">Quick Response</div>
                    <div className="contact-hero__card-subtitle">We reply fast</div>
                  </div>
                </div>

                <div className="contact-hero__floating-card contact-hero__floating-card--2">
                  <div className="contact-hero__card-icon">
                    <svg viewBox="0 0 24 24" fill="none">
                      <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <circle cx="9" cy="7" r="4" stroke="currentColor" strokeWidth="2"/>
                      <path d="M23 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <div className="contact-hero__card-text">
                    <div className="contact-hero__card-title">Expert Team</div>
                    <div className="contact-hero__card-subtitle">Here to help</div>
                  </div>
                </div>

                <div className="contact-hero__floating-card contact-hero__floating-card--3">
                  <div className="contact-hero__card-icon">
                    <svg viewBox="0 0 24 24" fill="none">
                      <path d="M20 6L9 17l-5-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <div className="contact-hero__card-text">
                    <div className="contact-hero__card-title">Trusted</div>
                    <div className="contact-hero__card-subtitle">50k+ agencies</div>
                  </div>
                </div>

                <div className="contact-hero__main-visual">
                  <div className="contact-hero__phone-mockup">
                    <div className="contact-hero__phone-screen">
                      <div className="contact-hero__phone-header">
                        <div className="contact-hero__phone-time">9:41</div>

                        {/* Dynamic Island */}
                        <div
                          className={`contact-hero__dynamic-island contact-hero__dynamic-island--${dynamicIslandState}`}
                          onClick={handleIslandClick}
                        >
                          {dynamicIslandState === 'default' && (
                            <div className="contact-hero__island-default">
                              <div className="contact-hero__island-camera"></div>
                              <div className="contact-hero__island-speaker"></div>
                            </div>
                          )}

                          {dynamicIslandState !== 'default' && islandContent && (
                            <div className="contact-hero__island-expanded">
                              <div className="contact-hero__island-icon" style={{ backgroundColor: islandContent.color }}>
                                {islandContent.icon}
                              </div>
                              <div className="contact-hero__island-content">
                                <div className="contact-hero__island-title">{islandContent.title}</div>
                                <div className="contact-hero__island-subtitle">{islandContent.subtitle}</div>
                              </div>
                              {dynamicIslandState === 'call' && (
                                <div className="contact-hero__island-actions">
                                  <div className="contact-hero__island-action contact-hero__island-action--decline">✕</div>
                                  <div className="contact-hero__island-action contact-hero__island-action--accept">✓</div>
                                </div>
                              )}
                              {dynamicIslandState === 'music' && (
                                <div className="contact-hero__island-waveform">
                                  <div className="contact-hero__wave"></div>
                                  <div className="contact-hero__wave"></div>
                                  <div className="contact-hero__wave"></div>
                                </div>
                              )}
                            </div>
                          )}
                        </div>

                        <div className="contact-hero__phone-status">
                          <div className="contact-hero__phone-signal"></div>
                          <div className="contact-hero__phone-battery"></div>
                        </div>
                      </div>
                      <div className="contact-hero__phone-content">
                        <div className="contact-hero__chat-bubble contact-hero__chat-bubble--received">
                          <div className="contact-hero__chat-text">Hi! How can we help you today?</div>
                          <div className="contact-hero__chat-time">2:30 PM</div>
                        </div>
                        <div className="contact-hero__chat-bubble contact-hero__chat-bubble--sent">
                          <div className="contact-hero__chat-text">I'd like to learn more about Telescope</div>
                          <div className="contact-hero__chat-time">2:31 PM</div>
                        </div>
                        <div className="contact-hero__chat-bubble contact-hero__chat-bubble--received">
                          <div className="contact-hero__chat-text">Perfect! I'd love to show you around. When works best for a demo?</div>
                          <div className="contact-hero__chat-time">2:31 PM</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Content */}
      <section className={`contact-content ${isContentVisible ? 'contact-content--visible' : ''}`} ref={contentRef}>
        <div className="contact-content__container">
          <div className="contact-content__header">
            <h2 className="contact-content__title">Get in Touch</h2>
            <p className="contact-content__subtitle">
              Choose the best way to reach us. We're here to help you succeed.
            </p>
          </div>

          <div className="contact-content__grid">
            {/* Contact Methods */}
            <div className="contact-methods">
              <div className="contact-method__card contact-method__card--primary">
                <div className="contact-method__header">
                  <div className="contact-method__icon">
                    <svg viewBox="0 0 24 24" fill="none">
                      <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <polyline points="22,6 12,13 2,6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <div className="contact-method__badge">Fastest Response</div>
                </div>
                <h3 className="contact-method__title">Email Us</h3>
                <p className="contact-method__description">
                  Send us a message and we'll respond within 24 hours during business days.
                </p>
                <div className="contact-method__detail">
                  <a href="mailto:<EMAIL>" className="contact-method__link">
                    <EMAIL>
                    <svg viewBox="0 0 24 24" fill="none">
                      <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </a>
                </div>
              </div>

              <div className="contact-method__card">
                <div className="contact-method__header">
                  <div className="contact-method__icon">
                    <svg viewBox="0 0 24 24" fill="none">
                      <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                      <polyline points="12,6 12,12 16,14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                </div>
                <h3 className="contact-method__title">Business Hours</h3>
                <p className="contact-method__description">
                  We're available to help during these hours.
                </p>
                <div className="contact-method__schedule">
                  <div className="contact-method__schedule-item">
                    <span className="contact-method__day">Monday - Friday</span>
                    <span className="contact-method__time">9:00 AM - 9:00 PM</span>
                  </div>
                  <div className="contact-method__schedule-item">
                    <span className="contact-method__day">Timezone</span>
                    <span className="contact-method__time">Central Time (CT)</span>
                  </div>
                </div>
              </div>

              <div className="contact-method__card">
                <div className="contact-method__header">
                  <div className="contact-method__icon">
                    <svg viewBox="0 0 24 24" fill="none">
                      <path d="M8 2v4M16 2v4M3 10h18M5 4h14a2 2 0 012 2v14a2 2 0 01-2 2H5a2 2 0 01-2-2V6a2 2 0 012-2z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                </div>
                <h3 className="contact-method__title">Book a Demo</h3>
                <p className="contact-method__description">
                  See Telescope in action with a personalized demo.
                </p>
                <div className="contact-method__detail">
                  <a href="#demo" className="contact-method__link contact-method__link--button">
                    Schedule Demo
                    <svg viewBox="0 0 24 24" fill="none">
                      <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </a>
                </div>
              </div>
            </div>

            {/* Contact Form */}
            <div className="contact-form">
              <div className="contact-form__visual">
                <div className="contact-form__decoration">
                  <div className="contact-form__decoration-circle contact-form__decoration-circle--1"></div>
                  <div className="contact-form__decoration-circle contact-form__decoration-circle--2"></div>
                  <div className="contact-form__decoration-circle contact-form__decoration-circle--3"></div>
                </div>
              </div>

              <div className="contact-form__content">
                <div className="contact-form__header">
                  <div className="contact-form__badge">
                    <svg className="contact-form__badge-icon" viewBox="0 0 24 24" fill="none">
                      <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    Send us a message
                  </div>
                  <h2 className="contact-form__title">Tell us about your needs</h2>
                  <p className="contact-form__subtitle">
                    Have a question about Telescope? We'd love to hear from you and help you find the perfect solution.
                  </p>
                </div>

              <form onSubmit={handleSubmit} className="contact-form__form">
                <div className="contact-form__row">
                  <div className="contact-form__field">
                    <label htmlFor="name" className="contact-form__label">Name *</label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="contact-form__input"
                      placeholder="Your full name"
                    />
                  </div>
                  <div className="contact-form__field">
                    <label htmlFor="email" className="contact-form__label">Email *</label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="contact-form__input"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="contact-form__row">
                  <div className="contact-form__field">
                    <label htmlFor="company" className="contact-form__label">Company</label>
                    <input
                      type="text"
                      id="company"
                      name="company"
                      value={formData.company}
                      onChange={handleInputChange}
                      className="contact-form__input"
                      placeholder="Your agency name"
                    />
                  </div>
                  <div className="contact-form__field">
                    <label htmlFor="phone" className="contact-form__label">Phone</label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="contact-form__input"
                      placeholder="(*************"
                    />
                  </div>
                </div>

                <div className="contact-form__field">
                  <label htmlFor="subject" className="contact-form__label">Subject *</label>
                  <select
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    required
                    className="contact-form__select"
                  >
                    <option value="General Inquiry">General Inquiry</option>
                    <option value="Product Demo">Product Demo</option>
                    <option value="Pricing Information">Pricing Information</option>
                    <option value="Technical Support">Technical Support</option>
                    <option value="Partnership">Partnership</option>
                    <option value="Other">Other</option>
                  </select>
                </div>

                <div className="contact-form__field">
                  <label htmlFor="message" className="contact-form__label">Message *</label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    rows="5"
                    className="contact-form__textarea"
                    placeholder="Tell us how we can help you..."
                  />
                </div>

                {submitStatus === 'success' && (
                  <div className="contact-form__success">
                    <svg className="contact-form__success-icon" viewBox="0 0 24 24" fill="none">
                      <path d="M20 6L9 17l-5-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <p>Thank you! We'll get back to you within 24 hours.</p>
                  </div>
                )}

                {submitStatus === 'error' && (
                  <div className="contact-form__error">
                    <p>Something went wrong. Please try again or email us directly.</p>
                  </div>
                )}

                <div className="contact-form__policy">
                  <p className="contact-form__policy-text">
                    Telescope's use and transfer to any other app of information received from Google APIs will adhere to Google API Services User Data Policy, including the Limited Use requirements. TelescopeHR does not share any user data with third-party tools, including AI models.
                  </p>
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={`contact-form__submit ${isSubmitting ? 'contact-form__submit--loading' : ''}`}
                >
                  {isSubmitting ? (
                    <>
                      <svg className="contact-form__loading-icon" viewBox="0 0 24 24">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" fill="none" strokeDasharray="31.416" strokeDashoffset="31.416">
                          <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                          <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                        </circle>
                      </svg>
                      Sending...
                    </>
                  ) : (
                    <>
                      Send Message
                      <svg className="contact-form__submit-icon" viewBox="0 0 24 24" fill="none">
                        <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </>
                  )}
                </button>
              </form>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Actions */}
      <section className="contact-actions">
        <div className="contact-actions__container">
          <div className="contact-actions__header">
            <h2 className="contact-actions__title">Ready to Get Started?</h2>
            <p className="contact-actions__subtitle">
              Choose the option that works best for you
            </p>
          </div>

          <div className="contact-actions__grid">
            <div className="contact-actions__card contact-actions__card--featured">
              <div className="contact-actions__card-header">
                <div className="contact-actions__icon">
                  <svg viewBox="0 0 24 24" fill="none">
                    <path d="M8 2v4M16 2v4M3 10h18M5 4h14a2 2 0 012 2v14a2 2 0 01-2 2H5a2 2 0 01-2-2V6a2 2 0 012-2z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                <div className="contact-actions__badge">Most Popular</div>
              </div>
              <h3 className="contact-actions__card-title">Book a Demo</h3>
              <p className="contact-actions__description">
                See Telescope in action with a personalized demo tailored to your agency's needs
              </p>
              <ul className="contact-actions__features">
                <li className="contact-actions__feature">
                  <svg viewBox="0 0 24 24" fill="none">
                    <path d="M20 6L9 17l-5-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  30-minute personalized demo
                </li>
                <li className="contact-actions__feature">
                  <svg viewBox="0 0 24 24" fill="none">
                    <path d="M20 6L9 17l-5-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  Custom pricing discussion
                </li>
                <li className="contact-actions__feature">
                  <svg viewBox="0 0 24 24" fill="none">
                    <path d="M20 6L9 17l-5-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  Implementation roadmap
                </li>
              </ul>
              <a href="#demo" className="contact-actions__button contact-actions__button--primary">
                Schedule Demo
                <svg viewBox="0 0 24 24" fill="none">
                  <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </a>
            </div>

            <div className="contact-actions__card">
              <div className="contact-actions__card-header">
                <div className="contact-actions__icon">
                  <svg viewBox="0 0 24 24" fill="none">
                    <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
              </div>
              <h3 className="contact-actions__card-title">Start Free Trial</h3>
              <p className="contact-actions__description">
                Try Telescope risk-free for 30 days with full access to all features
              </p>
              <ul className="contact-actions__features">
                <li className="contact-actions__feature">
                  <svg viewBox="0 0 24 24" fill="none">
                    <path d="M20 6L9 17l-5-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  30-day free trial
                </li>
                <li className="contact-actions__feature">
                  <svg viewBox="0 0 24 24" fill="none">
                    <path d="M20 6L9 17l-5-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  No credit card required
                </li>
                <li className="contact-actions__feature">
                  <svg viewBox="0 0 24 24" fill="none">
                    <path d="M20 6L9 17l-5-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  Full feature access
                </li>
              </ul>
              <a href="#trial" className="contact-actions__button">
                Start Trial
                <svg viewBox="0 0 24 24" fill="none">
                  <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ContactUs;
