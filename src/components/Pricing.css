/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

/* Pricing Page */
.pricing {
  font-family: "Plus Jakarta Sans", sans-serif;
}

/* Animation Keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Hero Section */
.pricing-hero {
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
  padding: 120px 0 80px 0;
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.pricing-hero--visible {
  opacity: 1;
  transform: translateY(0);
}

.pricing-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(0,106,193,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.5;
}

.pricing-hero__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 1;
}

.pricing-hero__content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.pricing-hero__title {
  font-size: clamp(2.5rem, 4vw, 3.5rem);
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 24px 0;
  letter-spacing: -0.02em;
  line-height: 1.1;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s;
}

.pricing-hero--visible .pricing-hero__title {
  opacity: 1;
  transform: translateY(0);
}

.pricing-hero__subtitle {
  font-size: 1.25rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.6;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
}

.pricing-hero--visible .pricing-hero__subtitle {
  opacity: 1;
  transform: translateY(0);
}

/* Content Section */
.pricing-content {
  padding: 100px 0;
  background: white;
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);
}

.pricing-content--visible {
  opacity: 1;
  transform: translateY(0);
}

.pricing-content__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.pricing-content__grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: start;
}

/* Features */
.pricing-features {
  opacity: 0;
  transform: translateX(-30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s;
}

.pricing-content--visible .pricing-features {
  opacity: 1;
  transform: translateX(0);
}

.pricing-features__title {
  font-size: 2rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 32px 0;
  letter-spacing: -0.02em;
}

.pricing-features__list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.pricing-features__item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  font-size: 1rem;
  color: #374151;
  line-height: 1.6;
  opacity: 0;
  transform: translateX(-20px);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.pricing-content--visible .pricing-features__item {
  opacity: 1;
  transform: translateX(0);
}

.pricing-content--visible .pricing-features__item:nth-child(1) { transition-delay: 0.4s; }
.pricing-content--visible .pricing-features__item:nth-child(2) { transition-delay: 0.5s; }
.pricing-content--visible .pricing-features__item:nth-child(3) { transition-delay: 0.6s; }
.pricing-content--visible .pricing-features__item:nth-child(4) { transition-delay: 0.7s; }
.pricing-content--visible .pricing-features__item:nth-child(5) { transition-delay: 0.8s; }
.pricing-content--visible .pricing-features__item:nth-child(6) { transition-delay: 0.9s; }
.pricing-content--visible .pricing-features__item:nth-child(7) { transition-delay: 1.0s; }
.pricing-content--visible .pricing-features__item:nth-child(8) { transition-delay: 1.1s; }
.pricing-content--visible .pricing-features__item:nth-child(9) { transition-delay: 1.2s; }

.pricing-features__icon {
  width: 20px;
  height: 20px;
  color: #006ac1;
  flex-shrink: 0;
  margin-top: 2px;
}

/* Form */
.pricing-form {
  background: white;
  border: 1px solid rgba(0, 106, 193, 0.1);
  border-radius: 24px;
  box-shadow: 0 8px 40px rgba(0, 106, 193, 0.08);
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateX(30px) scale(0.95);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
}

.pricing-content--visible .pricing-form {
  opacity: 1;
  transform: translateX(0) scale(1);
}

.pricing-form__content {
  padding: 40px;
  position: relative;
  z-index: 1;
}

.pricing-form__title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 8px 0;
}

.pricing-form__subtitle {
  font-size: 1rem;
  color: #6b7280;
  margin: 0 0 32px 0;
  line-height: 1.6;
}

.pricing-form__form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.pricing-form__field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.pricing-form__label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.pricing-form__input,
.pricing-form__select {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  font-family: inherit;
  transition: all 0.3s ease;
  background: white;
}

.pricing-form__input:focus,
.pricing-form__select:focus {
  outline: none;
  border-color: #006ac1;
  box-shadow: 0 0 0 3px rgba(0, 106, 193, 0.1);
}

.pricing-form__submit {
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  color: white;
  border: none;
  padding: 16px 24px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 8px;
}

.pricing-form__submit:hover {
  background: linear-gradient(135deg, #1a7dd1 0%, #006ac1 100%);
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 12px 35px rgba(0, 106, 193, 0.4);
}

.pricing-form__submit:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.pricing-form__submit-icon {
  width: 18px;
  height: 18px;
  transition: transform 0.3s ease;
}

.pricing-form__submit:hover .pricing-form__submit-icon {
  transform: translateX(4px);
}

.pricing-form__success {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 0.875rem;
  text-align: center;
}

.pricing-form__error {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 0.875rem;
  text-align: center;
}

.pricing-form__policy {
  margin: 16px 0;
  padding: 16px;
  background: rgba(0, 106, 193, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(0, 106, 193, 0.1);
}

.pricing-form__policy-text {
  font-size: 0.75rem;
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
  text-align: center;
}

/* FAQ Section */
.pricing-faq {
  padding: 100px 0;
  background: #f8faff;
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);
}

.pricing-faq--visible {
  opacity: 1;
  transform: translateY(0);
}

.pricing-faq__container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 24px;
}

.pricing-faq__header {
  text-align: center;
  margin-bottom: 60px;
}

.pricing-faq__title {
  font-size: 2.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 16px 0;
  letter-spacing: -0.02em;
}

.pricing-faq__subtitle {
  font-size: 1.125rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.6;
}

.pricing-faq__list {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.pricing-faq__item {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 20px rgba(0, 106, 193, 0.05);
  border: 1px solid rgba(0, 106, 193, 0.1);
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.pricing-faq--visible .pricing-faq__item {
  opacity: 1;
  transform: translateY(0);
}

.pricing-faq--visible .pricing-faq__item:nth-child(1) { transition-delay: 0.1s; }
.pricing-faq--visible .pricing-faq__item:nth-child(2) { transition-delay: 0.2s; }
.pricing-faq--visible .pricing-faq__item:nth-child(3) { transition-delay: 0.3s; }
.pricing-faq--visible .pricing-faq__item:nth-child(4) { transition-delay: 0.4s; }
.pricing-faq--visible .pricing-faq__item:nth-child(5) { transition-delay: 0.5s; }
.pricing-faq--visible .pricing-faq__item:nth-child(6) { transition-delay: 0.6s; }

.pricing-faq__item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 106, 193, 0.15);
  border-color: rgba(0, 106, 193, 0.2);
}

.pricing-faq__question {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 16px 0;
  line-height: 1.4;
}

.pricing-faq__answer {
  font-size: 1rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.6;
}

/* CTA Section */
.pricing-cta {
  padding: 100px 0;
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  color: white;
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);
}

.pricing-cta--visible {
  opacity: 1;
  transform: translateY(0);
}

.pricing-cta__container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 24px;
}

.pricing-cta__content {
  text-align: center;
}

.pricing-cta__title {
  font-size: 2.5rem;
  font-weight: 600;
  margin: 0 0 16px 0;
  letter-spacing: -0.02em;
}

.pricing-cta__subtitle {
  font-size: 1.125rem;
  margin: 0 0 32px 0;
  line-height: 1.6;
  opacity: 0.9;
}

.pricing-cta__button {
  background: white;
  color: #006ac1;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 1.125rem;
  font-weight: 600;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 32px;
}

.pricing-cta__button:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.pricing-cta__icon {
  width: 20px;
  height: 20px;
}

.pricing-cta__features {
  display: flex;
  justify-content: center;
  gap: 32px;
  flex-wrap: wrap;
}

.pricing-cta__feature {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  opacity: 0.9;
}

.pricing-cta__feature-icon {
  width: 16px;
  height: 16px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .pricing-content__grid {
    gap: 60px;
  }
}

@media (max-width: 768px) {
  .pricing-hero {
    padding: 100px 0 60px 0;
  }

  .pricing-content {
    padding: 80px 0;
  }

  .pricing-content__grid {
    grid-template-columns: 1fr;
    gap: 60px;
  }

  .pricing-features__title {
    font-size: 1.75rem;
  }

  .pricing-form__content {
    padding: 32px 24px;
  }

  .pricing-faq {
    padding: 80px 0;
  }

  .pricing-faq__header {
    margin-bottom: 40px;
  }

  .pricing-faq__title {
    font-size: 2rem;
  }

  .pricing-faq__item {
    padding: 24px;
  }

  .pricing-cta {
    padding: 80px 0;
  }

  .pricing-cta__title {
    font-size: 2rem;
  }

  .pricing-cta__features {
    gap: 24px;
  }
}

@media (max-width: 480px) {
  .pricing-hero {
    padding: 80px 0 40px 0;
  }

  .pricing-hero__container,
  .pricing-content__container,
  .pricing-faq__container,
  .pricing-cta__container {
    padding: 0 16px;
  }

  .pricing-content {
    padding: 60px 0;
  }

  .pricing-content__grid {
    gap: 40px;
  }

  .pricing-features__title {
    font-size: 1.5rem;
  }

  .pricing-form__content {
    padding: 24px 20px;
  }

  .pricing-faq {
    padding: 60px 0;
  }

  .pricing-faq__list {
    gap: 24px;
  }

  .pricing-faq__item {
    padding: 20px;
  }

  .pricing-faq__question {
    font-size: 1.125rem;
  }

  .pricing-cta {
    padding: 60px 0;
  }

  .pricing-cta__title {
    font-size: 1.75rem;
  }

  .pricing-cta__features {
    flex-direction: column;
    gap: 16px;
    align-items: center;
  }
}
