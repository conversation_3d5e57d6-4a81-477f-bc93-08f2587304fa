@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap');

:root {
  --primary-color: #006ac1;
  --primary-light: #1a7dd1;
  --primary-dark: #0057a1;
  --accent-color: #4f46e5;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-light: #9ca3af;
  --border-light: rgba(0, 0, 0, 0.06);
  --shadow-light: rgba(0, 0, 0, 0.04);
  --shadow-medium: rgba(0, 0, 0, 0.1);
}

/* Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(24px);
  -webkit-backdrop-filter: blur(24px);
  border-bottom: 1px solid var(--border-light);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  font-family: "Plus Jakarta Sans", sans-serif;
}

.header--scrolled {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 1px 3px var(--shadow-light), 0 1px 2px var(--shadow-light);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.header__container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 72px;
}

/* Logo */
.header__logo {
  display: flex;
  align-items: center;
  z-index: 1001;
}

.header__logo-img {
  height: 36px;
  width: auto;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.header__logo-img:hover {
  transform: scale(1.02);
}

/* Navigation */
.header__nav {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header__nav-item {
  position: relative;
}

.header__nav-link {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.625rem 1rem;
  font-weight: 500;
  font-size: 0.875rem;
  color: var(--text-primary);
  text-decoration: none;
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  border-radius: 8px;
  letter-spacing: -0.01em;
}

.header__nav-link:hover {
  color: var(--primary-color);
  background: rgba(0, 106, 193, 0.04);
}

.header__dropdown-icon {
  width: 14px;
  height: 14px;
  transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  opacity: 0.7;
}

.header__nav-item--dropdown:hover .header__dropdown-icon {
  transform: rotate(180deg);
  opacity: 1;
}

/* Dropdown */
.header__dropdown {
  position: absolute;
  top: calc(100% + 8px);
  left: -1rem;
  min-width: 840px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--border-light);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-8px) scale(0.98);
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: 1000;
}

.header__dropdown--open {
  opacity: 1;
  visibility: visible;
  transform: translateY(0) scale(1);
}

.header__dropdown-content {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  padding: 1.5rem;
}

.header__dropdown-section {
  padding: 0;
}

.header__dropdown-title {
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--primary-color);
  margin: 0 0 0.75rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #f1f5f9;
  letter-spacing: -0.01em;
}

.header__dropdown-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.header__dropdown-item {
  margin-bottom: 0.25rem;
}

.header__dropdown-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 0.5rem 0.75rem;
  font-size: 0.8125rem;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  line-height: 1.4;
  border-radius: 6px;
  letter-spacing: -0.005em;
  background: none;
  border: none;
  cursor: pointer;
  width: 100%;
  text-align: left;
  font-family: inherit;
}

.header__dropdown-icon {
  font-size: 16px;
  line-height: 1;
  min-width: 20px;
  text-align: center;
}

.header__dropdown-link:hover {
  color: var(--primary-color);
  background: rgba(0, 106, 193, 0.04);
  transform: translateX(2px);
}

/* Simple Dropdown Variant */
.header__dropdown--simple {
  min-width: 200px;
  left: 0;
}

.header__dropdown--simple .header__dropdown-content {
  display: block;
  padding: 0.75rem 0;
}

.header__dropdown--simple .header__dropdown-list {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.header__dropdown--simple .header__dropdown-item {
  margin: 0;
}

.header__dropdown--simple .header__dropdown-link {
  padding: 0.75rem 1rem;
  border-radius: 0;
  margin: 0;
  font-size: 0.875rem;
}

.header__dropdown--simple .header__dropdown-link:hover {
  background: rgba(0, 106, 193, 0.06);
  transform: none;
}

.header__dropdown--simple .header__dropdown-icon {
  font-size: 14px;
  min-width: 18px;
}

/* CTA Container */
.header__cta {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* Login Button */
.header__login-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0.625rem 1.25rem;
  background: transparent;
  color: var(--primary-color);
  border: 1px solid rgba(0, 106, 193, 0.2);
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  font-family: "Plus Jakarta Sans", sans-serif;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  text-decoration: none;
  letter-spacing: -0.01em;
  position: relative;
  overflow: hidden;
}

.header__login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 106, 193, 0.05);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.header__login-btn:hover {
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 106, 193, 0.1);
}

.header__login-btn:hover::before {
  opacity: 1;
}

.header__login-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 106, 193, 0.1);
}

.header__login-btn > * {
  position: relative;
  z-index: 1;
}

.header__login-icon {
  width: 14px;
  height: 14px;
  transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.header__login-btn:hover .header__login-icon {
  transform: scale(1.1);
}

/* CTA Button - WhatsApp inspired */
.header__cta-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1.25rem;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: white;
  border: none;
  border-radius: 24px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 1px 3px rgba(0, 106, 193, 0.12), 0 1px 2px rgba(0, 106, 193, 0.08);
  letter-spacing: -0.01em;
  position: relative;
  overflow: hidden;
}

.header__cta-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.header__cta-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 106, 193, 0.15), 0 2px 4px rgba(0, 106, 193, 0.1);
}

.header__cta-btn:hover::before {
  opacity: 1;
}

.header__cta-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 106, 193, 0.1);
}

.header__cta-btn > * {
  position: relative;
  z-index: 1;
}

.header__cta-icon {
  width: 14px;
  height: 14px;
  transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.header__cta-btn:hover .header__cta-icon {
  transform: translateX(2px);
}

/* Mobile Menu Toggle */
.header__mobile-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  z-index: 1001;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.header__mobile-toggle:hover {
  background: rgba(32, 28, 68, 0.04);
}

.header__hamburger {
  display: flex;
  flex-direction: column;
  width: 20px;
  height: 16px;
  position: relative;
}

.header__hamburger span {
  display: block;
  height: 2px;
  width: 100%;
  background: var(--text-primary);
  border-radius: 1px;
  transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.header__hamburger span:nth-child(1) {
  transform-origin: top left;
}

.header__hamburger span:nth-child(2) {
  margin: 5px 0;
}

.header__hamburger span:nth-child(3) {
  transform-origin: bottom left;
}

.header__hamburger--open span:nth-child(1) {
  transform: rotate(45deg) translate(0px, -1px);
}

.header__hamburger--open span:nth-child(2) {
  opacity: 0;
  transform: translateX(-15px);
}

.header__hamburger--open span:nth-child(3) {
  transform: rotate(-45deg) translate(0px, 1px);
}

/* Mobile Menu */
.header__mobile-menu {
  position: fixed;
  top: 72px;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid var(--border-light);
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  max-height: calc(100vh - 72px);
  overflow-y: auto;
}

.header__mobile-menu--open {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.header__mobile-nav {
  padding: 1.5rem;
}

.header__mobile-link {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0.875rem 0;
  font-weight: 500;
  color: var(--text-primary);
  text-decoration: none;
  background: none;
  border: none;
  border-bottom: 1px solid #f1f5f9;
  cursor: pointer;
  font-size: 0.9375rem;
  letter-spacing: -0.01em;
}

.header__mobile-dropdown-icon {
  width: 18px;
  height: 18px;
  transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  opacity: 0.6;
}

.header__mobile-dropdown-icon--open {
  transform: rotate(180deg);
  opacity: 1;
}

.header__mobile-dropdown {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.header__mobile-dropdown--open {
  max-height: 800px;
}

.header__mobile-dropdown-section {
  padding: 0.75rem 0;
  border-bottom: 1px solid #f8fafc;
}

.header__mobile-dropdown-title {
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
  font-size: 0.8125rem;
  letter-spacing: -0.01em;
}

.header__mobile-dropdown-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.header__mobile-dropdown-link {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 0.375rem 0;
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.8125rem;
  line-height: 1.4;
  letter-spacing: -0.005em;
}

.header__mobile-dropdown-icon {
  font-size: 14px;
  line-height: 1;
  min-width: 18px;
  text-align: center;
}

.header__mobile-cta {
  width: 100%;
  padding: 0.875rem;
  margin-top: 1.5rem;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 500;
  font-size: 0.9375rem;
  cursor: pointer;
  letter-spacing: -0.01em;
  box-shadow: 0 2px 4px rgba(0, 106, 193, 0.1);
}

/* Responsive */
@media (max-width: 1024px) {
  .header__dropdown {
    min-width: 720px;
    left: -2rem;
  }

  .header__dropdown-content {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
    padding: 1.25rem;
  }
}

@media (max-width: 768px) {
  .header__container {
    padding: 0 1rem;
    height: 64px;
  }

  .header__mobile-menu {
    top: 64px;
    max-height: calc(100vh - 64px);
  }

  .header__logo-img {
    height: 32px;
  }

  .header__nav,
  .header__cta {
    display: none;
  }

  .header__mobile-toggle {
    display: block;
  }
}

@media (max-width: 480px) {
  .header__container {
    padding: 0 0.75rem;
  }

  .header__mobile-nav {
    padding: 1rem;
  }
}
