/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

/* Feature Problems Section */
.feature-problems {
  background: #ffffff;
  padding: 100px 0;
  position: relative;
}

.feature-problems__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.feature-problems__zigzag {
  display: flex;
  flex-direction: column;
  gap: 120px;
}

.feature-problems__item {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  position: relative;
}

.feature-problems__item--reverse {
  direction: rtl;
}

.feature-problems__item--reverse .feature-problems__content {
  direction: ltr;
}

.feature-problems__content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.feature-problems__problem {
  font-size: 1.125rem;
  color: #ef4444;
  font-weight: 500;
  font-family: "Plus Jakarta Sans", sans-serif;
  line-height: 1.5;
}

.feature-problems__solution {
  font-size: clamp(1.75rem, 3vw, 2.25rem);
  font-weight: 500;
  color: #1a1a1a;
  margin: 0;
  line-height: 1.3;
  letter-spacing: -0.01em;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-problems__checklist {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feature-problems__check-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  font-size: 1rem;
  color: #374151;
  line-height: 1.5;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-problems__check-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.feature-problems__cta {
  background: linear-gradient(135deg, #006ac1 0%, #0056a3 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 500;
  font-family: "Plus Jakarta Sans", sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  align-self: flex-start;
  box-shadow: 0 4px 15px rgba(0, 106, 193, 0.2);
}

.feature-problems__cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 106, 193, 0.4);
}

.feature-problems__cta-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;
}

.feature-problems__cta:hover .feature-problems__cta-icon {
  transform: translateX(4px);
}

.feature-problems__image {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.feature-problems__image-container {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  background: white;
  transition: all 0.3s ease;
  width: 100%;
  max-width: 500px;
}

.feature-problems__image-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* Mac-style Header */
.feature-problems__mac-header {
  position: relative;
  background: linear-gradient(180deg, #f6f6f6 0%, #e8e8e8 100%);
  border-bottom: 1px solid #d1d1d1;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.feature-problems__mac-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.feature-problems__mac-button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 0.5px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.feature-problems__mac-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.feature-problems__mac-button--close {
  background: linear-gradient(135deg, #ff5f57 0%, #ff4444 100%);
}

.feature-problems__mac-button--close:hover::before {
  content: '×';
  color: #8b0000;
  font-size: 10px;
  font-weight: bold;
  opacity: 1;
}

.feature-problems__mac-button--minimize {
  background: linear-gradient(135deg, #ffbd2e 0%, #ffaa00 100%);
}

.feature-problems__mac-button--minimize:hover::before {
  content: '−';
  color: #8b4500;
  font-size: 10px;
  font-weight: bold;
  opacity: 1;
}

.feature-problems__mac-button--maximize {
  background: linear-gradient(135deg, #28ca42 0%, #20a034 100%);
}

.feature-problems__mac-button--maximize:hover::before {
  content: '+';
  color: #0d4f1c;
  font-size: 10px;
  font-weight: bold;
  opacity: 1;
}

.feature-problems__mac-button:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.feature-problems__img {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 0 0 16px 16px;
  transition: transform 0.3s ease;
}

/* Feature Why Use Section */
.feature-why-use {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #f8faff 100%);
  padding: 100px 0 50px 0;
  position: relative;
  overflow: hidden;
  margin-top: -50px;
  clip-path: polygon(0 50px, 100% 0, 100% 100%, 0 100%);
}

.feature-why-use::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(135deg, rgba(0, 106, 193, 0.05) 0%, rgba(0, 106, 193, 0.02) 100%);
  z-index: 0;
}

.feature-why-use__container {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.feature-why-use__zigzag {
  display: flex;
  flex-direction: column;
  gap: 120px;
}

.feature-why-use__item:nth-child(2) {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 106, 193, 0.1);
}

.feature-why-use__item:nth-child(2):hover {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 106, 193, 0.15);
}

.feature-why-use__item {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  position: relative;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 24px;
  padding: 60px 50px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 20px 40px rgba(0, 106, 193, 0.08),
    0 1px 0 rgba(255, 255, 255, 0.8);
  transition: all 0.4s ease;
}

.feature-why-use__item:hover {
  transform: translateY(-5px);
  box-shadow:
    0 30px 60px rgba(0, 106, 193, 0.12),
    0 1px 0 rgba(255, 255, 255, 0.9);
}

.feature-why-use__item--reverse {
  direction: rtl;
}

.feature-why-use__item--reverse .feature-why-use__content {
  direction: ltr;
}

.feature-why-use__content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.feature-why-use__title {
  font-size: clamp(1.75rem, 3vw, 2.25rem);
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  line-height: 1.3;
  letter-spacing: -0.01em;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-why-use__subtitle {
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-why-use__benefits {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feature-why-use__benefit {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  font-size: 1rem;
  color: #374151;
  line-height: 1.5;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-why-use__check-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.feature-why-use__cta {
  background: linear-gradient(135deg, #006ac1 0%, #0056a3 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 500;
  font-family: "Plus Jakarta Sans", sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  align-self: flex-start;
  box-shadow: 0 4px 15px rgba(0, 106, 193, 0.2);
}

.feature-why-use__cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 106, 193, 0.4);
}

.feature-why-use__cta-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;
}

.feature-why-use__cta:hover .feature-why-use__cta-icon {
  transform: translateX(4px);
}

.feature-why-use__image {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.feature-why-use__image-container {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  background: white;
  transition: all 0.3s ease;
  width: 100%;
  max-width: 500px;
}

.feature-why-use__image-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* Mac-style Header for Why Use */
.feature-why-use__mac-header {
  position: relative;
  background: linear-gradient(180deg, #f6f6f6 0%, #e8e8e8 100%);
  border-bottom: 1px solid #d1d1d1;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.feature-why-use__mac-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.feature-why-use__mac-button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 0.5px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.feature-why-use__mac-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.feature-why-use__mac-button--close {
  background: linear-gradient(135deg, #ff5f57 0%, #ff4444 100%);
}

.feature-why-use__mac-button--close:hover::before {
  content: '×';
  color: #8b0000;
  font-size: 10px;
  font-weight: bold;
  opacity: 1;
}

.feature-why-use__mac-button--minimize {
  background: linear-gradient(135deg, #ffbd2e 0%, #ffaa00 100%);
}

.feature-why-use__mac-button--minimize:hover::before {
  content: '−';
  color: #8b4500;
  font-size: 10px;
  font-weight: bold;
  opacity: 1;
}

.feature-why-use__mac-button--maximize {
  background: linear-gradient(135deg, #28ca42 0%, #20a034 100%);
}

.feature-why-use__mac-button--maximize:hover::before {
  content: '+';
  color: #0d4f1c;
  font-size: 10px;
  font-weight: bold;
  opacity: 1;
}

.feature-why-use__mac-button:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.feature-why-use__img {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 0 0 16px 16px;
  transition: transform 0.2s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
  /* Feature Problems Mobile */
  .feature-problems {
    padding: 80px 0;
  }

  .feature-problems__container {
    padding: 0 16px;
  }

  .feature-problems__zigzag {
    gap: 80px;
  }

  .feature-problems__item {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .feature-problems__item--reverse {
    direction: ltr;
  }

  .feature-problems__content {
    gap: 20px;
  }

  .feature-problems__problem {
    font-size: 1rem;
  }

  .feature-problems__checklist {
    gap: 12px;
  }

  .feature-problems__check-item {
    font-size: 0.9rem;
  }

  .feature-problems__cta {
    padding: 14px 28px;
    font-size: 0.9rem;
    align-self: center;
  }

  .feature-problems__image {
    order: -1;
  }

  .feature-problems__image-container {
    border-radius: 16px;
  }

  .feature-problems__mac-header {
    padding: 10px 12px;
    border-radius: 16px 16px 0 0;
  }

  .feature-problems__mac-button {
    width: 10px;
    height: 10px;
  }

  .feature-problems__mac-buttons {
    gap: 6px;
  }

  .feature-problems__img {
    border-radius: 0 0 12px 12px;
  }

  /* Feature Why Use Mobile */
  .feature-why-use {
    padding: 80px 0;
    padding-top: 120px;
    margin-top: -30px;
    clip-path: polygon(0 30px, 100% 0, 100% 100%, 0 100%);
  }

  .feature-why-use__container {
    padding: 0 16px;
  }

  .feature-why-use__zigzag {
    gap: 60px;
  }

  .feature-why-use__item {
    grid-template-columns: 1fr;
    gap: 30px;
    text-align: center;
    padding: 40px 30px;
  }

  .feature-why-use__item--reverse {
    direction: ltr;
  }

  .feature-why-use__content {
    gap: 20px;
  }

  .feature-why-use__benefits {
    gap: 12px;
  }

  .feature-why-use__benefit {
    font-size: 0.9rem;
  }

  .feature-why-use__cta {
    padding: 14px 28px;
    font-size: 0.9rem;
    align-self: center;
  }

  .feature-why-use__image {
    order: -1;
  }

  .feature-why-use__image-container {
    border-radius: 16px;
  }

  .feature-why-use__mac-header {
    padding: 10px 12px;
    border-radius: 16px 16px 0 0;
  }

  .feature-why-use__mac-button {
    width: 10px;
    height: 10px;
  }

  .feature-why-use__mac-buttons {
    gap: 6px;
  }

  .feature-why-use__img {
    border-radius: 0 0 12px 12px;
  }
}

@media (max-width: 480px) {
  .feature-problems {
    padding: 60px 0;
  }

  .feature-problems__zigzag {
    gap: 60px;
  }

  .feature-problems__item {
    gap: 30px;
  }

  .feature-problems__content {
    gap: 16px;
  }

  .feature-why-use {
    padding: 60px 0;
    padding-top: 100px;
  }

  .feature-why-use__zigzag {
    gap: 50px;
  }

  .feature-why-use__item {
    gap: 25px;
    padding: 30px 20px;
  }

  .feature-why-use__content {
    gap: 16px;
  }
}
