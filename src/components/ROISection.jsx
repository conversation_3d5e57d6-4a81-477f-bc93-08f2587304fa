import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import './ROISection.css';

const ROISection = () => {
  const [isVisible, setIsVisible] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    const section = document.querySelector('.roi-section');
    if (section) {
      observer.observe(section);
    }

    return () => {
      if (section) {
        observer.unobserve(section);
      }
    };
  }, []);

  const stats = [
    {
      icon: "⏰",
      problem: "Most home care agencies spend 10+ hours/week on scheduling alone.",
      highlight: "10+ hours/week"
    },
    {
      icon: "🚀",
      problem: "Telescope users report up to 85% faster onboarding.",
      highlight: "85% faster onboarding"
    }
  ];

  return (
    <section className={`roi-section ${isVisible ? 'roi-section--visible' : ''}`}>
      <div className="roi-section__container">
        {/* ROI Content */}
        <div className="roi-section__content">
          <div className="roi-section__header">
            <h2 className="roi-section__title">Did you know?</h2>
          </div>

          <div className="roi-section__stats">
            {stats.map((stat, index) => (
              <div
                key={index}
                className="roi-section__stat"
                style={{ animationDelay: `${index * 0.3}s` }}
              >
                <div className="roi-section__stat-icon">
                  {stat.icon}
                </div>
                <div className="roi-section__stat-content">
                  <p
                    className="roi-section__stat-text"
                    dangerouslySetInnerHTML={{
                      __html: stat.problem.replace(stat.highlight, `<strong class="roi-section__highlight">${stat.highlight}</strong>`)
                    }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Final CTA */}
        <div className="roi-section__cta">
          <div className="roi-section__cta-content">
            <h2 className="roi-section__cta-title">
              Ready to grow your agency — without growing your stress?
            </h2>

            <button
              className="roi-section__cta-btn"
              onClick={() => navigate('/contact')}
            >
              <span className="roi-section__cta-text">Book Your Demo</span>
              <div className="roi-section__cta-icon">
                <svg viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="roi-section__cta-ripple"></div>
            </button>

            <p className="roi-section__cta-subtitle">
              Join 500+ agencies already using Telescope
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ROISection;
