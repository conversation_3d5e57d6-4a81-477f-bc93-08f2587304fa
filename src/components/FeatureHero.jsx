import { getFeatureImage } from '../utils/featureIMG';
import './FeatureHero.css';

const FeatureHero = ({ feature }) => {
  if (!feature) {
    console.warn('FeatureHero: No feature data provided');
    return null;
  }

  // Safely get images with error handling
  let webImage, mobileImage, hasMobileImage;
  try {
    webImage = getFeatureImage('heroInterfaces', feature.id, 'web');
    mobileImage = getFeatureImage('heroInterfaces', feature.id, 'mobile');
    hasMobileImage = mobileImage && mobileImage.src !== '/images/placeholder.png';
  } catch (error) {
    console.warn('FeatureHero: Error loading images for feature:', feature.id, error);
    webImage = { src: '/images/placeholder.png', alt: 'Feature preview' };
    mobileImage = { src: '/images/placeholder.png', alt: 'Mobile preview' };
    hasMobileImage = false;
  }

  return (
    <div className="feature-hero">
      <div className="feature-hero__container">
        <div className="feature-hero__content">
          {/* Left Side - Hero Content */}
          <div className="feature-hero__text">
            <div className="feature-hero__badge">
              <span className="feature-hero__badge-text">{feature.category}</span>
            </div>

            <h1 className="feature-hero__title">
              {feature.heroTitle}
            </h1>

            <p className="feature-hero__subtitle">
              {feature.heroSubtitle}
            </p>

            {feature.benefits && (
              <ul className="feature-hero__benefits">
                {feature.benefits.map((benefit, index) => (
                  <li key={index} className="feature-hero__benefit">
                    <svg className="feature-hero__check-icon" viewBox="0 0 24 24" fill="none">
                      <path d="M20 6L9 17l-5-5" stroke="#006ac1" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    {benefit}
                  </li>
                ))}
              </ul>
            )}

            <div className="feature-hero__cta">
              <button className="feature-hero__cta-btn">
                {feature.ctaText || 'Try This Feature'}
                <svg className="feature-hero__cta-icon" viewBox="0 0 24 24" fill="none">
                  <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
              <button className="feature-hero__demo-btn">
                Watch Demo
              </button>
            </div>
          </div>

          {/* Right Side - Stacked Images */}
          <div className="feature-hero__images">
            {/* Web Interface with Mac-style Header */}
            <div className="feature-hero__web-container">
              <div className="feature-hero__mac-header">
                <div className="feature-hero__mac-buttons">
                  <div className="feature-hero__mac-button feature-hero__mac-button--close"></div>
                  <div className="feature-hero__mac-button feature-hero__mac-button--minimize"></div>
                  <div className="feature-hero__mac-button feature-hero__mac-button--maximize"></div>
                </div>
              </div>
              <div className="feature-hero__web-image">
                <img
                  src={webImage.src}
                  alt={webImage.alt}
                  className="feature-hero__web-img"
                />
              </div>
            </div>

            {/* Mobile Interface - Only show if mobile image exists */}
            {hasMobileImage && (
              <div className="feature-hero__mobile-container">
                <div className="feature-hero__mobile-frame">
                  <img
                    src={mobileImage.src}
                    alt={mobileImage.alt}
                    className="feature-hero__mobile-img"
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeatureHero;
