@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap');

/* Parallax Container - Proper Implementation */
.parallax-container {
  position: relative;
  font-family: "Plus Jakarta Sans", sans-serif;
  width: 100%;
  margin: 0;
  padding: 0;
}

/* Parallax Background Section */
.parallax-section {
  position: relative;
  height: 100vh;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: 0;
  border-radius: 40px 40px 0 0;
  padding: 0;
}

/* Background that moves with parallax */
.parallax-section__background {
  position: absolute;
  top: -20%;
  left: 0;
  right: 0;
  width: 100%;
  height: 120%;
  will-change: transform;
 
  z-index: 1;
  margin: 0;
  padding: 0;
}

/* Background Image Placeholder */
.parallax-section__image-bg {
  width: 100%;
  height: 100%;
  border-radius: 40px 40px 0 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin: 0;
  padding: 0;
}

.parallax-section__image-container {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  background: white;
  transition: all 0.3s ease;
  max-width: 800px;
  width: 100%;
  margin: 0 auto;
}

/* Mac-style Header */
.parallax-section__mac-header {
  position: relative;
  background: linear-gradient(180deg, #f6f6f6 0%, #e8e8e8 100%);
  border-bottom: 1px solid #d1d1d1;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-radius: 20px 20px 0 0;
}

.parallax-section__mac-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.parallax-section__mac-button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 0.5px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.parallax-section__mac-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.parallax-section__mac-button--close {
  background: linear-gradient(135deg, #ff5f57 0%, #ff4444 100%);
}

.parallax-section__mac-button--close:hover::before {
  content: '×';
  color: #8b0000;
  font-size: 10px;
  font-weight: bold;
  opacity: 1;
}

.parallax-section__mac-button--minimize {
  background: linear-gradient(135deg, #ffbd2e 0%, #ffaa00 100%);
}

.parallax-section__mac-button--minimize:hover::before {
  content: '−';
  color: #8b4500;
  font-size: 10px;
  font-weight: bold;
  opacity: 1;
}

.parallax-section__mac-button--maximize {
  background: linear-gradient(135deg, #28ca42 0%, #20a034 100%);
}

.parallax-section__mac-button--maximize:hover::before {
  content: '+';
  color: #0d4f1c;
  font-size: 10px;
  font-weight: bold;
  opacity: 1;
}

.parallax-section__mac-button:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Main Image Styling */
.parallax-section__main-image {
  width: 100%;
  height: auto;
  object-fit: cover;
  border-radius: 0 0 16px 16px;
  transition: all 0.3s ease;
  display: block;
}

.image-placeholder__content h3 {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: white;
}

.image-placeholder__content p {
  font-size: 16px;
  margin: 0 0 20px 0;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
}

.image-placeholder__dimensions {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 16px;
  border-radius: 20px;
  display: inline-block;
}

/* Gradient Overlay for readability */
.parallax-section__overlay-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 106, 193, 0.3) 0%,
    rgba(26, 125, 209, 0.2) 50%,
    rgba(52, 144, 225, 0.3) 100%
  );
  z-index: 2;
  border-radius: 40px 40px 0 0;
}

/* Content Section that slides over the parallax */
.parallax-content {
  position: relative;
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 50%, #3490e1 100%);
  color: white;
  z-index: 10;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40px 40px 0 0;
  width: 100%;
  margin: 0;
  padding: 0;
  top: -40px;
}

.parallax-content__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 120px 0;
  text-align: center;
  width: 100%;
}

.parallax-content__text {
  max-width: 800px;
  margin: 0 auto;
}

/* Content Typography */
.parallax-content__title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  line-height: 1.2;
  margin: 0 0 32px 0;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #ffffff 0%, #e5e7eb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.parallax-content__description {
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 48px 0;
  font-weight: 400;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 48px;
}

/* Feature Items */
.parallax-content__features {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 50px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  font-size: 16px;
  font-weight: 500;
  color: white;
  transition: all 0.3s ease;
}

.feature-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, 0.3);
}

.feature-item__icon {
  font-size: 20px;
  line-height: 1;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .parallax-content__container {
    padding: 100px 24px;
  }

  .image-placeholder {
    padding: 40px 30px;
    max-width: 400px;
  }

  .image-placeholder__content h3 {
    font-size: 24px;
  }

  .image-placeholder__content p {
    font-size: 15px;
  }

  .parallax-section__image-container {
    border-radius: 16px;
  }

  .parallax-section__mac-header {
    padding: 10px 12px;
    border-radius: 16px 16px 0 0;
  }

  .parallax-section__mac-button {
    width: 10px;
    height: 10px;
  }

  .parallax-section__mac-buttons {
    gap: 6px;
  }

  .parallax-section__main-image {
    border-radius: 0 0 12px 12px;
  }
}

@media (max-width: 768px) {
  .parallax-section {
    height: 80vh;
  }

  .parallax-content__container {
    padding: 80px 16px;
  }

  .parallax-content__features {
    gap: 24px;
  }

  .feature-item {
    padding: 12px 20px;
    font-size: 14px;
  }

  .feature-item__icon {
    font-size: 18px;
  }

  .image-placeholder {
    padding: 30px 20px;
    max-width: 350px;
  }

  .image-placeholder__content h3 {
    font-size: 20px;
  }

  .image-placeholder__content p {
    font-size: 14px;
  }

  .image-placeholder__dimensions {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .parallax-section {
    height: 70vh;
  }

  .parallax-content__container {
    padding: 60px 12px;
  }

  .parallax-content__features {
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  .feature-item {
    padding: 10px 16px;
    font-size: 13px;
  }

  .image-placeholder {
    padding: 20px 16px;
    max-width: 300px;
  }

  .image-placeholder__content h3 {
    font-size: 18px;
  }

  .image-placeholder__content p {
    font-size: 13px;
  }
}
