/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

/* Features Navigation */
.features-nav {
  background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
  padding: 160px 0 20px 0; /* Increased top padding to account for sticky nav */
  border-bottom: 1px solid rgba(0, 106, 193, 0.1);
  position: relative;
}

.features-nav__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.features-nav__header {
  text-align: center;
  margin-bottom: 40px;
}

.features-nav__title {
  font-size: clamp(2.5rem, 5vw, 3.5rem);
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 16px 0;
  padding-top: 40px;
  line-height: 1.2;
  letter-spacing: -0.02em;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.features-nav__subtitle {
  font-size: 1.25rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
  max-width: 600px;
  margin: 0 auto;
  font-family: "Plus Jakarta Sans", sans-serif;
}

/* Sticky Tabs Wrapper */
.features-nav__tabs-sticky {
  position: fixed;
  top: 72px; /* Just below header (header height is 72px) */
  left: 0;
  right: 0;
  z-index: 999; /* Just below header (1000) but above content */
  background: rgba(248, 250, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  padding: 20px 0;
  border-bottom: 1px solid rgba(0, 106, 193, 0.2);
  box-shadow: 0 4px 20px rgba(0, 106, 193, 0.1);
  transition: all 0.3s ease;
  width: 100%;
}

.features-nav__tabs-sticky.scrolled {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: 0 4px 30px rgba(0, 106, 193, 0.15);
  border-bottom: 1px solid rgba(0, 106, 193, 0.3);
}

/* Horizontal Features Tabs */
.features-nav__tabs {
  background: white;
  border-radius: 12px;
  padding: 8px;
  box-shadow: 0 4px 20px rgba(0, 106, 193, 0.08);
  border: 1px solid rgba(0, 106, 193, 0.1);
  margin: 0 auto;
  max-width: 1200px;
  position: relative;
  overflow: hidden;
}

.features-nav__tabs-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.features-nav__scroll-btn {
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  border: none;
  border-radius: 8px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
  color: white;
}

.features-nav__scroll-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #005aa1 0%, #1a6db1 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 106, 193, 0.3);
}

.features-nav__scroll-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
}

.features-nav__scroll-btn svg {
  width: 16px;
  height: 16px;
}

.features-nav__tabs-container {
  display: flex;
  gap: 4px;
  overflow-x: auto;
  scroll-behavior: smooth;
  scrollbar-width: none;
  -ms-overflow-style: none;
  flex: 1;
  padding: 0 4px;
}

.features-nav__tabs-container::-webkit-scrollbar {
  display: none;
}

.features-nav__tab {
  background: transparent;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  flex-shrink: 0;
  font-family: "Plus Jakarta Sans", sans-serif;
  position: relative;
}

.features-nav__tab:hover {
  background: rgba(0, 106, 193, 0.08);
  color: #006ac1;
}

.features-nav__tab--active {
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  color: white;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 106, 193, 0.2);
}

.features-nav__tab--active:hover {
  background: linear-gradient(135deg, #005aa1 0%, #1a6db1 100%);
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .features-nav {
    padding: 120px 0 20px 0;
  }
  
  .features-nav__title {
    font-size: 2rem;
    padding-top: 20px;
  }
  
  .features-nav__subtitle {
    font-size: 1.1rem;
  }
  
  .features-nav__container {
    padding: 0 16px;
  }
  
  .features-nav__tabs-sticky {
    padding: 16px 0;
  }
  
  .features-nav__tab {
    padding: 10px 16px;
    font-size: 0.9rem;
  }
  
  .features-nav__scroll-btn {
    width: 32px;
    height: 32px;
  }
  
  .features-nav__scroll-btn svg {
    width: 14px;
    height: 14px;
  }
}

@media (max-width: 480px) {
  .features-nav__title {
    font-size: 1.75rem;
  }
  
  .features-nav__subtitle {
    font-size: 1rem;
  }
  
  .features-nav__tab {
    padding: 8px 12px;
    font-size: 0.85rem;
  }
}
