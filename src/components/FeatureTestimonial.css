/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

/* Feature Testimonial Section */
.feature-testimonial {
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
  padding: 100px 0;
  position: relative;
}

.feature-testimonial__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.feature-testimonial__content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 80px;
  align-items: center;
}

.feature-testimonial__text {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.feature-testimonial__quote-icon {
  width: 48px;
  height: 48px;
}

.feature-testimonial__quote-svg {
  width: 100%;
  height: 100%;
}

.feature-testimonial__quote {
  font-size: clamp(1.5rem, 3vw, 2rem);
  font-weight: 400;
  color: #1a1a1a;
  line-height: 1.4;
  font-family: "Plus Jakarta Sans", sans-serif;
  font-style: italic;
  margin: 0;
}

.feature-testimonial__author {
  display: flex;
  align-items: center;
  gap: 16px;
}

.feature-testimonial__author-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.feature-testimonial__author-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a1a1a;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-testimonial__author-title {
  font-size: 1rem;
  font-weight: 500;
  color: #006ac1;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-testimonial__author-location {
  font-size: 0.9rem;
  color: #6b7280;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-testimonial__image {
  position: relative;
}

.feature-testimonial__image-container {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 106, 193, 0.15);
  background: linear-gradient(135deg, #006ac1 0%, #0056a3 100%);
  padding: 8px;
}

.feature-testimonial__img {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 16px;
  transition: transform 0.3s ease;
}

.feature-testimonial__image-decoration {
  position: absolute;
  top: -20px;
  right: -20px;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #006ac1 0%, #0056a3 100%);
  border-radius: 50%;
  opacity: 0.1;
  z-index: -1;
}

.feature-testimonial__image-decoration::before {
  content: '';
  position: absolute;
  top: -40px;
  left: -40px;
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #006ac1 0%, #0056a3 100%);
  border-radius: 50%;
  opacity: 0.05;
}

/* Responsive Design for Feature Testimonial */
@media (max-width: 768px) {
  .feature-testimonial {
    padding: 80px 0;
  }

  .feature-testimonial__container {
    padding: 0 16px;
  }

  .feature-testimonial__content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .feature-testimonial__text {
    gap: 24px;
  }

  .feature-testimonial__quote {
    font-size: 1.25rem;
  }

  .feature-testimonial__author {
    justify-content: center;
  }

  .feature-testimonial__image {
    max-width: 300px;
    margin: 0 auto;
  }
}

@media (max-width: 480px) {
  .feature-testimonial {
    padding: 60px 0;
  }

  .feature-testimonial__content {
    gap: 30px;
  }

  .feature-testimonial__text {
    gap: 20px;
  }

  .feature-testimonial__quote {
    font-size: 1.125rem;
  }

  .feature-testimonial__quote-icon {
    width: 40px;
    height: 40px;
  }

  .feature-testimonial__author-name {
    font-size: 1rem;
  }

  .feature-testimonial__author-title {
    font-size: 0.9rem;
  }

  .feature-testimonial__author-location {
    font-size: 0.8rem;
  }

  .feature-testimonial__image {
    max-width: 250px;
  }
}
