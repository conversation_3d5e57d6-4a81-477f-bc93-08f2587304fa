import { useEffect, useState } from 'react';
import { getImage } from '../utils/images';
import './FullSupport.css';

const FullSupport = () => {
  const [isVisible, setIsVisible] = useState(false);

  // Get images from centralized system
  const supportImages = {
    fastResponse: getImage('fullSupport', 'supportFeatures', 'fastResponse'),
    expertise: getImage('fullSupport', 'supportFeatures', 'expertise'),
    availability: getImage('fullSupport', 'supportFeatures', 'availability')
  };

  // Get the main support team image for CTA section
  const supportTeamImage = getImage('fullSupport', 'supportTeam');

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    const section = document.querySelector('.full-support');
    if (section) {
      observer.observe(section);
    }

    return () => {
      if (section) {
        observer.unobserve(section);
      }
    };
  }, []);

  const supportFeatures = [
    {
      name: "Lightning Fast",
      role: "Response Times",
      title: "Get answers instantly",
      description: "Less than 2min response time during business hours. No waiting, no frustration.",
      avatar: "⚡",
      color: "from-blue-500 to-blue-600",
      image: "🕐",
      step: "01",
      imageKey: "fastResponse"
    },
    {
      name: "Expert Knowledge",
      role: "Home Care Specialists",
      title: "Industry expertise at your fingertips",
      description: "Our team knows home care inside and out. Get solutions from people who understand your business.",
      avatar: "🎯",
      color: "from-purple-500 to-purple-600",
      image: "🧠",
      step: "02",
      imageKey: "expertise"
    },
    {
      name: "Always Available",
      role: "24/7 Support",
      title: "We're here when you need us",
      description: "Round-the-clock support via live chat, email, and phone. Your success never sleeps.",
      avatar: "🌟",
      color: "from-green-500 to-green-600",
      image: "🔄",
      step: "03",
      imageKey: "availability"
    }
  ];

  return (
    <section className={`full-support ${isVisible ? 'full-support--visible' : ''}`}>
      <div className="full-support__container">
        <div className="full-support__header">
          <h2 className="full-support__title">World-Class Support</h2>
          <p className="full-support__subtitle">Experience support that goes above and beyond your expectations</p>
        </div>

        <div className="full-support__team">
          <div className="full-support__flow-line">
            <svg viewBox="0 0 1000 800" preserveAspectRatio="none">
              <defs>
                <linearGradient id="lineGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                  <stop offset="0%" stopColor="#201c44" stopOpacity="0.4"/>
                  <stop offset="50%" stopColor="#201c44" stopOpacity="0.7"/>
                  <stop offset="100%" stopColor="#201c44" stopOpacity="0.4"/>
                </linearGradient>
              </defs>

              {/* Vertical line from card 1 to card 2 */}
              <line
                x1="250"
                y1="200"
                x2="750"
                y2="200"
                stroke="url(#lineGradient)"
                strokeWidth="2"
                strokeDasharray="6,4"
                strokeLinecap="round"
              />

              {/* Vertical line from card 2 to card 3 */}
              <line
                x1="750"
                y1="400"
                x2="250"
                y2="400"
                stroke="url(#lineGradient)"
                strokeWidth="2"
                strokeDasharray="6,4"
                strokeLinecap="round"
              />
            </svg>
          </div>
          {supportFeatures.map((feature, index) => (
            <div
              key={index}
              className={`full-support__member full-support__member--${index + 1}`}
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              <div className="full-support__step-number">
                {feature.step}
              </div>

              <div className="full-support__member-image">
                <img
                  src={supportImages[feature.imageKey].placeholder}
                  alt={supportImages[feature.imageKey].alt}
                  className="full-support__feature-image"
                  // onLoad={(e) => {
                  //   // Replace with actual image when available
                  //   const actualImage = supportImages[feature.imageKey];
                  //   if (actualImage.src !== actualImage.placeholder) {
                  //     e.target.src = actualImage.src;
                  //   }
                  // }}
                  // onError={(e) => {
                  //   // Fallback to placeholder if actual image fails
                  //   e.target.src = supportImages[feature.imageKey].placeholder;
                  // }}
                />
              </div>

              <div className="full-support__member-header">
                <div className={`full-support__avatar bg-gradient-to-br ${feature.color}`}>
                  <span className="full-support__avatar-emoji">{feature.avatar}</span>
                </div>
                <div className="full-support__member-info">
                  <h3 className="full-support__member-name">
                    <strong>{feature.name}</strong>
                  </h3>
                  <p className="full-support__member-role">{feature.role}</p>
                </div>
              </div>

              <div className="full-support__member-content">
                <h4 className="full-support__member-title">
                  <strong>{feature.title}</strong>
                </h4>
                <p className="full-support__member-description">
                  {feature.description}
                </p>
              </div>

              {index < supportFeatures.length - 1 && (
                <div className="full-support__connector">
                  <svg className="full-support__arrow" viewBox="0 0 24 24" fill="none">
                    <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="full-support__cta-section">
          <div className="full-support__cta-content">
            <div className="full-support__cta-image">
              <div className="full-support__image-container">
                <img
                  src={supportTeamImage.placeholder}
                  alt={supportTeamImage.alt}
                  className="full-support__team-image"
                  // onLoad={(e) => {
                  //   // Replace with actual image when available
                  //   if (supportTeamImage.src !== supportTeamImage.placeholder) {
                  //     e.target.src = supportTeamImage.src;
                  //   }
                  // }}
                  // onError={(e) => {
                  //   // Fallback to placeholder if actual image fails
                  //   e.target.src = supportTeamImage.placeholder;
                  // }}
                />

                {/* Rating overlay */}
                <div className="full-support__rating-overlay">
                  <div className="full-support__rating">
                    <div className="full-support__stars">
                      <span>⭐</span>
                      <span>⭐</span>
                      <span>⭐</span>
                      <span>⭐</span>
                      <span>⭐</span>
                    </div>
                    <span className="full-support__rating-text">5.0</span>
                  </div>
                </div>

                {/* Floating decorative elements */}
                <div className="full-support__decor full-support__decor--1">✨</div>
                <div className="full-support__decor full-support__decor--2">🌟</div>
                <div className="full-support__decor full-support__decor--3">💫</div>
                <div className="full-support__decor full-support__decor--4">⚡</div>
                <div className="full-support__decor full-support__decor--5">🎯</div>
                <div className="full-support__decor full-support__decor--6">✨</div>
              </div>
            </div>

            <div className="full-support__cta-text">
              <h3 className="full-support__cta-title">Experience the difference</h3>
              <p className="full-support__cta-subtitle">
                Join thousands of home care agencies who trust our support
              </p>
              <p className="full-support__cta-features">
                98% customer satisfaction • Award-winning support team
              </p>
              <button className="full-support__cta-button">
                Get Support Now
                <svg className="full-support__cta-icon" viewBox="0 0 24 24" fill="none">
                  <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FullSupport;
