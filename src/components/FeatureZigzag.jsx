import { getFeatureImage } from '../utils/featureIMG';
import './FeatureZigzag.css';

const FeatureZigzag = ({ 
  items = [], 
  type = "problems", // "problems" or "why-use"
  className = ""
}) => {
  if (!items || items.length === 0) return null;

  const baseClassName = type === "problems" ? "feature-problems" : "feature-why-use";

  return (
    <section className={`${baseClassName} ${className}`}>
      <div className={`${baseClassName}__container`}>
        <div className={`${baseClassName}__zigzag`}>
          {items.map((item, index) => (
            <div
              key={index}
              className={`${baseClassName}__item ${index % 2 === 1 ? `${baseClassName}__item--reverse` : ''}`}
            >
              {/* Text Content */}
              <div className={`${baseClassName}__content`}>
                {type === "problems" && item.problem && (
                  <div className={`${baseClassName}__problem`}>
                    {item.problem}
                  </div>
                )}

                <h3 className={`${baseClassName}__${type === "problems" ? "solution" : "title"}`}>
                  {type === "problems" ? item.solution : item.title}
                </h3>

                {item.subtitle && (
                  <p className={`${baseClassName}__subtitle`}>
                    {item.subtitle}
                  </p>
                )}

                <ul className={`${baseClassName}__${type === "problems" ? "checklist" : "benefits"}`}>
                  {(item.checks || item.benefits || []).map((checkItem, checkIndex) => (
                    <li key={checkIndex} className={`${baseClassName}__${type === "problems" ? "check-item" : "benefit"}`}>
                      <svg className={`${baseClassName}__check-icon`} viewBox="0 0 24 24" fill="none">
                        <circle cx="12" cy="12" r="10" stroke="#006ac1" strokeWidth="2" fill="none"/>
                        <path d="M8 12l2 2 4-4" stroke="#006ac1" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      {checkItem}
                    </li>
                  ))}
                </ul>

                <button className={`${baseClassName}__cta`}>
                  {item.cta}
                  <svg className={`${baseClassName}__cta-icon`} viewBox="0 0 24 24" fill="none">
                    <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
              </div>

              {/* Image */}
              <div className={`${baseClassName}__image`}>
                <div className={`${baseClassName}__image-container`}>
                  {/* Mac-style Header */}
                  <div className={`${baseClassName}__mac-header`}>
                    <div className={`${baseClassName}__mac-buttons`}>
                      <div className={`${baseClassName}__mac-button ${baseClassName}__mac-button--close`}></div>
                      <div className={`${baseClassName}__mac-button ${baseClassName}__mac-button--minimize`}></div>
                      <div className={`${baseClassName}__mac-button ${baseClassName}__mac-button--maximize`}></div>
                    </div>
                  </div>

                  <img
                    src={(() => {
                      try {
                        return getFeatureImage(
                          type === "problems" ? 'problemSolutions' : 'whyUseSoftware',
                          item.imageKey
                        ).src;
                      } catch (error) {
                        console.warn('FeatureZigzag: Error loading image for', item.imageKey, error);
                        return '/images/placeholder.png';
                      }
                    })()}
                    alt={(() => {
                      try {
                        return getFeatureImage(
                          type === "problems" ? 'problemSolutions' : 'whyUseSoftware',
                          item.imageKey
                        ).alt;
                      } catch (error) {
                        return `${type === "problems" ? 'Problem solution' : 'Software benefit'} illustration`;
                      }
                    })()}
                    className={`${baseClassName}__img`}
                    onError={(e) => {
                      e.target.src = '/images/placeholder.png';
                    }}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeatureZigzag;
