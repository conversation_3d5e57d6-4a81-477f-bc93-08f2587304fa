/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

/* Videos Page */
.videos {
  font-family: "Plus Jakarta Sans", sans-serif;
}

/* Hero Section */
.videos-hero {
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
  padding: 120px 0 80px 0;
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.videos-hero--visible {
  opacity: 1;
  transform: translateY(0);
}

.videos-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(0,106,193,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.5;
}

.videos-hero__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 1;
}

.videos-hero__content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.videos-hero__title {
  font-size: clamp(2.5rem, 4vw, 3.5rem);
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 24px 0;
  letter-spacing: -0.02em;
  line-height: 1.1;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s;
}

.videos-hero--visible .videos-hero__title {
  opacity: 1;
  transform: translateY(0);
}

.videos-hero__subtitle {
  font-size: 1.25rem;
  color: #6b7280;
  margin: 0 0 48px 0;
  line-height: 1.6;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
}

.videos-hero--visible .videos-hero__subtitle {
  opacity: 1;
  transform: translateY(0);
}

.videos-hero__stats {
  display: flex;
  justify-content: center;
  gap: 48px;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.6s;
}

.videos-hero--visible .videos-hero__stats {
  opacity: 1;
  transform: translateY(0);
}

.videos-hero__stat {
  text-align: center;
}

.videos-hero__stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 700;
  color: #006ac1;
  line-height: 1;
  margin-bottom: 8px;
}

.videos-hero__stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

/* Content Section */
.videos-content {
  padding: 100px 0;
  background: white;
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);
}

.videos-content--visible {
  opacity: 1;
  transform: translateY(0);
}

.videos-content__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Filter Section */
.videos-filter {
  margin-bottom: 48px;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s;
}

.videos-content--visible .videos-filter {
  opacity: 1;
  transform: translateY(0);
}

.videos-filter__title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 20px 0;
}

.videos-filter__buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.videos-filter__button {
  padding: 10px 20px;
  border: 2px solid rgba(0, 106, 193, 0.2);
  background: white;
  color: #6b7280;
  border-radius: 25px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.videos-filter__button:hover {
  border-color: #006ac1;
  color: #006ac1;
  transform: translateY(-2px);
}

.videos-filter__button--active {
  background: #006ac1;
  color: white;
  border-color: #006ac1;
}

/* Videos Grid */
.videos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
  margin-bottom: 80px;
}

.video-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 106, 193, 0.08);
  border: 1px solid rgba(0, 106, 193, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease forwards;
}

.videos-content--visible .video-card {
  opacity: 1;
  transform: translateY(0);
}

.video-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 106, 193, 0.15);
}

.video-card__thumbnail {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.video-card__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.video-card:hover .video-card__image {
  transform: scale(1.05);
}

.video-card__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-card:hover .video-card__overlay {
  opacity: 1;
}

.video-card__play-button {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #006ac1;
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.video-card:hover .video-card__play-button {
  transform: scale(1);
}

.video-card__duration {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.video-card__content {
  padding: 24px;
}

.video-card__category {
  display: inline-block;
  background: rgba(0, 106, 193, 0.1);
  color: #006ac1;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  margin-bottom: 12px;
}

.video-card__title-row {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 12px;
  margin-bottom: 12px;
}

.video-card__title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  line-height: 1.4;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.video-card__expand-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 6px;
  transition: all 0.3s ease;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 26px;
  height: 26px;
}

.video-card__expand-btn:hover {
  background: rgba(0, 106, 193, 0.1);
  color: #006ac1;
  transform: scale(1.1);
}

.video-card__description {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.5;
  margin: 0 0 16px 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: all 0.3s ease;
}

.video-card__description--expanded {
  display: block;
  -webkit-line-clamp: unset;
  overflow: visible;
}

.video-card__meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #9ca3af;
}

.video-card__channel {
  display: flex;
  align-items: center;
  gap: 6px;
}

.video-card__stats {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* CTA Section */
.videos-cta {
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  border-radius: 24px;
  padding: 60px 40px;
  text-align: center;
  color: white;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
}

.videos-content--visible .videos-cta {
  opacity: 1;
  transform: translateY(0);
}

.videos-cta__title {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 16px 0;
  letter-spacing: -0.02em;
}

.videos-cta__description {
  font-size: 1.125rem;
  margin: 0 0 32px 0;
  line-height: 1.6;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.videos-cta__buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
}

.videos-cta__button {
  padding: 14px 28px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.videos-cta__button--primary {
  background: white;
  color: #006ac1;
}

.videos-cta__button--primary:hover {
  background: #f8faff;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.videos-cta__button--secondary {
  background: transparent;
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

.videos-cta__button--secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: white;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .videos-hero {
    padding: 100px 0 60px 0;
  }

  .videos-hero__stats {
    gap: 32px;
  }

  .videos-hero__stat-number {
    font-size: 2rem;
  }

  .videos-content {
    padding: 80px 0;
  }

  .videos-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .videos-filter__buttons {
    justify-content: center;
  }

  .videos-cta {
    padding: 40px 24px;
  }

  .videos-cta__title {
    font-size: 1.75rem;
  }

  .videos-cta__buttons {
    flex-direction: column;
    align-items: center;
  }

  .videos-cta__button {
    width: 100%;
    max-width: 280px;
  }
}

@media (max-width: 480px) {
  .videos-hero {
    padding: 80px 0 40px 0;
  }

  .videos-hero__container,
  .videos-content__container {
    padding: 0 16px;
  }

  .videos-content {
    padding: 60px 0;
  }

  .videos-hero__stats {
    flex-direction: column;
    gap: 24px;
  }

  .videos-filter__buttons {
    gap: 8px;
  }

  .videos-filter__button {
    padding: 8px 16px;
    font-size: 0.8125rem;
  }

  .video-card__content {
    padding: 20px;
  }

  .videos-cta {
    padding: 32px 20px;
  }

  .videos-cta__title {
    font-size: 1.5rem;
  }

  .videos-cta__description {
    font-size: 1rem;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
