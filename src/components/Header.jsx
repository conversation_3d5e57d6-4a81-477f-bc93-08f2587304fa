import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import logo from '../assets/logo.svg';
import './Header.css';

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isSolutionsOpen, setIsSolutionsOpen] = useState(false);
  const [isResourcesOpen, setIsResourcesOpen] = useState(false);

  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Feature name to ID mapping
  const featureMapping = {
    'EVV / GPS Clock-In Verification': 'evv-gps',
    'Caregiver App': 'caregiver-app',
    'Scheduling': 'smart-scheduling',
    'Task / ADL Documentation': 'task-tracking',
    'Client eSignature': 'esignature',
    'Client + Family Portals': 'portals',
    'Custom Forms': 'custom-forms',
    'Client / Caregiver Satisfaction Surveys': 'surveys',
    'Text Messaging': 'messaging',
    'Hiring + Onboarding': 'hiring',
    'Caregiver Training': 'training',
    'Background Screening': 'background-screening',
    'Invoicing + Billing': 'invoicing'
  };

  // Navigation function
  const navigateToFeature = (featureName) => {
    const featureId = featureMapping[featureName];
    if (featureId) {
      navigate(`/features/${featureId}`);
      setIsSolutionsOpen(false);
      setIsMobileMenuOpen(false);
    }
  };

  const solutionsData = {
    'Core Features': [
      { name: 'EVV / GPS Clock-In Verification', icon: '📍' },
      { name: 'Caregiver App', icon: '📱' },
      { name: 'Scheduling', icon: '📅' },
      { name: 'Task / ADL Documentation', icon: '📋' },
      { name: 'Client eSignature', icon: '✍️' }
    ],
    'Client & Family': [
      { name: 'Client + Family Portals', icon: '👥' },
      { name: 'Custom Forms', icon: '📝' },
      { name: 'Client / Caregiver Satisfaction Surveys', icon: '⭐' },
      { name: 'Text Messaging', icon: '💬' }
    ],
    'Operations & Growth': [
      { name: 'Hiring + Onboarding', icon: '🎯' },
      { name: 'Background Screening', icon: '🔍' },
      { name: 'Caregiver Training', icon: '🎓' },
      { name: 'Invoicing + Billing', icon: '💰' }
    ]
  };

  const resourcesData = [
    { name: 'Videos', icon: '📺', path: '/videos' },
    { name: 'Blog', icon: '📝', path: '/blog' }
  ];



  return (
    <header className={`header ${isScrolled ? 'header--scrolled' : ''}`}>
      <div className="header__container">
        {/* Logo */}
        <div className="header__logo" onClick={() => navigate('/')} style={{ cursor: 'pointer' }}>
          <img src={logo} alt="Logo" className="header__logo-img" />
        </div>

        {/* Desktop Navigation */}
        <nav className="header__nav">
          <div className="header__nav-item header__nav-item--dropdown">
            <button
              className="header__nav-link"
              onMouseEnter={() => setIsSolutionsOpen(true)}
              onMouseLeave={() => setIsSolutionsOpen(false)}
            >
              Solutions
              <svg className="header__dropdown-icon" viewBox="0 0 24 24" fill="none">
                <path d="M6 9l6 6 6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>

            {/* Solutions Dropdown */}
            <div
              className={`header__dropdown ${isSolutionsOpen ? 'header__dropdown--open' : ''}`}
              onMouseEnter={() => setIsSolutionsOpen(true)}
              onMouseLeave={() => setIsSolutionsOpen(false)}
            >
              <div className="header__dropdown-content">
                {Object.entries(solutionsData).map(([category, items]) => (
                  <div key={category} className="header__dropdown-section">
                    <h4 className="header__dropdown-title">{category}</h4>
                    <ul className="header__dropdown-list">
                      {items.map((item, index) => (
                        <li key={index} className="header__dropdown-item">
                          <button
                            className="header__dropdown-link"
                            onClick={() => navigateToFeature(item.name)}
                          >
                            <span className="header__dropdown-icon">{item.icon}</span>
                            {item.name}
                          </button>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <button className="header__nav-link" onClick={() => navigate('/pricing')}>Pricing</button>

          {/* Resources Dropdown */}
          <div className="header__nav-item header__nav-item--dropdown">
            <button
              className="header__nav-link"
              onMouseEnter={() => setIsResourcesOpen(true)}
              onMouseLeave={() => setIsResourcesOpen(false)}
            >
              Resources
              <svg className="header__dropdown-icon" viewBox="0 0 24 24" fill="none">
                <path d="M6 9l6 6 6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>

            <div
              className={`header__dropdown header__dropdown--simple ${isResourcesOpen ? 'header__dropdown--open' : ''}`}
              onMouseEnter={() => setIsResourcesOpen(true)}
              onMouseLeave={() => setIsResourcesOpen(false)}
            >
              <div className="header__dropdown-content">
                <ul className="header__dropdown-list">
                  {resourcesData.map((item, index) => (
                    <li key={index} className="header__dropdown-item">
                      <button
                        className="header__dropdown-link"
                        onClick={() => {
                          navigate(item.path);
                          setIsResourcesOpen(false);
                        }}
                      >
                        <span className="header__dropdown-icon">{item.icon}</span>
                        {item.name}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>



          <button className="header__nav-link" onClick={() => navigate('/contact')}>Contact Us</button>
        </nav>

        {/* CTA Buttons */}
        <div className="header__cta">
          <button className="header__login-btn">
            <svg className="header__login-icon" viewBox="0 0 24 24" fill="none">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <circle cx="12" cy="7" r="4" stroke="currentColor" strokeWidth="2"/>
            </svg>
            Login
          </button>
          <button className="header__cta-btn">
            Get Started
            <svg className="header__cta-icon" viewBox="0 0 24 24" fill="none">
              <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </div>

        {/* Mobile Menu Button */}
        <button
          className="header__mobile-toggle"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        >
          <span className={`header__hamburger ${isMobileMenuOpen ? 'header__hamburger--open' : ''}`}>
            <span></span>
            <span></span>
            <span></span>
          </span>
        </button>
      </div>

      {/* Mobile Menu */}
      <div className={`header__mobile-menu ${isMobileMenuOpen ? 'header__mobile-menu--open' : ''}`}>
        <nav className="header__mobile-nav">
          <div className="header__mobile-item">
            <button
              className="header__mobile-link"
              onClick={() => setIsSolutionsOpen(!isSolutionsOpen)}
            >
              Solutions
              <svg className={`header__mobile-dropdown-icon ${isSolutionsOpen ? 'header__mobile-dropdown-icon--open' : ''}`} viewBox="0 0 24 24" fill="none">
                <path d="M6 9l6 6 6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>

            <div className={`header__mobile-dropdown ${isSolutionsOpen ? 'header__mobile-dropdown--open' : ''}`}>
              {Object.entries(solutionsData).map(([category, items]) => (
                <div key={category} className="header__mobile-dropdown-section">
                  <h4 className="header__mobile-dropdown-title">{category}</h4>
                  <ul className="header__mobile-dropdown-list">
                    {items.map((item, index) => (
                      <li key={index} className="header__mobile-dropdown-item">
                        <a href="#" className="header__mobile-dropdown-link">
                          <span className="header__mobile-dropdown-icon">{item.icon}</span>
                          {item.name}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>

          <a href="#pricing" className="header__mobile-link">Pricing</a>
          <a href="#blog" className="header__mobile-link">Blog</a>
          <button
            className="header__mobile-link"
            onClick={() => {
              navigate('/contact');
              setIsMobileMenuOpen(false);
            }}
          >
            Contact Us
          </button>

          <button className="header__mobile-cta">
            Get Started
          </button>
        </nav>
      </div>
    </header>
  );
};

export default Header;
