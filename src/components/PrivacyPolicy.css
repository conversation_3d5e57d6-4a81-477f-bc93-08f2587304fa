/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

/* Privacy Policy Page */
.privacy-policy {
  font-family: "Plus Jakarta Sans", sans-serif;
}

/* Animation Keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Hero Section */
.privacy-hero {
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
  padding: 120px 0 80px 0;
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.privacy-hero--visible {
  opacity: 1;
  transform: translateY(0);
}

.privacy-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(0,106,193,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.5;
}

.privacy-hero__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 1;
}

.privacy-hero__content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.privacy-hero__title {
  font-size: clamp(2.5rem, 4vw, 3.5rem);
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 24px 0;
  letter-spacing: -0.02em;
  line-height: 1.1;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s;
}

.privacy-hero--visible .privacy-hero__title {
  opacity: 1;
  transform: translateY(0);
}

.privacy-hero__subtitle {
  font-size: 1.25rem;
  color: #6b7280;
  margin: 0 0 32px 0;
  line-height: 1.6;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
}

.privacy-hero--visible .privacy-hero__subtitle {
  opacity: 1;
  transform: translateY(0);
}

.privacy-hero__meta {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.6s;
}

.privacy-hero--visible .privacy-hero__meta {
  opacity: 1;
  transform: translateY(0);
}

.privacy-hero__revision {
  display: inline-block;
  background: rgba(0, 106, 193, 0.1);
  color: #006ac1;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Content Section */
.privacy-content {
  padding: 100px 0;
  background: white;
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);
}

.privacy-content--visible {
  opacity: 1;
  transform: translateY(0);
}

.privacy-content__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.privacy-content__grid {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 80px;
  align-items: start;
}

/* Table of Contents */
.privacy-toc {
  position: sticky;
  top: 100px;
  opacity: 0;
  transform: translateX(-30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s;
}

.privacy-content--visible .privacy-toc {
  opacity: 1;
  transform: translateX(0);
}

.privacy-toc__content {
  background: #f8faff;
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(0, 106, 193, 0.1);
}

.privacy-toc__title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 20px 0;
}

.privacy-toc__nav {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.privacy-toc__link {
  color: #6b7280;
  text-decoration: none;
  font-size: 0.875rem;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  word-spacing: 0.05em;
}

.privacy-toc__link:hover {
  background: rgba(0, 106, 193, 0.1);
  color: #006ac1;
  border-left-color: #006ac1;
  transform: translateX(4px);
}

/* Main Content */
.privacy-main {
  opacity: 0;
  transform: translateX(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s;
}

.privacy-content--visible .privacy-main {
  opacity: 1;
  transform: translateX(0);
}

.privacy-section {
  margin-bottom: 48px;
  scroll-margin-top: 100px;
}

.privacy-section__title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 24px 0;
  letter-spacing: -0.02em;
  padding-bottom: 12px;
  border-bottom: 2px solid rgba(0, 106, 193, 0.1);
}

.privacy-section__content {
  line-height: 1.7;
  word-spacing: 0.08em;
}

.privacy-section__paragraph {
  font-size: 1rem;
  color: #374151;
  margin: 0 0 16px 0;
  line-height: 1.7;
  word-spacing: 0.1em;
  letter-spacing: 0.01em;
}

.privacy-section__paragraph:last-child {
  margin-bottom: 0;
}

/* Lists */
.privacy-list {
  margin: 16px 0;
  padding-left: 0;
  list-style: none;
}

.privacy-list__item {
  position: relative;
  padding: 8px 0 8px 24px;
  font-size: 1rem;
  color: #374151;
  line-height: 1.7;
  margin-bottom: 8px;
}

.privacy-list__item::before {
  content: '•';
  position: absolute;
  left: 0;
  top: 8px;
  color: #006ac1;
  font-weight: bold;
  font-size: 1.2rem;
}

.privacy-list__item:last-child {
  margin-bottom: 0;
}

/* Subsection titles */
.privacy-subsection__title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 24px 0 12px 0;
}

.privacy-subsection__title:first-child {
  margin-top: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .privacy-content__grid {
    gap: 40px;
  }

  .privacy-toc {
    position: static;
  }
}

@media (max-width: 768px) {
  .privacy-hero {
    padding: 100px 0 60px 0;
  }

  .privacy-content {
    padding: 80px 0;
  }

  .privacy-content__grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .privacy-toc__content {
    padding: 20px;
  }

  .privacy-section__title {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .privacy-hero {
    padding: 80px 0 40px 0;
  }

  .privacy-hero__container,
  .privacy-content__container {
    padding: 0 16px;
  }

  .privacy-content {
    padding: 60px 0;
  }

  .privacy-content__grid {
    gap: 32px;
  }

  .privacy-toc__content {
    padding: 16px;
  }

  .privacy-section {
    margin-bottom: 40px;
  }

  .privacy-section__title {
    font-size: 1.375rem;
  }

  .privacy-section__paragraph {
    font-size: 0.9375rem;
  }
}
