@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap');

/* Core Features Section - Clean & Modern */
.core-features {
  position: relative;
  padding: 120px 0;
  background: #fafbff;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.core-features__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Header */
.core-features__header {
  text-align: center;
  margin-bottom: 80px;
  opacity: 1;
  transform: translateY(0);
  transition: all 0.8s ease-out;
}

.core-features--visible .core-features__header {
  opacity: 1;
  transform: translateY(0);
}

.core-features__label {
  display: inline-block;
  padding: 8px 16px;
  background: rgba(32, 28, 68, 0.05);
  color: #201c44;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-radius: 20px;
  margin-bottom: 24px;
  border: 1px solid rgba(32, 28, 68, 0.1);
}

.core-features__title {
  font-size: clamp(2rem, 4vw, 2.75rem);
  font-weight: 600;
  color: #201c44;
  line-height: 1.3;
  margin: 0;
  letter-spacing: -0.02em;
  max-width: 600px;
  margin: 0 auto;
}

/* Core Features Zigzag Layout */
.core-features__zigzag {
  margin-top: 5rem;
  display: flex;
  flex-direction: column;
  gap: 6rem;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.core-features__item {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  opacity: 1;
  transform: translateY(0);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.core-features--visible .core-features__item {
  opacity: 1;
  transform: translateY(0);
}

/* Reverse layout for odd items */
.core-features__item--reverse {
  direction: rtl;
}

.core-features__item--reverse > * {
  direction: ltr;
}

/* Content Section */
.core-features__content {
  padding: 2rem 0;
}

.core-features__badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: linear-gradient(135deg, rgba(0, 106, 193, 0.1) 0%, rgba(0, 106, 193, 0.05) 100%);
  color: #006ac1;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(0, 106, 193, 0.1);
}

.core-features__badge-icon {
  font-size: 16px;
  line-height: 1;
}

.core-features__item-title {
  font-size: clamp(1.75rem, 4vw, 2.5rem);
  font-weight: 400;
  color: #1a1a1a;
  margin: 0 0 1.5rem 0;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.core-features__item-description {
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.7;
  margin: 0 0 1.5rem 0;
}

/* Checklist Styles */
.core-features__checklist {
  list-style: none;
  padding: 0;
  margin: 0 0 2.5rem 0;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px 32px;
}

.core-features__checklist-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  font-size: 0.9rem;
  color: #4b5563;
  line-height: 1.5;
  font-weight: 400;
}

.core-features__check-icon {
  width: 16px;
  height: 16px;
  color: #10b981;
  background: transparent;
  border: 1.5px solid #10b981;
  border-radius: 50%;
  padding: 3px;
  flex-shrink: 0;
  margin-top: 1px;
}

.core-features__cta {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  font-family: "Plus Jakarta Sans", sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  letter-spacing: -0.01em;
}

.core-features__cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 106, 193, 0.3);
}

.core-features__cta-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;
}

.core-features__cta:hover .core-features__cta-icon {
  transform: translateX(2px);
}

/* View All Features CTA Section */
.core-features__cta-section {
  margin-top: 80px;
  padding: 60px 40px;
  background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
  border-radius: 24px;
  text-align: center;
  border: 1px solid rgba(0, 106, 193, 0.1);
  position: relative;
  overflow: hidden;
}

.core-features__cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, rgba(0, 106, 193, 0.03) 0%, transparent 70%);
  pointer-events: none;
}

.core-features__cta-content {
  position: relative;
  z-index: 2;
  max-width: 600px;
  margin: 0 auto;
}

.core-features__cta-title {
  font-size: clamp(1.75rem, 4vw, 2.25rem);
  font-weight: 500;
  color: #1a1a1a;
  margin: 0 0 16px 0;
  line-height: 1.3;
  letter-spacing: -0.02em;
}

.core-features__cta-description {
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0 0 32px 0;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.core-features__view-all-btn {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  padding: 16px 32px;
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  color: white;
  border: none;
  border-radius: 50px;
  font-size: 16px;
  font-weight: 500;
  font-family: "Plus Jakarta Sans", sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  letter-spacing: -0.01em;
  box-shadow: 0 4px 16px rgba(0, 106, 193, 0.2);
}

.core-features__view-all-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 106, 193, 0.3);
  background: linear-gradient(135deg, #1a7dd1 0%, #3490e1 100%);
}

.core-features__view-all-btn:active {
  transform: translateY(0);
}

.core-features__view-all-icon {
  width: 18px;
  height: 18px;
  transition: transform 0.2s ease;
}

.core-features__view-all-btn:hover .core-features__view-all-icon {
  transform: translateX(3px);
}

/* Image Section */
.core-features__image {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.core-features__image-container {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 106, 193, 0.1);
  background: white;
  transition: all 0.3s ease;
  width: 100%;
  max-width: 600px;
  min-height: 300px;
}

.core-features__image-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 106, 193, 0.15);
}

/* Small image variant for mobile app */
.core-features__image-container--small {
  max-width: 350px;
  margin: 0 auto;
}

/* Mac-style Header */
.core-features__mac-header {
  position: relative;
  background: linear-gradient(180deg, #f6f6f6 0%, #e8e8e8 100%);
  border-bottom: 1px solid #d1d1d1;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-radius: 20px 20px 0 0;
}

.core-features__mac-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.core-features__mac-button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 0.5px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.core-features__mac-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.core-features__mac-button--close {
  background: linear-gradient(135deg, #ff5f57 0%, #ff4444 100%);
}

.core-features__mac-button--close:hover::before {
  content: '×';
  color: #8b0000;
  font-size: 10px;
  font-weight: bold;
  opacity: 1;
}

.core-features__mac-button--minimize {
  background: linear-gradient(135deg, #ffbd2e 0%, #ffaa00 100%);
}

.core-features__mac-button--minimize:hover::before {
  content: '−';
  color: #8b4500;
  font-size: 10px;
  font-weight: bold;
  opacity: 1;
}

.core-features__mac-button--maximize {
  background: linear-gradient(135deg, #28ca42 0%, #20a034 100%);
}

.core-features__mac-button--maximize:hover::before {
  content: '+';
  color: #0d4f1c;
  font-size: 10px;
  font-weight: bold;
  opacity: 1;
}

.core-features__mac-button:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.core-features__feature-image {
  width: 100%;
  height: auto;
  object-fit: contain;
  border-radius: 0 0 16px 16px;
  transition: all 0.3s ease;
  display: block;
}

.core-features__image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
  border-radius: 0 0 16px 16px;
  min-height: 250px;
}

.core-features__image-icon {
  font-size: 4rem;
  opacity: 0.7;
}

.core-features__image-label {
  font-size: 1rem;
  font-weight: 500;
  color: #6b7280;
  text-align: center;
}

/* Animations */
@keyframes cardReveal {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .core-features {
    padding: 80px 0;
  }
  
  .core-features__container {
    padding: 0 16px;
  }
  
  .core-features__header {
    margin-bottom: 60px;
  }
  
  .core-features__zigzag {
    gap: 4rem;
    margin-top: 3rem;
  }

  .core-features__item {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .core-features__item--reverse {
    direction: ltr;
  }

  .core-features__content {
    padding: 1rem 0;
  }
  
  .core-features__label {
    font-size: 13px;
    padding: 6px 14px;
    margin-bottom: 20px;
  }
  
  .core-features__item-title {
    font-size: clamp(1.5rem, 4vw, 2rem);
  }

  .core-features__item-description {
    font-size: 1rem;
  }

  .core-features__image-container {
    border-radius: 16px;
  }

  .core-features__mac-header {
    padding: 10px 12px;
    border-radius: 16px 16px 0 0;
  }

  .core-features__mac-button {
    width: 10px;
    height: 10px;
  }

  .core-features__mac-buttons {
    gap: 6px;
  }

  .core-features__feature-image {
    border-radius: 0 0 12px 12px;
  }

  .core-features__image-container--small {
    max-width: 280px;
  }

  .core-features__checklist {
    gap: 16px 20px;
    margin-bottom: 2rem;
  }

  .core-features__checklist-item {
    font-size: 0.85rem;
  }

  .core-features__check-icon {
    width: 14px;
    height: 14px;
    padding: 2.5px;
    border-width: 1.5px;
  }
}

@media (max-width: 480px) {
  .core-features {
    padding: 60px 0;
  }
  
  .core-features__container {
    padding: 0 12px;
  }
  
  .core-features__header {
    margin-bottom: 40px;
  }
  
  .core-features__grid {
    gap: 20px;
  }
  
  .core-features__card {
    padding: 28px 20px;
  }
  
  .core-features__label {
    font-size: 12px;
    padding: 6px 12px;
    margin-bottom: 16px;
  }
  
  .core-features__icon {
    font-size: 36px;
    margin-bottom: 16px;
  }

  .core-features__image-container {
    border-radius: 12px;
  }

  .core-features__mac-header {
    padding: 8px 10px;
    border-radius: 12px 12px 0 0;
  }

  .core-features__mac-button {
    width: 8px;
    height: 8px;
  }

  .core-features__mac-buttons {
    gap: 5px;
  }

  .core-features__feature-image {
    border-radius: 0 0 8px 8px;
  }

  .core-features__image-container--small {
    max-width: 220px;
  }

  .core-features__checklist {
    gap: 14px;
    grid-template-columns: 1fr;
    margin-bottom: 1.5rem;
  }

  .core-features__checklist-item {
    font-size: 0.8rem;
  }

  .core-features__check-icon {
    width: 12px;
    height: 12px;
    padding: 2px;
    border-width: 1px;
  }

  /* CTA Section Mobile */
  .core-features__cta-section {
    margin-top: 40px;
    padding: 32px 20px;
  }

  .core-features__cta-title {
    font-size: clamp(1.25rem, 4vw, 1.5rem);
    margin-bottom: 12px;
  }

  .core-features__cta-description {
    font-size: 0.9rem;
    margin-bottom: 20px;
  }

  .core-features__view-all-btn {
    padding: 12px 24px;
    font-size: 14px;
  }

  .core-features__view-all-icon {
    width: 16px;
    height: 16px;
  }
}
