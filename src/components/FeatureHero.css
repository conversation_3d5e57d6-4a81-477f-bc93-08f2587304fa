/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

/* Feature Hero Section */
.feature-hero {
  padding: 40px 0 60px 0;
  background: white;
}

.feature-hero__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.feature-hero__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  animation: fadeInUp 0.6s ease-out;
}

/* Left Side - Text Content */
.feature-hero__text {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.feature-hero__badge {
  display: inline-flex;
  align-self: flex-start;
}

.feature-hero__badge-text {
  background: linear-gradient(135deg, rgba(0, 106, 193, 0.1) 0%, rgba(0, 106, 193, 0.05) 100%);
  color: #006ac1;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid rgba(0, 106, 193, 0.1);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-hero__title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.2;
  letter-spacing: -0.02em;
  margin: 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-hero__subtitle {
  font-size: 1.25rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-hero__benefits {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feature-hero__benefit {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  font-size: 1rem;
  color: #374151;
  line-height: 1.5;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-hero__check-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.feature-hero__cta {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.feature-hero__cta-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 16px 32px;
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  color: white;
  border: none;
  border-radius: 50px;
  font-size: 16px;
  font-weight: 500;
  font-family: "Plus Jakarta Sans", sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  box-shadow: 0 4px 15px rgba(0, 106, 193, 0.2);
}

.feature-hero__cta-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 106, 193, 0.3);
  background: linear-gradient(135deg, #1a7dd1 0%, #3490e1 100%);
}

.feature-hero__cta-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;
}

.feature-hero__cta-btn:hover .feature-hero__cta-icon {
  transform: translateX(2px);
}

.feature-hero__demo-btn {
  padding: 16px 32px;
  background: transparent;
  color: #006ac1;
  border: 2px solid rgba(0, 106, 193, 0.2);
  border-radius: 50px;
  font-size: 16px;
  font-weight: 500;
  font-family: "Plus Jakarta Sans", sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
}

.feature-hero__demo-btn:hover {
  background: rgba(0, 106, 193, 0.05);
  border-color: rgba(0, 106, 193, 0.3);
  transform: translateY(-1px);
}

/* Right Side - Images */
.feature-hero__images {
  display: flex;
  flex-direction: column;
  gap: 24px;
  position: relative;
}

/* Web Interface Container */
.feature-hero__web-container {
  background: #f5f5f7;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.feature-hero__mac-header {
  background: #e5e5e7;
  padding: 12px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.feature-hero__mac-buttons {
  display: flex;
  gap: 8px;
}

.feature-hero__mac-button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.feature-hero__mac-button:hover {
  opacity: 0.8;
}

.feature-hero__mac-button--close {
  background: #ff5f57;
}

.feature-hero__mac-button--minimize {
  background: #ffbd2e;
}

.feature-hero__mac-button--maximize {
  background: #28ca42;
}

.feature-hero__web-image {
  background: white;
  padding: 0;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-hero__web-img {
  width: 100%;
  height: auto;
  display: block;
}

/* Mobile Interface Container */
.feature-hero__mobile-container {
  align-self: flex-end;
  max-width: 200px;
  position: relative;
  z-index: 2;
}

.feature-hero__mobile-frame {
  background: #1a1a1a;
  border-radius: 24px;
  padding: 8px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.feature-hero__mobile-img {
  width: 100%;
  height: auto;
  border-radius: 16px;
  display: block;
}

/* Animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .feature-hero__content {
    gap: 60px;
  }

  .feature-hero__mobile-container {
    max-width: 180px;
  }
}

@media (max-width: 768px) {
  .feature-hero {
    padding: 60px 0;
  }

  .feature-hero__container {
    padding: 0 20px;
  }

  .feature-hero__content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .feature-hero__images {
    order: -1;
  }

  .feature-hero__mobile-container {
    max-width: 160px;
    align-self: center;
  }

  .feature-hero__cta {
    flex-direction: column;
    align-items: stretch;
  }

  .feature-hero__cta-btn,
  .feature-hero__demo-btn {
    text-align: center;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .feature-hero {
    padding: 40px 0;
  }

  .feature-hero__container {
    padding: 0 16px;
  }

  .feature-hero__content {
    gap: 30px;
  }

  .feature-hero__text {
    gap: 20px;
  }

  .feature-hero__benefits {
    gap: 12px;
  }

  .feature-hero__benefit {
    font-size: 0.9rem;
  }

  .feature-hero__mobile-container {
    max-width: 140px;
  }

  .feature-hero__cta-btn,
  .feature-hero__demo-btn {
    padding: 14px 24px;
    font-size: 15px;
  }

  .feature-hero__web-image {
    min-height: 200px;
  }
}
