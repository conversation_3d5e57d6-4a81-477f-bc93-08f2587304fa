import { useEffect, useState } from 'react';
import './ReviewBadges.css';

const ReviewBadges = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    const section = document.querySelector('.review-badges');
    if (section) {
      observer.observe(section);
    }

    return () => {
      if (section) {
        observer.unobserve(section);
      }
    };
  }, []);

  const badges = [
    {
      name: "Trustpilot",
      logo: "⭐",
      rating: "4.8/5"
    },
    {
      name: "App Store",
      logo: "📱",
      rating: "4.9/5"
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      logo: "🏆",
      rating: "4.7/5"
    }
  ];

  return (
    <section className={`review-badges ${isVisible ? 'review-badges--visible' : ''}`}>
      <div className="review-badges__container">
        <div className="review-badges__header">
          <h3 className="review-badges__title">5 stars on:</h3>
        </div>

        <div className="review-badges__grid">
          {badges.map((badge, index) => (
            <div 
              key={index}
              className="review-badges__item"
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              <div className="review-badges__logo">
                {badge.logo}
              </div>
              <div className="review-badges__info">
                <div className="review-badges__name">{badge.name}</div>
                <div className="review-badges__stars">
                  <span className="review-badges__rating">★★★★★</span>
                  <span className="review-badges__score">{badge.rating}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ReviewBadges;
