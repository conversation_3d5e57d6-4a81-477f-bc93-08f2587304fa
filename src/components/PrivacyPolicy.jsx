import { useState, useEffect, useRef } from 'react';
import './PrivacyPolicy.css';

const PrivacyPolicy = () => {
  // Animation states
  const [isHeroVisible, setIsHeroVisible] = useState(false);
  const [isContentVisible, setIsContentVisible] = useState(false);
  
  // Refs for intersection observer
  const heroRef = useRef(null);

  // Initial animations on page load
  useEffect(() => {
    // Hero animation
    setTimeout(() => setIsHeroVisible(true), 100);
    
    // Content animation (TOC and main content together)
    setTimeout(() => setIsContentVisible(true), 600);
  }, []);

  const sections = [
    {
      id: 'introduction',
      title: '1. Introduction',
      content: `**123 Consulting Solutions:**
123 Consulting Solutions, LLC, commonly referred to as "123", "We", "Us", or "Our", prioritizes the privacy of our clients and ensures its protection in accordance with this document.

**Purpose:**
This document outlines the nature of data we might collect from you or that you might provide when using our websites and mobile applications.

**Scope of Application:**
This privacy notice is applicable to data collected:
• On our Websites
• Through electronic communications such as emails and texts between you and our Websites

**Exclusions:**
It does not extend to data collected by us outside the digital environment, through other means, or on any third-party websites, even those affiliated with 123 Consulting Solutions.

**Agreement and Revisions:**
Your use of our Websites signifies agreement to this privacy notice. We may modify this notice, and continued use of our Websites after such changes implies acceptance of the new terms. Regular review of this notice is advisable.`
    },
    {
      id: 'data-collection',
      title: '2. Data Collection and Types',
      content: `We gather various kinds of information from users of our Websites, which include:

**Types of Data Collected:**
• Personal identification data (name, address, email, phone number, financial details, demographics, and other information provided by you)
• Non-identifying individual data
• Information related to your internet use, devices used for website access, and usage specifics

**Methods of Collection:**
• *Directly from you* upon provision
• *Automatically* via website navigation (this may include usage specifics, IP addresses, and cookie data)
• *From third-party sources* such as business partners

**Associated Websites:**
• Telescopehr.com
• 123consultingsolutions.com`
    },
    {
      id: 'user-provided',
      title: '3. User-Provided Information',
      content: `The data collected through our Websites may encompass:

• Data provided in website forms (registration, service subscription, training completion, or service requests)
• Communication records, including emails
• Transaction details and order fulfillments
• Your search inquiries on the Websites`
    },
    {
      id: 'automatic-data',
      title: '4. Automatic Data Gathering Techniques',
      content: `In the course of your interaction with our Websites, we employ automated data collection methods to gather:

**Website Interaction Data:**
• Detailed information about your website visits (traffic data, logs, etc.)
• Details about your computer and internet connection (like IP address, operating system, and browser type)

**Data Usage:**
This collected data is generally statistical and non-personal, but can be linked with personal information from other sources. This aids in website improvement and personalization.`
    },
    {
      id: 'third-party-cookies',
      title: '5. Third-Party Cookie Usage',
      content: `**Third-Party Data Collection:**
Third-party entities, including advertisers and content providers, may use cookies on our Websites to gather data about you.

**Data Scope:**
This data could be linked to your personal information or include details of your online activities across various services.

**Our Control:**
We do not control these third-party tracking technologies. For targeted advertising opt-outs, see our information usage and disclosure choices.`
    },
    {
      id: 'data-utilization',
      title: '6. Utilization of Your Data',
      content: `We use the collected data for:

• Website operation and content presentation
• Service and information provision upon your request
• Fulfilling the purposes you provide the data for
• Account notifications
• Obligation fulfillment and rights enforcement under contracts with you
• Notifications about changes to our services or Websites
• Interactive feature facilitation on our Websites
• Other purposes as described at the point of data provision or with your consent`
    },
    {
      id: 'data-disclosure',
      title: '7. Data Disclosure',
      content: `We may disclose your personal information as described in this privacy notice:

• To our affiliates and subsidiaries
• To third-party service providers and business partners
• In events like mergers or asset transfers, where user data might be transferred
• For the specific purposes you provide it
• With your consent
• Under legal requirements, for enforcing our terms of service, and for the protection of our rights, property, or safety`
    },
    {
      id: 'user-choices',
      title: '8. Information Use and Disclosure Choices',
      content: `You have control over your personal information, especially regarding tracking technologies and advertising.`
    },
    {
      id: 'data-access',
      title: '9. Accessing and Amending Your Data',
      content: `You can request access, correction, or deletion of your personal data through email communication with us.`
    },
    {
      id: 'data-security',
      title: '10. Data Security Measures',
      content: `We implement security measures to protect your data from unauthorized access, alteration, and loss.`
    },
    {
      id: 'children-privacy',
      title: '11. Children\'s Privacy',
      content: `Our Websites are not intended for children under 13 years old, and we do not knowingly collect data from them.`
    },
    {
      id: 'notice-updates',
      title: '12. Notice Updates',
      content: `Changes to this privacy notice will be posted on this page. The last revision date is stated at the beginning.`
    },
    {
      id: 'contact-info',
      title: '13. Contact Information',
      content: `For queries or comments about this privacy notice and our practices, contact <NAME_EMAIL>.`
    },
    {
      id: 'google-calendar',
      title: '14. Google Calendar',
      content: `Telescope's use and transfer to any other app of information received from Google APIs will adhere to Google API Services User Data Policy, including the Limited Use requirements. TelescopeHR does not share any user data with third-party tools, including AI models.`
    }
  ];

  return (
    <div className="privacy-policy">
      {/* Hero Section */}
      <section className={`privacy-hero ${isHeroVisible ? 'privacy-hero--visible' : ''}`} ref={heroRef}>
        <div className="privacy-hero__container">
          <div className="privacy-hero__content">
            <h1 className="privacy-hero__title">Privacy Policy</h1>
            <p className="privacy-hero__subtitle">
              We prioritize your privacy and are committed to protecting your personal information. 
              This policy explains how we collect, use, and safeguard your data.
            </p>
            <div className="privacy-hero__meta">
              <span className="privacy-hero__revision">Last Revision: December, 2023</span>
            </div>
          </div>
        </div>
      </section>

      {/* Content Section */}
      <section className={`privacy-content ${isContentVisible ? 'privacy-content--visible' : ''}`}>
        <div className="privacy-content__container">
          <div className="privacy-content__grid">
            {/* Table of Contents */}
            <div className="privacy-toc">
              <div className="privacy-toc__content">
                <h3 className="privacy-toc__title">Table of Contents</h3>
                <nav className="privacy-toc__nav">
                  {sections.map((section, index) => (
                    <a
                      key={section.id}
                      href={`#${section.id}`}
                      className="privacy-toc__link"
                    >
                      {section.title}
                    </a>
                  ))}
                </nav>
              </div>
            </div>

            {/* Privacy Content */}
            <div className="privacy-main">
              {sections.map((section, index) => (
                <div
                  key={section.id}
                  id={section.id}
                  className="privacy-section"
                >
                  <h2 className="privacy-section__title">{section.title}</h2>
                  <div className="privacy-section__content">
                    {section.content.split('\n\n').map((paragraph, pIndex) => {
                      // Handle bold text (text between **)
                      if (paragraph.startsWith('**') && paragraph.includes(':**')) {
                        const title = paragraph.replace(/\*\*(.*?):\*\*/, '$1');
                        return (
                          <h3 key={pIndex} className="privacy-subsection__title">
                            {title}
                          </h3>
                        );
                      }
                      
                      // Handle bullet points
                      if (paragraph.includes('•')) {
                        const items = paragraph.split('•').filter(item => item.trim());
                        return (
                          <ul key={pIndex} className="privacy-list">
                            {items.map((item, itemIndex) => (
                              <li key={itemIndex} className="privacy-list__item">
                                {item.trim().replace(/\*(.*?)\*/g, '<em>$1</em>').split('<em>').map((part, partIndex) => {
                                  if (part.includes('</em>')) {
                                    const [emphasized, rest] = part.split('</em>');
                                    return (
                                      <span key={partIndex}>
                                        <em>{emphasized}</em>
                                        {rest}
                                      </span>
                                    );
                                  }
                                  return part;
                                })}
                              </li>
                            ))}
                          </ul>
                        );
                      }
                      
                      // Regular paragraphs
                      return (
                        <p key={pIndex} className="privacy-section__paragraph">
                          {paragraph.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>').split('<strong>').map((part, partIndex) => {
                            if (part.includes('</strong>')) {
                              const [bold, rest] = part.split('</strong>');
                              return (
                                <span key={partIndex}>
                                  <strong>{bold}</strong>
                                  {rest}
                                </span>
                              );
                            }
                            return part;
                          })}
                        </p>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default PrivacyPolicy;
