import { useState, useEffect, useRef } from 'react';
import './TermsOfService.css';

const TermsOfService = () => {
  // Animation states
  const [isHeroVisible, setIsHeroVisible] = useState(false);
  const [isContentVisible, setIsContentVisible] = useState(false);

  // Refs for intersection observer
  const heroRef = useRef(null);

  // Initial animations on page load
  useEffect(() => {
    // Hero animation
    setTimeout(() => setIsHeroVisible(true), 100);

    // Content animation (TOC and main content together)
    setTimeout(() => setIsContentVisible(true), 600);
  }, []);

  const sections = [
    {
      id: 'introduction',
      title: '1. Introduction',
      content: `This document presents the Terms of Service ("Terms") for 123 Consulting Solutions, LLC, inclusive of its affiliates, collectively referred to as "123". These Terms, in conjunction with a Subscription Order Form signed by both 123 and the Company, referencing these Terms, constitute a legally binding agreement. It governs the Company's access and usage of 123's products and services. Both 123 and the Company are referred to as "Party" individually and "Parties" collectively. "Subscription Order Form" refers to one or more forms agreed upon by 123 and the Company for 123's product or service provision. "Agreement" refers to these Terms along with the applicable Subscription Order Form.`
    },
    {
      id: 'license-services',
      title: '2. License; Services',
      content: `Under this Agreement, including the requisite payment of all Fees, 123 grants the Company a limited, personal, non-exclusive, non-transferable, non-sub-licensable, revocable right to access and use the Services solely for internal business purposes. 123 is committed to performing the Services as defined. "Services" encompass the functionality of the Software made available by 123 as a SaaS service and other services as detailed in the Subscription Order Form. "Software" includes any software used by 123 to provide the Services, along with updates, bug fixes, and related materials. The "Term" denotes the duration of this Agreement as specified in the Subscription Order Form, unless earlier terminated as per these Terms. The Company's usage of the Services is strictly within the Agreement's scope. Company shall not, either directly or indirectly, permit any party to engage in actions such as copying, modifying, or creating derivative works of the Services or Documentation; renting, leasing, lending, or transferring the Services or Documentation; reverse engineering or attempting unauthorized access to the Services; removing proprietary notices; or using the Services in any infringing, misappropriating, or unlawful manner. The Company has no rights to access, reproduce, or use the Software outside the scope of the Agreement.`
    },
    {
      id: 'fees-payment',
      title: '3. Fees and Payment',
      content: `a) Fees: The Company agrees to pay 123 the Fees as outlined in the Subscription Order Form. These Fees may be subject to automatic increases ranging from 3% to 10%. Post the Initial Service Term, 123 may adjust Fees with a sixty-day written notice, effective annually post service commencement. This excludes immediate pass-through increases in third-party charges. Overages exceeding allocated limits will incur additional charges. All applicable sales, use, or related taxes, excluding income taxes, are to be paid or reimbursed by the Customer.`
    },
    {
      id: 'data-ownership',
      title: '4. Ownership and Use of Data',
      content: `123's data collection and usage in connection with the Services are detailed in its Privacy Notice, incorporated herein by reference. For information classified as Protected Health Information under HIPAA, the Business Associate Agreement (BAA) governs, superseding any conflicting terms in these Terms or the Privacy Notice. 123 may de-identify any data and use or disclose it as per applicable law, not subjected to the terms of the BAA.`
    },
    {
      id: 'feedback',
      title: '5. Feedback',
      content: `Any Feedback provided by the Company or its representatives to 123 is freely usable by 123. This includes suggestions, ideas, or intellectual property in the Feedback, for which the Company assigns all rights to 123. Except for the limited rights and licenses granted under the Agreement, no rights or licenses are impliedly granted to the Company regarding 123 Intellectual Property.`
    },
    {
      id: 'term-termination',
      title: '6. Term & Termination',
      content: `a) Term: Each Service begins with a one-year term, automatically renewing for successive one-year terms unless otherwise stated in the Order Form. Unused base transactions expire at renewal or termination.

b) Termination: Material breaches of the Agreement allow for termination by the aggrieved Party, given a thirty-day notice and breach rectification period. Immediate termination is available under specific conditions like cessation of business or bankruptcy. 123's breach results in termination being the Company's sole remedy. Post-termination, all rights to access and use the Services cease, and all 123 materials must be returned within ten days. The Company may continue using any reports or documentation for internal business purposes.`
    },
    {
      id: 'indemnification',
      title: '7. Indemnification',
      content: `The Company agrees to defend, indemnify, and hold harmless 123 from claims arising from its negligence, legal violations, agreement breaches, or data use. The Company will control the defense of any such claims and provide assistance to 123.`
    },
    {
      id: 'liability',
      title: '8. Limitation of Liability',
      content: `123's liability is strictly limited to direct damages, capped at the service fees paid during the preceding twelve months. Liability for lost profits, data loss, or consequential damages is expressly disclaimed. 123 is not liable for performance delays or failures beyond its control.`
    },
    {
      id: 'general',
      title: '9. General',
      content: `Enforcement of rights under the Agreement is not a waiver. The Company cannot assign the Agreement without 123's consent. The Agreement benefits and binds successors and permitted assigns. It is governed by Texas law and exclusive to the jurisdiction of state and federal courts in Houston, Texas. Injunctive relief can be sought by 123 in any competent court. Both parties waive the right to a jury trial for any claim related to the Agreement. If any part of the Agreement is found invalid or unenforceable, the remaining parts remain effective. The Agreement, including any Subscription Order Form, can only be modified through a written instrument signed by both parties. 123 may include the Company's name and logo in its client list unless otherwise notified by the Company. In legal actions to enforce the Agreement, the prevailing party is entitled to reasonable attorney's fees and costs. Enforcement of the Agreement is limited to the parties involved. All rights and remedies provided in these Terms are cumulative and not exclusive, available to each party under law, equity, statute, other agreements, or otherwise.`
    },
    {
      id: 'google-calendar',
      title: '10. Google Calendar',
      content: `Telescope's use and transfer to any other app of information received from Google APIs will adhere to Google API Services User Data Policy, including the Limited Use requirements. TelescopeHR does not share any user data with third-party tools, including AI models.`
    }
  ];

  return (
    <div className="terms-of-service">
      {/* Hero Section */}
      <section className={`terms-hero ${isHeroVisible ? 'terms-hero--visible' : ''}`} ref={heroRef}>
        <div className="terms-hero__container">
          <div className="terms-hero__content">
            <h1 className="terms-hero__title">Terms of Service</h1>
            <p className="terms-hero__subtitle">
              Please read these terms carefully before using our services.
              By accessing or using Telescope, you agree to be bound by these terms.
            </p>
            <div className="terms-hero__meta">
              <span className="terms-hero__revision">Last Revision: December, 2023</span>
            </div>
          </div>
        </div>
      </section>

      {/* Content Section */}
      <section className={`terms-content ${isContentVisible ? 'terms-content--visible' : ''}`}>
        <div className="terms-content__container">
          <div className="terms-content__grid">
            {/* Table of Contents */}
            <div className="terms-toc">
              <div className="terms-toc__content">
                <h3 className="terms-toc__title">Table of Contents</h3>
                <nav className="terms-toc__nav">
                  {sections.map((section, index) => (
                    <a
                      key={section.id}
                      href={`#${section.id}`}
                      className="terms-toc__link"
                    >
                      {section.title}
                    </a>
                  ))}
                </nav>
              </div>
            </div>

            {/* Terms Content */}
            <div className="terms-main">
              {sections.map((section, index) => (
                <div
                  key={section.id}
                  id={section.id}
                  className="terms-section"
                >
                  <h2 className="terms-section__title">{section.title}</h2>
                  <div className="terms-section__content">
                    {section.content.split('\n\n').map((paragraph, pIndex) => (
                      <p key={pIndex} className="terms-section__paragraph">
                        {paragraph}
                      </p>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default TermsOfService;
