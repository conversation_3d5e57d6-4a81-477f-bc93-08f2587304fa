import { useParallax, useIntersectionObserver } from '../hooks/useParallax';
import { useNavigate } from 'react-router-dom';
import './FeatureParallax.css';

const FeatureParallax = ({
  type = "not-done", // "not-done", "manager", "ratings"
  content = {},
  className = ""
}) => {
  const navigate = useNavigate();
  const { sectionRef, backgroundRef } = useParallax(
    type === "not-done" ? 0.5 : type === "manager" ? 0.3 : 0.2
  );
  const { ref: tileRef, isVisible: isTileVisible } = useIntersectionObserver(0.5);
  const { ref: iconsRef, isVisible: areIconsVisible } = useIntersectionObserver(0.3);

  const renderNotDoneContent = () => (
    <div
      ref={tileRef}
      className={`feature-not-done__tile ${isTileVisible ? 'feature-not-done__tile--visible' : ''}`}
    >
      <p className="feature-not-done__text">We're not done yet.</p>
    </div>
  );

  const renderManagerContent = () => (
    <div className="feature-manager-content">
      <div className="feature-manager-text">
        <h2 className="feature-manager-title">
          {content?.title || "Managers Save 5+ hours a week on average with Telescope."}
        </h2>
        <p className="feature-manager-subtitle">
          {content?.subtitle || "Ditch the sticky notes and spreadsheets."}
        </p>
        <button
          className="feature-manager-cta"
          onClick={() => navigate('/contact')}
        >
          {content?.cta || "Get started and thank us later"}
          <svg className="feature-manager-cta-icon" viewBox="0 0 24 24" fill="none">
            <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      </div>

      <div
        ref={iconsRef}
        className={`feature-manager-icons ${areIconsVisible ? 'feature-manager-icons--visible' : ''}`}
      >
        <div className="feature-manager-icon feature-manager-icon--1">
          <svg viewBox="0 0 24 24" fill="none" className="feature-manager-arrow">
            <path d="M5 12h14M12 5l7 7-7 7" stroke="#006ac1" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </div>
        <div className="feature-manager-icon feature-manager-icon--2">
          <svg viewBox="0 0 24 24" fill="none" className="feature-manager-arrow">
            <path d="M5 12h14M12 5l7 7-7 7" stroke="#006ac1" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </div>
      </div>
    </div>
  );

  const renderRatingsContent = () => (
    <div className="feature-ratings__container">
      <div className="feature-ratings__content">
        <div className="feature-ratings__header">
          <h2 className="feature-ratings__title">
            {content?.title || "Trusted by thousands of home care agencies"}
          </h2>
          <p className="feature-ratings__subtitle">
            {content?.subtitle || "Join the agencies that have transformed their EVV compliance with Telescope"}
          </p>
        </div>
        <div className="feature-ratings__grid">
          {(content?.ratings || [
            { stars: 5, rating: "4.9", platform: "Trustpilot", reviews: "2,847 reviews" },
            { stars: 5, rating: "4.8", platform: "App Store", reviews: "1,293 reviews" },
            { stars: 5, rating: "4.9", platform: "Capterra", reviews: "856 reviews" },
            { stars: 5, rating: "4.7", platform: "Google Play", reviews: "3,421 reviews" }
          ]).map((rating, index) => (
            <div key={index} className="feature-ratings__card">
              <div className="feature-ratings__stars">
                {[...Array(rating.stars)].map((_, starIndex) => (
                  <svg key={starIndex} className="feature-ratings__star" viewBox="0 0 24 24" fill="#FFD700">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                ))}
              </div>
              <div className="feature-ratings__score">{rating.rating}</div>
              <div className="feature-ratings__platform">{rating.platform}</div>
              <div className="feature-ratings__reviews">{rating.reviews}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const getContainerClass = () => {
    switch (type) {
      case "manager":
        return "feature-manager-parallax-container";
      case "ratings":
        return "feature-ratings-parallax-container";
      default:
        return "feature-parallax-container";
    }
  };

  const getSectionClass = () => {
    switch (type) {
      case "manager":
        return "feature-manager-parallax-section";
      case "ratings":
        return "feature-ratings-parallax-section";
      default:
        return "feature-parallax-section";
    }
  };

  const getBackgroundClass = () => {
    switch (type) {
      case "manager":
        return "feature-manager-parallax-background";
      case "ratings":
        return "feature-ratings-parallax-background";
      default:
        return "feature-parallax-background";
    }
  };

  const getBgClass = () => {
    switch (type) {
      case "manager":
        return "feature-manager-parallax-bg";
      case "ratings":
        return "feature-ratings-parallax-bg";
      default:
        return "feature-parallax-bg";
    }
  };

  const renderContent = () => {
    switch (type) {
      case "manager":
        return renderManagerContent();
      case "ratings":
        return renderRatingsContent();
      default:
        return renderNotDoneContent();
    }
  };

  return (
    <div className={`${getContainerClass()} ${className}`}>
      <section className={getSectionClass()} ref={sectionRef}>
        <div className={getBackgroundClass()} ref={backgroundRef}>
          <div className={getBgClass()}>
            {renderContent()}
          </div>
        </div>
      </section>
    </div>
  );
};

export default FeatureParallax;
