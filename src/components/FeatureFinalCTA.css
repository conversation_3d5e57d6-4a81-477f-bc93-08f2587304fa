/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

/* Feature Final CTA Section */
.feature-final-cta {
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
  padding: 120px 0;
  position: relative;
  overflow: hidden;
}

.feature-final-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(0,106,193,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.5;
}

.feature-final-cta__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 1;
}

.feature-final-cta__grid {
  display: flex;
  flex-direction: column;
  gap: 60px;
  align-items: center;
  max-width: 800px;
  margin: 0 auto;
}

.feature-final-cta__content {
  color: #1a1a1a;
  text-align: center;
}

.feature-final-cta__title {
  font-size: clamp(2.5rem, 4vw, 3.5rem);
  font-weight: 500;
  margin: 0 0 24px 0;
  line-height: 1.2;
  letter-spacing: -0.02em;
  font-family: "Plus Jakarta Sans", sans-serif;
  color: #1a1a1a;
}

.feature-final-cta__subtitle {
  font-size: 1.25rem;
  font-weight: 400;
  line-height: 1.5;
  margin: 0 0 40px 0;
  color: #6b7280;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-final-cta__stats {
  display: flex;
  gap: 40px;
  margin-top: 40px;
  justify-content: center;
}

.feature-final-cta__stat {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.feature-final-cta__stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #006ac1;
  line-height: 1;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-final-cta__stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
  margin-top: 4px;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-final-cta__card {
  background: white;
  border-radius: 24px;
  padding: 40px;
  box-shadow: 0 20px 60px rgba(0, 106, 193, 0.1);
  border: 1px solid rgba(0, 106, 193, 0.1);
  position: relative;
  width: 100%;
  max-width: 500px;
}

.feature-final-cta__card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #006ac1 0%, #1a7dd1 100%);
  border-radius: 24px 24px 0 0;
}

.feature-final-cta__card-header {
  text-align: center;
  margin-bottom: 32px;
}

.feature-final-cta__card-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 8px 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-final-cta__card-subtitle {
  font-size: 1rem;
  color: #6b7280;
  margin: 0;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-final-cta__buttons {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 32px;
}

.feature-final-cta__primary-btn {
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  color: white;
  border: none;
  padding: 18px 24px;
  border-radius: 12px;
  font-size: 1.125rem;
  font-weight: 600;
  font-family: "Plus Jakarta Sans", sans-serif;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  box-shadow: 0 8px 25px rgba(0, 106, 193, 0.25);
  position: relative;
  overflow: hidden;
}

.feature-final-cta__primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(0, 106, 193, 0.35);
  color: white;
  text-decoration: none;
}

.feature-final-cta__secondary-btn {
  background: transparent;
  color: #006ac1;
  border: 2px solid #006ac1;
  padding: 16px 24px;
  border-radius: 12px;
  font-size: 1.125rem;
  font-weight: 600;
  font-family: "Plus Jakarta Sans", sans-serif;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.feature-final-cta__secondary-btn:hover {
  background: #006ac1;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 106, 193, 0.25);
  text-decoration: none;
}

.feature-final-cta__btn-icon {
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
  flex-shrink: 0;
}

.feature-final-cta__primary-btn:hover .feature-final-cta__btn-icon {
  transform: scale(1.1);
}

.feature-final-cta__secondary-btn:hover .feature-final-cta__btn-icon {
  transform: scale(1.1);
}

.feature-final-cta__features {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding-top: 24px;
  border-top: 1px solid rgba(0, 106, 193, 0.1);
}

.feature-final-cta__feature {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.875rem;
  color: #374151;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-final-cta__feature-icon {
  width: 16px;
  height: 16px;
  color: #10b981;
  flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .feature-final-cta__grid {
    gap: 50px;
  }

  .feature-final-cta__stats {
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .feature-final-cta {
    padding: 80px 0;
  }

  .feature-final-cta__container {
    padding: 0 16px;
  }

  .feature-final-cta__grid {
    gap: 40px;
  }

  .feature-final-cta__subtitle {
    font-size: 1.125rem;
    margin-bottom: 32px;
  }

  .feature-final-cta__stats {
    gap: 20px;
  }

  .feature-final-cta__stat-number {
    font-size: 1.75rem;
  }

  .feature-final-cta__card {
    padding: 32px;
  }

  .feature-final-cta__card-title {
    font-size: 1.5rem;
  }

  .feature-final-cta__primary-btn,
  .feature-final-cta__secondary-btn {
    padding: 16px 20px;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .feature-final-cta {
    padding: 60px 0;
  }

  .feature-final-cta__grid {
    gap: 32px;
  }

  .feature-final-cta__subtitle {
    font-size: 1rem;
    margin-bottom: 24px;
  }

  .feature-final-cta__stats {
    flex-wrap: wrap;
    gap: 16px;
    justify-content: center;
  }

  .feature-final-cta__stat-number {
    font-size: 1.5rem;
  }

  .feature-final-cta__card {
    padding: 24px;
  }

  .feature-final-cta__card-title {
    font-size: 1.25rem;
  }

  .feature-final-cta__primary-btn,
  .feature-final-cta__secondary-btn {
    padding: 14px 18px;
    font-size: 0.95rem;
  }

  .feature-final-cta__btn-icon {
    width: 18px;
    height: 18px;
  }

  .feature-final-cta__feature-icon {
    width: 14px;
    height: 14px;
  }

  .feature-final-cta__feature {
    font-size: 0.8rem;
  }
}

/* Animation effects */
@media (prefers-reduced-motion: no-preference) {
  .feature-final-cta__content {
    animation: fadeInLeft 0.8s ease-out;
  }

  .feature-final-cta__card {
    animation: fadeInRight 0.8s ease-out 0.2s both;
  }

  .feature-final-cta__stats {
    animation: fadeInUp 0.8s ease-out 0.4s both;
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus states for accessibility */
.feature-final-cta__primary-btn:focus,
.feature-final-cta__secondary-btn:focus {
  outline: 3px solid #006ac1;
  outline-offset: 2px;
}

.feature-final-cta__primary-btn:focus:not(:focus-visible),
.feature-final-cta__secondary-btn:focus:not(:focus-visible) {
  outline: none;
}

/* Enhanced visual effects */
.feature-final-cta__primary-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
  border-radius: inherit;
}

.feature-final-cta__primary-btn:hover::before {
  transform: translateX(100%);
}

/* Loading state */
.feature-final-cta__primary-btn--loading,
.feature-final-cta__secondary-btn--loading {
  pointer-events: none;
  opacity: 0.7;
}

.feature-final-cta__primary-btn--loading .feature-final-cta__btn-icon,
.feature-final-cta__secondary-btn--loading .feature-final-cta__btn-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
