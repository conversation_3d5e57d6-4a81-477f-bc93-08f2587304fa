import './FeatureTrustedBy.css';

const FeatureTrustedBy = ({
  title = "Trusted by 500 agencies",
  companies = [
    { name: "Endearing Care Services", logo: "/agencies/Endearing Care Services.png" },
    { name: "Fairbranch Health Services", logo: "/agencies/Fairbranch Health Services.png" },
    { name: "Genuine Global Care", logo: "/agencies/Genuine Global Care.png" },
    { name: "Heart to Hart", logo: "/agencies/Heart to Hart.png" },
    { name: "<PERSON>'s Heart Homecare", logo: "/agencies/Helen's Heart Homecare.png" },
    { name: "Helping Hands Superior Care", logo: "/agencies/Helping Hands Superior Care.png" },
    { name: "In the Beginning", logo: "/agencies/In the Beginning Logo.jpg" },
    { name: "Infinito", logo: "/agencies/Infinito Logo.png" },
    { name: "Key to Care", logo: "/agencies/Key to Care.png" },
    { name: "KM Home Care", logo: "/agencies/KM Home Care Logo.png" },
    { name: "Life View Homecare Services", logo: "/agencies/Life View Homecare Services.jpeg" },
    { name: "Light Bearers Home Care", logo: "/agencies/Light Bearers Home Care.png" },
    { name: "Medlink Homecare", logo: "/agencies/Medlink Homecare.jpg" },
    { name: "Perfect Touch", logo: "/agencies/Perfect Touch Logo.jpg" },
    { name: "Positive Home Care Services", logo: "/agencies/Positive Home Care Services (1).png" }
  ]
}) => {
  return (
    <section className="feature-trusted-by">
      <div className="feature-trusted-by__container">
        <div className="feature-trusted-by__header">
          <h2 className="feature-trusted-by__title">{title}</h2>
        </div>

        <div className="feature-trusted-by__marquee">
          <div className="feature-trusted-by__marquee-track">
            {/* First set of companies */}
            {companies.map((company, index) => (
              <div key={`first-${index}`} className="feature-trusted-by__company">
                <div className="feature-trusted-by__company-logo">
                  <img
                    src={company.logo}
                    alt={`${company.name} logo`}
                    className="feature-trusted-by__company-logo-img"
                  />
                </div>
              </div>
            ))}

            {/* Duplicate set for seamless loop */}
            {companies.map((company, index) => (
              <div key={`second-${index}`} className="feature-trusted-by__company">
                <div className="feature-trusted-by__company-logo">
                  <img
                    src={company.logo}
                    alt={`${company.name} logo`}
                    className="feature-trusted-by__company-logo-img"
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeatureTrustedBy;
