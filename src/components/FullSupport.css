@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap');

/* Full Support Section */
.full-support {
  padding: 120px 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
  font-family: "Plus Jakarta Sans", sans-serif;
  position: relative;
}

.full-support__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Header */
.full-support__header {
  text-align: center;
  margin-bottom: 4rem;
}

.full-support__title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 400;
  color: #1a1a1a;
  margin: 0 0 1rem 0;
  line-height: 1.2;
  letter-spacing: -0.02em;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease-out;
}

.full-support--visible .full-support__title {
  opacity: 1;
  transform: translateY(0);
}

.full-support__subtitle {
  font-size: 1.25rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.6;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease-out 0.2s;
}

.full-support--visible .full-support__subtitle {
  opacity: 1;
  transform: translateY(0);
}

/* Team Section - Zigzag Layout */
.full-support__team {
  display: flex;
  flex-direction: column;
  gap: 4rem;
  margin-bottom: 4rem;
  position: relative;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

/* Clean Curved Flow Line */
.full-support__flow-line {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  z-index: 1;
  pointer-events: none;
  overflow: hidden;
}

.full-support__flow-line svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 2px 4px rgba(0, 106, 193, 0.1));
}

.full-support__flow-line line {
  stroke-dasharray: 6, 4;
  stroke-dashoffset: 100;
  animation: drawDottedLine 1.5s ease-in-out forwards;
}

.full-support__flow-line line:nth-child(2) {
  animation-delay: 0.8s;
}

.full-support__flow-line line:nth-child(3) {
  animation-delay: 1.6s;
}

@keyframes drawDottedLine {
  to {
    stroke-dashoffset: 0;
  }
}

.full-support--visible .full-support__flow-line line {
  animation-play-state: running;
}

@media (max-width: 768px) {
  .full-support__team {
    gap: 3rem;
  }

  .full-support__flow-line {
    display: none;
  }
}

.full-support__member {
  background: white;
  border-radius: 24px;
  padding: 2.5rem;
  box-shadow: 0 10px 30px rgba(0, 106, 193, 0.08);
  border: 1px solid rgba(0, 106, 193, 0.06);
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(20px);
  animation: memberSlideIn 0.6s ease-out forwards;
  position: relative;
  z-index: 2;
  max-width: 500px;
  display: grid;
  grid-template-columns: auto 1fr;
  grid-template-rows: auto auto auto;
  gap: 1.5rem;
  align-items: start;
}

/* Zigzag Positioning */
.full-support__member--1 {
  margin-left: 0;
  margin-right: auto;
}

.full-support__member--2 {
  margin-left: auto;
  margin-right: 0;
}

.full-support__member--3 {
  margin-left: 0;
  margin-right: auto;
}

.full-support--visible .full-support__member {
  animation-play-state: running;
}

@keyframes memberSlideIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.full-support__member:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 106, 193, 0.12);
  border-color: rgba(0, 106, 193, 0.1);
}

/* Step Number */
.full-support__step-number {
  grid-column: 1;
  grid-row: 1;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  box-shadow: 0 4px 15px rgba(0, 106, 193, 0.3);
  position: relative;
  z-index: 3;
}

/* Member Image */
.full-support__member-image {
  grid-column: 2;
  grid-row: 1;
  width: 80px;
  height: 80px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  justify-self: end;
  overflow: hidden;
}

.full-support__feature-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 16px;
  transition: all 0.3s ease;
}

.full-support__feature-image:hover {
  transform: scale(1.1);
}

.full-support__member-header {
  grid-column: 1 / -1;
  grid-row: 2;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.full-support__avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  box-shadow: 0 4px 15px rgba(0, 106, 193, 0.2);
}

.full-support__avatar-emoji {
  font-size: 1.5rem;
  line-height: 1;
}

.full-support__member-info {
  flex: 1;
}

.full-support__member-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #006ac1;
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
}

.full-support__member-role {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  font-weight: 500;
}

.full-support__member-content {
  grid-column: 1 / -1;
  grid-row: 3;
  padding-left: 0;
}

/* Connector Arrow - Clean Design */
.full-support__connector {
  position: absolute;
  bottom: -1.5rem;
  z-index: 3;
  transition: all 0.3s ease;
  opacity: 0;
  animation: fadeInArrow 0.8s ease-out 1.5s forwards;
}

@keyframes fadeInArrow {
  to {
    opacity: 1;
  }
}

.full-support__member--1 .full-support__connector {
  right: -1.5rem;
  transform: rotate(0deg);
}

.full-support__member--2 .full-support__connector {
  left: -1.5rem;
  transform: rotate(180deg);
}

.full-support__arrow {
  width: 24px;
  height: 24px;
  color: #006ac1;
  background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
  border-radius: 50%;
  padding: 3px;
  box-shadow: 0 3px 8px rgba(0, 106, 193, 0.12), 0 1px 3px rgba(0, 106, 193, 0.08);
  border: 1.5px solid #006ac1;
  transition: all 0.3s ease;
}

.full-support__arrow:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 106, 193, 0.18), 0 2px 4px rgba(0, 106, 193, 0.12);
  background: linear-gradient(135deg, #f8faff 0%, #e5e7eb 100%);
}

.full-support__member-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #006ac1;
  margin: 0 0 0.75rem 0;
  line-height: 1.4;
}

.full-support__member-description {
  font-size: 1rem;
  color: #4b5563;
  line-height: 1.6;
  margin: 0;
}

/* CTA Section */
.full-support__cta-section {
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  border-radius: 24px;
  padding: 3rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.full-support__cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="20" cy="80" r="0.5" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  z-index: 1;
}

.full-support__cta-content {
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 4rem;
  align-items: center;
}

.full-support__cta-title {
  font-size: 2rem;
  font-weight: 600;
  color: white;
  margin: 0 0 1rem 0;
  line-height: 1.3;
}

.full-support__cta-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 0.5rem 0;
  line-height: 1.5;
}

.full-support__cta-features {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0 0 2rem 0;
  line-height: 1.5;
}

.full-support__cta-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 16px 32px;
  background: white;
  color: #006ac1;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  font-family: "Plus Jakarta Sans", sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  letter-spacing: -0.01em;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.full-support__cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  background: #f8faff;
}

.full-support__cta-icon {
  width: 18px;
  height: 18px;
  transition: transform 0.2s ease;
}

.full-support__cta-button:hover .full-support__cta-icon {
  transform: translateX(2px);
}

/* CTA Image Section */
.full-support__cta-image {
  position: relative;
  flex-shrink: 0;
}

.full-support__image-container {
  position: relative;
  width: 280px;
  height: 280px;
}

.full-support__team-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  animation: float 3s ease-in-out infinite;
  transition: all 0.3s ease;
}

.full-support__team-image:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.full-support__image-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.full-support__main-icon {
  font-size: 4rem;
  line-height: 1;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.full-support__rating {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.full-support__stars {
  display: flex;
  gap: 4px;
  font-size: 1.5rem;
  line-height: 1;
}

.full-support__stars span {
  animation: sparkle 2s ease-in-out infinite;
}

.full-support__stars span:nth-child(1) { animation-delay: 0s; }
.full-support__stars span:nth-child(2) { animation-delay: 0.2s; }
.full-support__stars span:nth-child(3) { animation-delay: 0.4s; }
.full-support__stars span:nth-child(4) { animation-delay: 0.6s; }
.full-support__stars span:nth-child(5) { animation-delay: 0.8s; }

@keyframes sparkle {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

.full-support__rating-text {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  font-size: 1.25rem;
}

/* Rating Overlay */
.full-support__rating-overlay {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 8px 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.full-support__rating-overlay .full-support__rating {
  flex-direction: row;
  align-items: center;
  gap: 8px;
}

.full-support__rating-overlay .full-support__rating-text {
  color: #006ac1;
  font-size: 1rem;
}

/* Floating Decorative Elements */
.full-support__decor {
  position: absolute;
  font-size: 2rem;
  opacity: 0.8;
  animation: floatDecor 4s ease-in-out infinite;
  pointer-events: none;
}

.full-support__decor--1 {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.full-support__decor--2 {
  top: 20%;
  right: 15%;
  animation-delay: 0.5s;
}

.full-support__decor--3 {
  bottom: 30%;
  left: 5%;
  animation-delay: 1s;
}

.full-support__decor--4 {
  bottom: 15%;
  right: 10%;
  animation-delay: 1.5s;
}

.full-support__decor--5 {
  top: 50%;
  left: -10%;
  animation-delay: 2s;
}

.full-support__decor--6 {
  top: 60%;
  right: -5%;
  animation-delay: 2.5s;
}

@keyframes floatDecor {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.6;
  }
  25% {
    transform: translateY(-15px) rotate(90deg);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
    opacity: 1;
  }
  75% {
    transform: translateY(-20px) rotate(270deg);
    opacity: 0.8;
  }
}

/* Visibility Animation */
.full-support {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.full-support--visible {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .full-support {
    padding: 80px 0;
  }

  .full-support__container {
    padding: 0 16px;
  }

  .full-support__header {
    margin-bottom: 3rem;
  }

  .full-support__team {
    gap: 2rem;
    margin-bottom: 3rem;
  }

  .full-support__member {
    padding: 2rem;
    grid-template-columns: auto 1fr;
    gap: 1rem;
    max-width: 100%;
  }

  /* Reset zigzag on tablet */
  .full-support__member--1,
  .full-support__member--2,
  .full-support__member--3 {
    margin-left: auto;
    margin-right: auto;
  }

  .full-support__member-image {
    width: 60px;
    height: 60px;
  }

  .full-support__image-icon {
    font-size: 1.5rem;
  }

  .full-support__connector {
    display: none;
  }

  .full-support__cta-section {
    padding: 2rem;
  }

  .full-support__cta-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .full-support__image-container {
    width: 200px;
    height: 200px;
    margin: 0 auto;
  }

  .full-support__main-icon {
    font-size: 3rem;
  }

  .full-support__stars {
    font-size: 1.25rem;
  }

  .full-support__decor {
    font-size: 1.5rem;
  }

  .full-support__cta-title {
    font-size: 1.5rem;
  }

  .full-support__cta-subtitle {
    font-size: 1.125rem;
  }
}

@media (max-width: 480px) {
  .full-support {
    padding: 60px 0;
  }

  .full-support__header {
    margin-bottom: 2rem;
  }

  .full-support__team {
    margin-bottom: 2rem;
  }

  .full-support__member {
    padding: 1.5rem;
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto auto;
    text-align: center;
    gap: 1rem;
    max-width: 100%;
  }

  /* Reset all zigzag positioning on mobile */
  .full-support__member--1,
  .full-support__member--2,
  .full-support__member--3 {
    margin-left: auto;
    margin-right: auto;
  }

  .full-support__step-number {
    grid-column: 1;
    grid-row: 1;
    justify-self: center;
  }

  .full-support__member-image {
    grid-column: 1;
    grid-row: 2;
    justify-self: center;
    width: 50px;
    height: 50px;
  }

  .full-support__image-icon {
    font-size: 1.25rem;
  }

  .full-support__member-header {
    grid-column: 1;
    grid-row: 3;
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .full-support__member-content {
    grid-column: 1;
    grid-row: 4;
  }

  .full-support__connector {
    display: none;
  }

  .full-support__cta-section {
    padding: 1.5rem;
  }

  .full-support__image-container {
    width: 160px;
    height: 160px;
  }

  .full-support__main-icon {
    font-size: 2.5rem;
  }

  .full-support__stars {
    font-size: 1.125rem;
  }

  .full-support__decor {
    font-size: 1.25rem;
  }

  .full-support__cta-button {
    padding: 14px 28px;
    font-size: 14px;
  }
}
