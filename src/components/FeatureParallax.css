/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

/* Feature Parallax Container */
.feature-parallax-container {
  position: relative;
  font-family: "Plus Jakarta Sans", sans-serif;
  width: 100%;
  overflow: hidden;
}

.feature-parallax-section {
  position: relative;
  height: 100vh;
  min-height: 600px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-parallax-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 120%;
  will-change: transform;
}

.feature-parallax-bg {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #006ac1 0%, #0056a3 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* Not Done Tile */
.feature-not-done__tile {
  background: #006ac1;
  border-radius: 20px;
  padding: 60px 80px;
  box-shadow: 0 30px 60px rgba(0, 106, 193, 0.3);
  border: none;
  transform: translateY(50px);
  opacity: 0;
  transition: all 0.8s ease;
}

.feature-not-done__tile--visible {
  transform: translateY(0);
  opacity: 1;
}

.feature-not-done__text {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 400;
  color: white;
  margin: 0;
  text-align: center;
  font-style: italic;
  letter-spacing: -0.02em;
  font-family: "Plus Jakarta Sans", sans-serif;
}

/* Manager Parallax */
.feature-manager-parallax-container {
  position: relative;
  font-family: "Plus Jakarta Sans", sans-serif;
  width: 100%;
  overflow: hidden;
}

.feature-manager-parallax-section {
  position: relative;
  height: 100vh;
  min-height: 600px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-manager-parallax-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 120%;
  will-change: transform;
}

.feature-manager-parallax-bg {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1a7dd1 0%, #006ac1 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.feature-manager-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 80px;
  align-items: center;
  max-width: 1200px;
  padding: 0 24px;
  width: 100%;
}

.feature-manager-text {
  color: white;
}

.feature-manager-title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 500;
  margin: 0 0 20px 0;
  line-height: 1.2;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-manager-subtitle {
  font-size: 1.25rem;
  margin: 0 0 30px 0;
  opacity: 0.9;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-manager-cta {
  background: white;
  color: #006ac1;
  border: 2px solid white;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  font-family: "Plus Jakarta Sans", sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.feature-manager-cta:hover {
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
  color: #006ac1;
  text-decoration: none;
}

.feature-manager-cta-icon {
  width: 18px;
  height: 18px;
  transition: transform 0.3s ease;
  flex-shrink: 0;
}

.feature-manager-cta:hover .feature-manager-cta-icon {
  transform: translateX(4px);
}

.feature-manager-icons {
  display: flex;
  flex-direction: column;
  gap: 40px;
  opacity: 0;
  transform: translateX(50px);
  transition: all 0.8s ease;
}

.feature-manager-icons--visible {
  opacity: 1;
  transform: translateX(0);
}

.feature-manager-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(255, 255, 255, 0.2);
  animation: float 3s ease-in-out infinite;
}

.feature-manager-icon--2 {
  animation-delay: 1.5s;
  margin-left: 40px;
}

.feature-manager-arrow {
  width: 24px;
  height: 24px;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Ratings Parallax */
.feature-ratings-parallax-container {
  position: relative;
  font-family: "Plus Jakarta Sans", sans-serif;
  width: 100%;
  overflow: hidden;
}

.feature-ratings-parallax-section {
  position: relative;
  height: 100vh;
  min-height: 600px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-ratings-parallax-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 120%;
  will-change: transform;
}

.feature-ratings-parallax-bg {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0056a3 0%, #003d7a 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.feature-ratings__container {
  max-width: 1200px;
  padding: 0 24px;
  width: 100%;
}

.feature-ratings__content {
  text-align: center;
  color: white;
}

.feature-ratings__header {
  margin-bottom: 60px;
}

.feature-ratings__title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 500;
  margin: 0 0 20px 0;
  line-height: 1.2;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-ratings__subtitle {
  font-size: 1.25rem;
  margin: 0;
  opacity: 0.9;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-ratings__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.feature-ratings__card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 30px 20px;
  text-align: center;
  transition: all 0.3s ease;
}

.feature-ratings__card:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-5px);
}

.feature-ratings__stars {
  display: flex;
  justify-content: center;
  gap: 4px;
  margin-bottom: 15px;
}

.feature-ratings__star {
  width: 20px;
  height: 20px;
}

.feature-ratings__score {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 10px;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-ratings__platform {
  font-size: 1.125rem;
  font-weight: 500;
  margin-bottom: 5px;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-ratings__reviews {
  font-size: 0.9rem;
  opacity: 0.8;
  font-family: "Plus Jakarta Sans", sans-serif;
}

/* Responsive Design */
@media (max-width: 768px) {
  .feature-parallax-container,
  .feature-manager-parallax-container,
  .feature-ratings-parallax-container {
    width: 100%;
  }

  .feature-parallax-section,
  .feature-manager-parallax-section,
  .feature-ratings-parallax-section {
    min-height: 500px;
    height: 80vh;
  }

  .feature-not-done__tile {
    padding: 40px 50px;
    margin: 0 20px;
  }

  .feature-not-done__text {
    font-size: 2rem;
  }

  .feature-manager-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
    padding: 0 20px;
  }

  .feature-manager-title {
    font-size: 2rem;
  }

  .feature-manager-subtitle {
    font-size: 1.125rem;
  }

  .feature-manager-icons {
    flex-direction: row;
    justify-content: center;
    gap: 20px;
  }

  .feature-manager-icon--2 {
    margin-left: 0;
  }

  .feature-ratings__container {
    padding: 0 20px;
  }

  .feature-ratings__title {
    font-size: 2rem;
  }

  .feature-ratings__subtitle {
    font-size: 1.125rem;
  }

  .feature-ratings__grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }

  .feature-ratings__card {
    padding: 20px 15px;
  }
}

@media (max-width: 480px) {
  .feature-parallax-container,
  .feature-manager-parallax-container,
  .feature-ratings-parallax-container {
    width: 100%;
  }

  .feature-parallax-section,
  .feature-manager-parallax-section,
  .feature-ratings-parallax-section {
    min-height: 400px;
    height: 70vh;
  }

  .feature-not-done__tile {
    padding: 30px 40px;
    margin: 0 16px;
  }

  .feature-not-done__text {
    font-size: 1.75rem;
  }

  .feature-manager-content {
    padding: 0 16px;
    gap: 30px;
  }

  .feature-manager-title {
    font-size: 1.75rem;
  }

  .feature-manager-subtitle {
    font-size: 1rem;
  }

  .feature-manager-cta {
    padding: 14px 28px;
    font-size: 0.9rem;
  }

  .feature-manager-icon {
    width: 50px;
    height: 50px;
  }

  .feature-manager-arrow {
    width: 20px;
    height: 20px;
  }

  .feature-ratings__container {
    padding: 0 16px;
  }

  .feature-ratings__title {
    font-size: 1.75rem;
  }

  .feature-ratings__subtitle {
    font-size: 1rem;
  }

  .feature-ratings__grid {
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }

  .feature-ratings__card {
    padding: 16px 12px;
  }

  .feature-ratings__score {
    font-size: 1.5rem;
  }

  .feature-ratings__platform {
    font-size: 1rem;
  }

  .feature-ratings__reviews {
    font-size: 0.8rem;
  }
}

/* Performance optimizations */
@media (prefers-reduced-motion: reduce) {
  .feature-parallax-background,
  .feature-manager-parallax-background,
  .feature-ratings-parallax-background {
    transform: none !important;
  }

  .feature-manager-icon {
    animation: none;
  }

  .feature-not-done__tile,
  .feature-manager-icons {
    transition: none;
  }
}
