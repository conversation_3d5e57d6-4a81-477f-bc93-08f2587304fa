/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

/* Feature Trusted By Section */
.feature-trusted-by {
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
  padding: 80px 0;
  border-bottom: 1px solid rgba(0, 106, 193, 0.1);
}

.feature-trusted-by__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.feature-trusted-by__header {
  text-align: center;
  margin-bottom: 60px;
}

.feature-trusted-by__title {
  font-size: clamp(1.75rem, 4vw, 2.5rem);
  font-weight: 500;
  color: #1a1a1a;
  margin: 0;
  line-height: 1.3;
  letter-spacing: -0.01em;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.feature-trusted-by__marquee {
  overflow: hidden;
  position: relative;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 20px 0;
  box-shadow: 0 4px 20px rgba(0, 106, 193, 0.08);
}

.feature-trusted-by__marquee-track {
  display: flex;
  gap: 60px;
  animation: marqueeScroll 40s linear infinite;
  width: max-content;
}

.feature-trusted-by__company {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  border: 1px solid rgba(0, 106, 193, 0.1);
  min-width: 120px;
  transition: all 0.3s ease;
}

.feature-trusted-by__company:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 106, 193, 0.15);
}

.feature-trusted-by__company-logo {
  width: 96px;
  height: 96px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 12px;
  border: 1px solid rgba(0, 106, 193, 0.1);
  flex-shrink: 0;
  overflow: hidden;
}

.feature-trusted-by__company-logo-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  padding: 4px;
}

.feature-trusted-by__company-name {
  font-size: 0.95rem;
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
  font-family: "Plus Jakarta Sans", sans-serif;
}

@keyframes marqueeScroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .feature-trusted-by {
    padding: 60px 0;
  }

  .feature-trusted-by__container {
    padding: 0 16px;
  }

  .feature-trusted-by__header {
    margin-bottom: 40px;
  }

  .feature-trusted-by__marquee-track {
    gap: 40px;
    animation-duration: 30s;
  }

  .feature-trusted-by__company {
    padding: 20px;
    min-width: 100px;
  }

  .feature-trusted-by__company-logo {
    width: 80px;
    height: 80px;
  }

  .feature-trusted-by__company-name {
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .feature-trusted-by {
    padding: 50px 0;
  }

  .feature-trusted-by__marquee-track {
    gap: 30px;
    animation-duration: 25s;
  }

  .feature-trusted-by__company {
    padding: 16px;
    min-width: 80px;
  }

  .feature-trusted-by__company-logo {
    width: 72px;
    height: 72px;
  }

  .feature-trusted-by__company-name {
    font-size: 0.8rem;
  }
}
