import { useEffect, useState } from 'react';
import { getImage } from '../utils/images';
import './Testimonials.css';

const Testimonials = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Get images from centralized system
  const testimonialsImage = getImage('testimonials', 'mainImage');
  const clientPhotos = {
    sarah<PERSON>ohnson: getImage('testimonials', 'clientPhotos', 'sarah<PERSON>ohnson'),
    michael<PERSON><PERSON>: getImage('testimonials', 'clientPhotos', 'michael<PERSON>hen'),
    lisaRodriguez: getImage('testimonials', 'clientPhotos', 'lisaRodriguez'),
    davidThompson: getImage('testimonials', 'clientPhotos', 'davidThompson')
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    const section = document.querySelector('.testimonials');
    if (section) {
      observer.observe(section);
    }

    return () => {
      if (section) {
        observer.unobserve(section);
      }
    };
  }, []);

  // Auto-rotate testimonials
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isTransitioning) {
        changeTestimonial((prev) => (prev + 1) % testimonials.length);
      }
    }, 5000);

    return () => clearInterval(interval);
  }, [isTransitioning]);

  const changeTestimonial = (newIndex) => {
    if (isTransitioning) return;

    setIsTransitioning(true);

    // Simple fade transition
    const carousel = document.querySelector('.testimonials__carousel-container');
    if (carousel) {
      carousel.style.opacity = '0.5';
    }

    setTimeout(() => {
      setCurrentTestimonial(newIndex);

      if (carousel) {
        carousel.style.opacity = '1';
      }

      setIsTransitioning(false);
    }, 150);
  };

  const testimonials = [
    {
      id: 1,
      quote: "Telescope transformed our entire operation. We went from 3-day onboarding to just 24 hours, and our caregivers love how easy everything is now.",
      clientName: "Sarah Johnson",
      clientTitle: "Operations Director",
      companyName: "CareFirst Home Health",
      imageKey: "sarahJohnson",
      companyLogo: "🏥"
    },
    {
      id: 2,
      quote: "The scheduling feature alone saved us 15 hours per week. Our clients constantly compliment how organized and professional we've become.",
      clientName: "Michael Chen",
      clientTitle: "Agency Owner",
      companyName: "Compassionate Care Solutions",
      imageKey: "michaelChen",
      companyLogo: "❤️"
    },
    {
      id: 3,
      quote: "Our caregiver retention improved by 40% after implementing Telescope. The mobile app makes their job so much easier and more efficient.",
      clientName: "Lisa Rodriguez",
      clientTitle: "HR Manager",
      companyName: "Guardian Angels Home Care",
      imageKey: "lisaRodriguez",
      companyLogo: "👼"
    },
    {
      id: 4,
      quote: "The compliance features give us complete peace of mind. We've passed every state inspection with flying colors since using Telescope.",
      clientName: "David Thompson",
      clientTitle: "Compliance Officer",
      companyName: "Premier Home Health Services",
      imageKey: "davidThompson",
      companyLogo: "⭐"
    }
  ];

  const nextTestimonial = () => {
    changeTestimonial((currentTestimonial + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    changeTestimonial((currentTestimonial - 1 + testimonials.length) % testimonials.length);
  };

  return (
    <section className={`testimonials ${isVisible ? 'testimonials--visible' : ''}`}>
      <div className="testimonials__container">
        <div className="testimonials__content">
          {/* Left Side: Header Section */}
          <div className="testimonials__header">
            <div className="testimonials__header-content">
              <h2 className="testimonials__title">Tales of success</h2>
              <p className="testimonials__subtitle">What our clients have to say</p>
            </div>
            <div className="testimonials__header-image">
              <img
                src={testimonialsImage.placeholder}
                alt={testimonialsImage.alt}
                className="testimonials__main-image"
                onLoad={(e) => {
                  // Replace with actual image when available
                  if (testimonialsImage.src !== testimonialsImage.placeholder) {
                    e.target.src = testimonialsImage.src;
                  }
                }}
                onError={(e) => {
                  // Fallback to placeholder if actual image fails
                  e.target.src = testimonialsImage.placeholder;
                }}
              />
            </div>
          </div>

          {/* Right Side: Testimonial Carousel */}
          <div className="testimonials__carousel">
            <div className="testimonials__carousel-container">
              <div className="testimonials__quote-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M14,17H17L19,13V7H13V13H16M6,17H9L11,13V7H5V13H8L6,17Z"/>
                </svg>
              </div>

              <div className="testimonials__review">
                <p className="testimonials__quote">
                  "{testimonials[currentTestimonial].quote}"
                </p>
              </div>

              <div className="testimonials__client">
                <div className="testimonials__client-info">
                  <div className="testimonials__client-image">
                    <img
                      src={clientPhotos[testimonials[currentTestimonial].imageKey].placeholder}
                      alt={clientPhotos[testimonials[currentTestimonial].imageKey].alt}
                      className="testimonials__client-avatar"
                      // onLoad={(e) => {
                      //   // Replace with actual image when available
                      //   const actualImage = clientPhotos[testimonials[currentTestimonial].imageKey];
                      //   if (actualImage.src !== actualImage.placeholder) {
                      //     e.target.src = actualImage.src;
                      //   }
                      // }}
                      // onError={(e) => {
                      //   // Fallback to placeholder if actual image fails
                      //   e.target.src = clientPhotos[testimonials[currentTestimonial].imageKey].placeholder;
                      // }}
                    />
                  </div>
                  <div className="testimonials__client-details">
                    <h4 className="testimonials__client-name">
                      {testimonials[currentTestimonial].clientName}
                    </h4>
                    <p className="testimonials__client-title">
                      {testimonials[currentTestimonial].clientTitle}
                    </p>
                    <p className="testimonials__company-name">
                      {testimonials[currentTestimonial].companyName}
                    </p>
                  </div>
                </div>
                <div className="testimonials__company-logo">
                  {testimonials[currentTestimonial].companyLogo}
                </div>
              </div>

              {/* Navigation */}
              <div className="testimonials__navigation">
                <button
                  className="testimonials__nav-btn testimonials__nav-btn--prev"
                  onClick={prevTestimonial}
                >
                  <svg viewBox="0 0 24 24" fill="none">
                    <path d="M15 18l-6-6 6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>

                <div className="testimonials__dots">
                  {testimonials.map((_, index) => (
                    <button
                      key={index}
                      className={`testimonials__dot ${index === currentTestimonial ? 'testimonials__dot--active' : ''}`}
                      onClick={() => changeTestimonial(index)}
                    />
                  ))}
                </div>

                <button
                  className="testimonials__nav-btn testimonials__nav-btn--next"
                  onClick={nextTestimonial}
                >
                  <svg viewBox="0 0 24 24" fill="none">
                    <path d="M9 18l6-6-6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
