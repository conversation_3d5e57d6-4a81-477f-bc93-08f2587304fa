import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import FeatureNavigation from './FeatureNavigation';
import FeatureHero from './FeatureHero';
import FeatureTrustedBy from './FeatureTrustedBy';
import FeatureZigzag from './FeatureZigzag';
import FeatureTestimonial from './FeatureTestimonial';
import FeatureParallax from './FeatureParallax';
import FeatureHowItWorks from './FeatureHowItWorks';
import FeatureEverythingWorks from './FeatureEverythingWorks';
import FeatureFAQ from './FeatureFAQ';
import FeatureFinalCTA from './FeatureFinalCTA';
import './Features.css';

// Features data - moved outside component for better performance
const features = [
    {
      id: 'evv-gps',
      title: 'EVV & GPS Verification',
      category: 'Compliance',
      shortDescription: 'Real-time EVV with GPS that keeps you compliant',
      heroTitle: 'Real-Time EVV with GPS That Keeps You Compliant',
      heroSubtitle: 'Accurate, automated clock-ins with built-in GPS — meet state requirements and protect your business from costly errors.',
      benefits: [
        'GPS-Verified Clock-Ins – Caregivers can only clock in and out within a geofenced radius of the client\'s home',
        'Real-Time Tracking – Monitor visit status live — no more guessing if care actually happened',
        '100% EVV-Compliant – Designed to meet federal and state EVV requirements, including Medicaid mandates',
        'Audit-Ready Records – Every visit is logged with time, date, location, and caregiver credentials — all exportable',
        'Missed Visit Alerts – Get notified instantly if a visit is late, skipped, or clocked out early'
      ],
      testimonial: {
        quote: "With Telescope's EVV system, we passed our audit without a single issue. It's accurate and saves us hours every week.",
        author: "Maria J.",
        title: "Agency Administrator",
        location: "Austin, TX"
      },
      howItWorks: [
        'Caregiver clocks in via the mobile app — GPS is automatically validated',
        'Telescope logs the visit in real time — visible on the admin dashboard',
        'Visit record is locked and archived — ready for billing, payroll, or state audits'
      ],
      ctaText: 'Book a Demo',
      mobileImageKey: 'evvGps',
      webImageKey: 'evvGps'
    },
    {
      id: 'caregiver-app',
      title: 'Caregiver Mobile App',
      category: 'Mobile',
      shortDescription: 'Complete mobile solution for caregivers',
      heroTitle: 'Empower Your Caregivers',
      heroSubtitle: 'Give your caregivers everything they need in their pocket. Clock in/out, document care, and communicate seamlessly from anywhere.',
      benefits: [
        'One-tap clock in/out with GPS verification',
        'Task management and care documentation',
        'Instant messaging with office and families',
        'Photo documentation for care activities'
      ],
      mobileImageKey: 'caregiverApp',
      webImageKey: 'caregiverApp'
    },
    {
      id: 'smart-scheduling',
      title: 'Smart Scheduling',
      category: 'Operations',
      shortDescription: 'AI-powered scheduling optimization',
      heroTitle: 'Schedule Smarter, Not Harder',
      heroSubtitle: 'Reduce scheduling conflicts by 90% with AI-powered assignment matching. Fill gaps faster and optimize caregiver routes automatically.',
      benefits: [
        'AI-powered caregiver-client matching',
        'Automatic route optimization',
        'Real-time schedule adjustments',
        'Conflict detection and resolution'
      ],
      mobileImageKey: 'scheduling',
      webImageKey: 'scheduling'
    },
    {
      id: 'task-tracking',
      title: 'Task & ADL Tracking',
      category: 'Documentation',
      shortDescription: 'Comprehensive care documentation',
      heroTitle: 'Document Every Detail',
      heroSubtitle: 'Track activities of daily living and care tasks with precision. Generate detailed reports for families and healthcare providers.',
      benefits: [
        'Comprehensive ADL tracking and documentation',
        'Customizable care plans and task lists',
        'Real-time progress tracking and updates',
        'Automated family and coordinator reports'
      ],
      mobileImageKey: 'taskTracking',
      webImageKey: 'taskTracking'
    },
    {
      id: 'esignature',
      title: 'Client eSignature',
      category: 'Digital',
      shortDescription: 'Secure digital signature collection',
      heroTitle: 'Go Paperless with Confidence',
      heroSubtitle: 'Collect secure digital signatures from clients and families. Streamline consent forms, care plans, and documentation.',
      benefits: [
        'Secure digital signature collection',
        'Legal compliance and audit trails',
        'Streamlined consent and documentation',
        'Instant form completion and storage'
      ],
      mobileImageKey: 'eSignature',
      webImageKey: 'eSignature'
    },
    {
      id: 'custom-forms',
      title: 'Custom Forms',
      category: 'Documentation',
      shortDescription: 'Build forms tailored to your needs',
      heroTitle: 'Forms That Work For You',
      heroSubtitle: 'Create custom forms for assessments, intake, and documentation. No more one-size-fits-all solutions.',
      benefits: [
        'Drag-and-drop form builder',
        'Conditional logic and branching',
        'Mobile-optimized form completion',
        'Automated data collection and storage'
      ],
      mobileImageKey: 'customForms',
      webImageKey: 'customForms'
    },
    {
      id: 'messaging',
      title: 'Text Messaging',
      category: 'Communication',
      shortDescription: 'HIPAA-compliant messaging platform',
      heroTitle: 'Stay Connected Securely',
      heroSubtitle: 'Communicate with caregivers, clients, and families through our HIPAA-compliant messaging platform.',
      benefits: [
        'HIPAA-compliant messaging',
        'Group and individual conversations',
        'File and photo sharing',
        'Message scheduling and templates'
      ],
      mobileImageKey: 'textMessaging',
      webImageKey: 'textMessaging'
    },
    {
      id: 'surveys',
      title: 'Client Surveys',
      category: 'Feedback',
      shortDescription: 'Automated satisfaction surveys',
      heroTitle: 'Measure What Matters',
      heroSubtitle: 'Automatically collect feedback from clients and families. Track satisfaction scores and improve your services.',
      benefits: [
        'Automated survey distribution',
        'Customizable survey templates',
        'Real-time satisfaction tracking',
        'Actionable insights and reporting'
      ],
      mobileImageKey: 'surveys',
      webImageKey: 'surveys'
    },
    {
      id: 'portals',
      title: 'Client Portals',
      category: 'Access',
      shortDescription: 'Secure client and family access',
      heroTitle: 'Transparency Builds Trust',
      heroSubtitle: 'Give clients and families secure access to care plans, schedules, and updates through dedicated portals.',
      benefits: [
        'Secure client and family portals',
        'Real-time care plan access',
        'Schedule and update notifications',
        'Document sharing and storage'
      ],
      mobileImageKey: 'portals',
      webImageKey: 'portals'
    },
    {
      id: 'hiring',
      title: 'Hiring & Onboarding',
      category: 'HR',
      shortDescription: 'Streamlined recruitment process',
      heroTitle: 'Hire Better, Faster',
      heroSubtitle: 'Streamline your hiring process from application to onboarding. Find the right caregivers and get them started quickly.',
      benefits: [
        'Online application and screening',
        'Automated background checks',
        'Digital onboarding workflows',
        'Compliance tracking and documentation'
      ],
      mobileImageKey: 'hiring',
      webImageKey: 'hiring'
    },
    {
      id: 'training',
      title: 'Caregiver Training',
      category: 'Education',
      shortDescription: 'Comprehensive training modules',
      heroTitle: 'Invest in Excellence',
      heroSubtitle: 'Provide ongoing training and certification for your caregivers. Track progress and ensure quality care delivery.',
      benefits: [
        'Interactive training modules',
        'Certification tracking',
        'Progress monitoring and reporting',
        'Continuing education management'
      ],
      mobileImageKey: 'training',
      webImageKey: 'training'
    },
    {
      id: 'background-screening',
      title: 'Background Screening',
      category: 'Security',
      shortDescription: 'Comprehensive background verification',
      heroTitle: 'Safety First, Always',
      heroSubtitle: 'Ensure the safety of your clients with comprehensive background screening and ongoing monitoring.',
      benefits: [
        'Comprehensive background checks',
        'Ongoing monitoring and alerts',
        'Compliance with state requirements',
        'Secure document storage'
      ],
      mobileImageKey: 'backgroundScreening',
      webImageKey: 'backgroundScreening'
    },
    {
      id: 'invoicing',
      title: 'Invoicing & Billing',
      category: 'Finance',
      shortDescription: 'Automated billing and payments',
      heroTitle: 'Get Paid Faster',
      heroSubtitle: 'Automate your billing process and get paid faster. Track payments, manage insurance claims, and reduce administrative overhead.',
      benefits: [
        'Automated invoice generation',
        'Insurance claim management',
        'Payment tracking and reminders',
        'Financial reporting and analytics'
      ],
      mobileImageKey: 'invoicing',
      webImageKey: 'invoicing'
    }
  ];

const Features = () => {
  const [activeTab, setActiveTab] = useState(0);
  const { featureId } = useParams();
  const navigate = useNavigate();

  // Handle tab change and update URL
  const handleTabChange = (index) => {
    setActiveTab(index);
    const feature = features[index];
    navigate(`/features/${feature.id}`, { replace: true });
  };

  useEffect(() => {
    // Handle direct navigation to specific feature
    if (featureId) {
      const featureIndex = features.findIndex(feature => feature.id === featureId);
      if (featureIndex !== -1) {
        setActiveTab(featureIndex);
      }
    } else {
      // If no specific feature, navigate to first feature
      if (features.length > 0) {
        navigate(`/features/${features[0].id}`, { replace: true });
      }
    }
  }, [featureId, navigate]);

  // EVV-specific data for modular components
  const evvProblemsData = [
    {
      problem: "Tired of failed audits and compliance headaches?",
      solution: "Never worry about EVV compliance again",
      checks: [
        "Automatic GPS verification for every visit",
        "Real-time tracking prevents missed visits",
        "Audit-ready records generated automatically"
      ],
      imageKey: "complianceViolations",
      cta: "See How It Works"
    },
    {
      problem: "Struggling with caregivers clocking in from wrong locations?",
      solution: "Ensure caregivers are exactly where they should be",
      checks: [
        "Geofenced clock-ins prevent location fraud",
        "GPS validation happens automatically",
        "Instant alerts for any location discrepancies"
      ],
      imageKey: "timeTracking",
      cta: "Try GPS Verification"
    },
    {
      problem: "Spending hours manually tracking visit records?",
      solution: "Automate your entire visit documentation process",
      checks: [
        "Every visit logged automatically with timestamps",
        "Complete audit trail for state requirements",
        "Export reports in seconds, not hours"
      ],
      imageKey: "manualPaperwork",
      cta: "Book a Demo"
    }
  ];

  // Caregiver App-specific data for modular components
  const caregiverAppProblemsData = [
    {
      problem: "Caregivers struggling with paper forms and missed documentation?",
      solution: "Give them digital tools that work anywhere",
      checks: [
        "Mobile-friendly design for iOS and Android devices",
        "Real-time documentation prevents missed notes",
        "All forms and checklists accessible in one app"
      ],
      imageKey: "paperForms",
      cta: "See the App"
    },
    {
      problem: "Lost communication between caregivers and office staff?",
      solution: "Keep everyone connected with instant messaging",
      checks: [
        "Real-time alerts and shift updates",
        "Direct messaging with office and families",
        "Instant notifications for schedule changes"
      ],
      imageKey: "communication",
      cta: "Try Messaging"
    },
    {
      problem: "Caregivers confused about client care plans and schedules?",
      solution: "Put complete client information at their fingertips",
      checks: [
        "Full schedules and client profiles in one view",
        "Step-by-step ADL checklists for each visit",
        "Care plan updates sync automatically"
      ],
      imageKey: "scheduling",
      cta: "Book a Demo"
    }
  ];

  const evvWhyUseData = [
    {
      title: "Stop losing money on compliance violations",
      subtitle: "Protect your agency from costly penalties",
      benefits: [
        "Automatic EVV compliance reduces audit risk by 95%",
        "Real-time GPS verification prevents location fraud",
        "Instant alerts for missed or late visits",
        "Complete audit trail for state inspections"
      ],
      imageKey: "protectAgency",
      cta: "Protect Your Agency"
    },
    {
      title: "Eliminate manual paperwork and save hours daily",
      subtitle: "Automate your entire visit documentation process",
      benefits: [
        "Digital visit records eliminate paper trails",
        "Automated time tracking saves 10+ hours per week",
        "Instant report generation for billing and payroll",
        "Cloud storage ensures records never get lost"
      ],
      imageKey: "saveTime",
      cta: "Start Saving Time"
    }
  ];

  // Caregiver App "Why Use" data
  const caregiverAppWhyUseData = [
    {
      title: "Empower your caregivers with mobile-first tools",
      subtitle: "Boost productivity and job satisfaction in the field",
      benefits: [
        "Mobile-friendly design works on any iOS or Android device",
        "One-tap clock-in/out with GPS verification saves time",
        "Real-time documentation prevents missed care notes",
        "Instant messaging keeps caregivers connected and supported"
      ],
      imageKey: "empowerCaregivers",
      cta: "Empower Your Team"
    },
    {
      title: "Ensure complete, compliant documentation every time",
      subtitle: "Never worry about missing care records again",
      benefits: [
        "Step-by-step ADL checklists ensure nothing is missed",
        "Digital forms eliminate lost paperwork",
        "Real-time sync means records are always up-to-date",
        "Automated compliance tracking reduces audit risk"
      ],
      imageKey: "compliantDocs",
      cta: "Improve Compliance"
    }
  ];

  // Caregiver Training-specific data for modular components
  const caregiverTrainingProblemsData = [
    {
      problem: "Struggling to keep track of caregiver certifications and compliance?",
      solution: "Automate training assignments and track progress in real-time",
      checks: [
        "150+ state-compliant classes in one platform",
        "Automated training assignments by role and date",
        "Real-time progress tracking and instant certificates"
      ],
      imageKey: "trainingCompliance",
      cta: "See Training Library"
    },
    {
      problem: "Caregivers missing training deadlines and falling out of compliance?",
      solution: "Never miss a deadline with automated reminders",
      checks: [
        "Text and dashboard reminders keep everyone on track",
        "Automated notifications for upcoming deadlines",
        "Admin alerts for overdue training requirements"
      ],
      imageKey: "trainingReminders",
      cta: "Try Reminders"
    },
    {
      problem: "Spending hours creating custom training content and tracking completion?",
      solution: "Upload your own content and let the system handle the rest",
      checks: [
        "Upload custom orientation videos and policies",
        "Role and client-specific training assignments",
        "Instant progress reports and audit-ready certificates"
      ],
      imageKey: "customTraining",
      cta: "Book a Demo"
    }
  ];

  // Caregiver Training "Why Use" data
  const caregiverTrainingWhyUseData = [
    {
      title: "Save 95% of admin time on training management",
      subtitle: "Let automation handle the heavy lifting",
      benefits: [
        "Automated training assignments eliminate manual scheduling",
        "Real-time progress tracking replaces spreadsheet chaos",
        "Instant certificate generation saves hours of paperwork",
        "Export audit-ready reports in seconds, not days"
      ],
      imageKey: "saveAdminTime",
      cta: "Save Time Now"
    },
    {
      title: "Ensure every caregiver is properly trained and compliant",
      subtitle: "Never worry about certification gaps again",
      benefits: [
        "150+ state-compliant courses cover all requirements",
        "Role-specific training ensures relevant skill development",
        "Automated reminders prevent compliance lapses",
        "Mobile access means training happens anywhere, anytime"
      ],
      imageKey: "ensureCompliance",
      cta: "Ensure Compliance"
    }
  ];

  // eSignature-specific data for modular components
  const eSignatureProblemsData = [
    {
      problem: "Drowning in paperwork and chasing down client signatures?",
      solution: "Capture digital signatures instantly at the point of care",
      checks: [
        "Clients sign directly on caregiver's device",
        "No more printing, scanning, or lost paperwork",
        "Signatures automatically attached to care records"
      ],
      imageKey: "paperworkChaos",
      cta: "Go Digital"
    },
    {
      problem: "Worried about signature authenticity and legal compliance?",
      solution: "Get legally binding, audit-ready signatures every time",
      checks: [
        "Fully compliant with electronic signature regulations",
        "HIPAA-compliant encryption and storage",
        "Timestamped with date, time, and GPS location"
      ],
      imageKey: "legalCompliance",
      cta: "Ensure Compliance"
    },
    {
      problem: "Spending hours organizing and filing signed documents?",
      solution: "Automatic organization and instant access to all signatures",
      checks: [
        "Signed documents stored in client files automatically",
        "Export signatures for audits or billing in seconds",
        "Secure audit trail for every signature captured"
      ],
      imageKey: "documentOrganization",
      cta: "Book a Demo"
    }
  ];

  // eSignature "Why Use" data
  const eSignatureWhyUseData = [
    {
      title: "Eliminate hours of admin work every week",
      subtitle: "Stop chasing paperwork and focus on care",
      benefits: [
        "Digital signatures eliminate printing and scanning",
        "Automatic document organization saves filing time",
        "Instant access to signed records from anywhere",
        "No more lost or incomplete paperwork"
      ],
      imageKey: "eliminateAdminWork",
      cta: "Save Time Now"
    },
    {
      title: "Ensure every signature is secure and audit-ready",
      subtitle: "Never worry about compliance or authenticity again",
      benefits: [
        "Legally binding signatures meet all regulatory standards",
        "Encrypted storage protects client privacy",
        "Timestamped records provide complete audit trail",
        "Export capabilities for Medicaid billing and audits"
      ],
      imageKey: "secureAuditReady",
      cta: "Ensure Security"
    }
  ];

  // Client Portal-specific data for modular components
  const clientPortalProblemsData = [
    {
      problem: "Families constantly calling for updates on their loved one's care?",
      solution: "Give families 24/7 access to real-time care information",
      checks: [
        "Families view care updates, schedules, and notes anytime",
        "Real-time visit confirmations and schedule changes",
        "Secure access to completed ADLs and caregiver observations"
      ],
      imageKey: "familyCalls",
      cta: "Reduce Calls"
    },
    {
      problem: "Struggling to build trust and transparency with families?",
      solution: "Create transparency that builds lasting relationships",
      checks: [
        "Families see exactly what care was provided and when",
        "Share caregiver notes and medication reminders",
        "Build trust through complete visibility into care delivery"
      ],
      imageKey: "buildTrust",
      cta: "Build Trust"
    },
    {
      problem: "Worried about sharing sensitive information securely?",
      solution: "HIPAA-compliant portals with role-based access control",
      checks: [
        "Secure login with role-based permissions",
        "HIPAA-compliant information sharing",
        "Control exactly what each family member can see"
      ],
      imageKey: "secureSharing",
      cta: "Book a Demo"
    }
  ];

  // Client Portal "Why Use" data
  const clientPortalWhyUseData = [
    {
      title: "Reduce family calls and improve satisfaction",
      subtitle: "Turn anxious families into informed advocates",
      benefits: [
        "24/7 portal access reduces 'check-in' phone calls",
        "Real-time updates keep families informed and calm",
        "Transparent communication builds trust and loyalty",
        "Higher client satisfaction leads to better retention"
      ],
      imageKey: "reduceCalls",
      cta: "Improve Satisfaction"
    },
    {
      title: "Strengthen family relationships and referrals",
      subtitle: "Happy families become your best marketing tool",
      benefits: [
        "Informed families feel more confident in your care",
        "Transparency builds long-term trust and relationships",
        "Satisfied families refer friends and neighbors",
        "Portal access differentiates you from competitors"
      ],
      imageKey: "strengthenRelationships",
      cta: "Build Relationships"
    }
  ];

  // Custom Forms-specific data for modular components
  const customFormsProblemsData = [
    {
      problem: "Stuck with generic forms that don't fit your agency's needs?",
      solution: "Build custom forms tailored to your exact requirements",
      checks: [
        "Drag-and-drop form builder with custom fields and logic",
        "Create intake forms, incident reports, and specialized assessments",
        "No coding required - build professional forms in minutes"
      ],
      imageKey: "genericForms",
      cta: "Build Custom Forms"
    },
    {
      problem: "Caregivers struggling with paper forms in the field?",
      solution: "Mobile-ready forms that work anywhere, anytime",
      checks: [
        "All forms accessible via caregiver mobile app",
        "Fill out forms offline and sync when connected",
        "Attach forms to specific visits or client files automatically"
      ],
      imageKey: "paperForms",
      cta: "Go Mobile"
    },
    {
      problem: "Losing time organizing and accessing completed forms?",
      solution: "Automatic organization and instant access to all forms",
      checks: [
        "Completed forms automatically saved and organized",
        "Export forms for audits, compliance, or family sharing",
        "Search and filter forms by client, date, or form type"
      ],
      imageKey: "formOrganization",
      cta: "Book a Demo"
    }
  ];

  // Custom Forms "Why Use" data
  const customFormsWhyUseData = [
    {
      title: "Create forms that actually work for your agency",
      subtitle: "Stop forcing your processes into generic templates",
      benefits: [
        "Build forms with exactly the fields and logic you need",
        "Use professional templates or start from scratch",
        "Conditional logic shows/hides fields based on responses",
        "Custom validation ensures data quality and completeness"
      ],
      imageKey: "perfectForms",
      cta: "Build Perfect Forms"
    },
    {
      title: "Streamline data collection and eliminate paperwork",
      subtitle: "From creation to completion in one seamless workflow",
      benefits: [
        "Mobile-first design works on any device",
        "Automatic form routing and assignment",
        "Real-time data sync eliminates double entry",
        "Export capabilities for reporting and compliance"
      ],
      imageKey: "streamlineData",
      cta: "Streamline Now"
    }
  ];

  // Hiring & Onboarding-specific data for modular components
  const hiringProblemsData = [
    {
      problem: "Losing great caregivers to slow hiring processes?",
      solution: "Engage candidates instantly and speed up your pipeline",
      checks: [
        "One-click job posting to multiple boards simultaneously",
        "Automated text engagement connects with applicants immediately",
        "Self-scheduled interviews eliminate back-and-forth coordination"
      ],
      imageKey: "slowHiring",
      cta: "Speed Up Hiring"
    },
    {
      problem: "Drowning in onboarding paperwork and compliance tracking?",
      solution: "Automate document collection and ensure 100% compliance",
      checks: [
        "E-signature for all onboarding documents and forms",
        "Centralized tracking ensures nothing falls through cracks",
        "Compliance-ready documentation before caregivers start"
      ],
      imageKey: "onboardingPaperwork",
      cta: "Automate Onboarding"
    },
    {
      problem: "Can't track where candidates are in your hiring funnel?",
      solution: "Complete visibility into every step of your hiring process",
      checks: [
        "Centralized applicant tracking with real-time status updates",
        "Automated workflow moves candidates through each stage",
        "Dashboard view shows bottlenecks and completion rates"
      ],
      imageKey: "hiringTracking",
      cta: "Book a Demo"
    }
  ];

  // Hiring & Onboarding "Why Use" data
  const hiringWhyUseData = [
    {
      title: "Hire faster without sacrificing quality",
      subtitle: "Speed up every step while maintaining high standards",
      benefits: [
        "Instant candidate engagement reduces drop-off rates",
        "Automated scheduling eliminates coordination delays",
        "Streamlined process attracts top-quality caregivers",
        "Digital workflows save 15+ hours per hire"
      ],
      imageKey: "hireFaster",
      cta: "Hire Faster"
    },
    {
      title: "Ensure every hire is compliant and audit-ready",
      subtitle: "Never worry about missing documents or signatures again",
      benefits: [
        "Digital document collection ensures 100% completion",
        "E-signatures provide legally binding, timestamped records",
        "Automated compliance tracking prevents oversights",
        "Audit-ready files from day one of employment"
      ],
      imageKey: "ensureCompliance",
      cta: "Ensure Compliance"
    }
  ];

  // Invoicing-specific data for modular components
  const invoicingProblemsData = [
    {
      problem: "Spending days manually creating invoices and chasing payments?",
      solution: "Automate invoice generation and get paid faster",
      checks: [
        "Auto-generated invoices based on verified visits and hours",
        "Custom billing rules for different rates and service types",
        "Real-time tracking of billed, paid, and outstanding amounts"
      ],
      imageKey: "manualInvoicing",
      cta: "Automate Billing"
    },
    {
      problem: "Making billing errors that delay payments and frustrate clients?",
      solution: "Eliminate errors with integrated scheduling and time tracking",
      checks: [
        "Invoices automatically sync with approved visits and hours",
        "Connected system prevents discrepancies and double-billing",
        "Accurate billing builds trust and improves cash flow"
      ],
      imageKey: "billingErrors",
      cta: "Eliminate Errors"
    },
    {
      problem: "Struggling to export invoices to your accounting software?",
      solution: "Seamless integration with QuickBooks and other systems",
      checks: [
        "Export invoices as PDFs or directly to accounting software",
        "Compatible with QuickBooks, Xero, and other popular platforms",
        "Streamlined workflow from billing to bookkeeping"
      ],
      imageKey: "accountingIntegration",
      cta: "Book a Demo"
    }
  ];

  // Invoicing "Why Use" data
  const invoicingWhyUseData = [
    {
      title: "Save hours every week on billing and get paid faster",
      subtitle: "Automated invoicing means more time for growing your business",
      benefits: [
        "Auto-generated invoices eliminate manual data entry",
        "Faster billing cycles improve cash flow significantly",
        "Real-time dashboards show exactly what's owed and paid",
        "Professional invoices enhance your agency's reputation"
      ],
      imageKey: "saveTimeGetPaid",
      cta: "Get Paid Faster"
    },
    {
      title: "Eliminate billing errors and improve client relationships",
      subtitle: "Accurate billing builds trust and reduces disputes",
      benefits: [
        "Connected system ensures invoices match actual services",
        "Transparent billing details build client confidence",
        "Fewer billing disputes mean better client relationships",
        "Audit-ready records for insurance and compliance reviews"
      ],
      imageKey: "accurateBilling",
      cta: "Improve Accuracy"
    }
  ];

  // Client Surveys-specific data for modular components
  const surveysProblemsData = [
    {
      problem: "Flying blind without knowing what clients really think?",
      solution: "Get real-time feedback automatically after every visit",
      checks: [
        "Automated surveys sent via text and email after key visits",
        "HIPAA-compliant data collection protects privacy",
        "Monthly reports show satisfaction trends and insights"
      ],
      imageKey: "blindFeedback",
      cta: "Get Real Feedback"
    },
    {
      problem: "Finding out about problems only after clients leave?",
      solution: "Spot issues early before they become complaints",
      checks: [
        "Real-time alerts for low satisfaction scores",
        "Identify concerns before they turn into turnover",
        "Proactive problem-solving improves retention rates"
      ],
      imageKey: "lateProblems",
      cta: "Catch Issues Early"
    },
    {
      problem: "Struggling to show families and staff that you care about their input?",
      solution: "Demonstrate that every voice matters with systematic feedback",
      checks: [
        "Regular surveys show commitment to continuous improvement",
        "Transparent feedback process builds trust and relationships",
        "Action-oriented approach turns feedback into real changes"
      ],
      imageKey: "showCaring",
      cta: "Book a Demo"
    }
  ];

  // Client Surveys "Why Use" data
  const surveysWhyUseData = [
    {
      title: "Turn feedback into your competitive advantage",
      subtitle: "Use insights to continuously improve and outperform competitors",
      benefits: [
        "Monthly reports reveal exactly what's working and what's not",
        "Data-driven improvements increase client satisfaction scores",
        "Proactive issue resolution prevents client churn",
        "Satisfied clients become your best referral sources"
      ],
      imageKey: "competitiveAdvantage",
      cta: "Gain Advantage"
    },
    {
      title: "Build stronger relationships through listening",
      subtitle: "Show clients and caregivers their opinions drive real change",
      benefits: [
        "Automated surveys demonstrate ongoing commitment to quality",
        "Regular feedback collection builds trust and transparency",
        "Acting on feedback shows families and staff they matter",
        "Improved communication leads to better retention rates"
      ],
      imageKey: "strongerRelationships",
      cta: "Build Relationships"
    }
  ];

  // Smart Scheduling-specific data for modular components
  const schedulingProblemsData = [
    {
      problem: "Spending hours every week creating schedules manually?",
      solution: "AI-powered scheduling that optimizes routes and matches automatically",
      checks: [
        "AI matches caregivers to clients based on skills and preferences",
        "Automatic route optimization reduces travel time by 30%",
        "Real-time schedule adjustments handle last-minute changes"
      ],
      imageKey: "manualScheduling",
      cta: "Automate Scheduling"
    },
    {
      problem: "Dealing with constant scheduling conflicts and no-shows?",
      solution: "Smart conflict detection and automated backup solutions",
      checks: [
        "Conflict detection prevents double-booking and overlaps",
        "Automated backup caregiver assignments for no-shows",
        "Real-time notifications keep everyone informed of changes"
      ],
      imageKey: "schedulingConflicts",
      cta: "Eliminate Conflicts"
    },
    {
      problem: "Caregivers driving inefficient routes and wasting time?",
      solution: "Intelligent route planning saves time and reduces costs",
      checks: [
        "GPS-optimized routes minimize drive time between visits",
        "Geographic clustering groups nearby clients together",
        "Real-time traffic updates adjust routes automatically"
      ],
      imageKey: "inefficientRoutes",
      cta: "Book a Demo"
    }
  ];

  // Smart Scheduling "Why Use" data
  const schedulingWhyUseData = [
    {
      title: "Save 10+ hours per week on scheduling tasks",
      subtitle: "Let AI handle the complexity while you focus on care",
      benefits: [
        "AI-powered matching eliminates manual caregiver assignments",
        "Automatic route optimization saves hours of planning time",
        "Real-time adjustments handle changes without admin intervention",
        "Reduced scheduling errors improve operational efficiency"
      ],
      imageKey: "saveSchedulingTime",
      cta: "Save Time Now"
    },
    {
      title: "Reduce scheduling conflicts by 90% and improve satisfaction",
      subtitle: "Happy caregivers and clients through smarter scheduling",
      benefits: [
        "Better caregiver-client matching improves care quality",
        "Optimized routes reduce caregiver stress and burnout",
        "Fewer conflicts mean more reliable care for families",
        "Improved efficiency leads to better profit margins"
      ],
      imageKey: "improveScheduling",
      cta: "Improve Efficiency"
    }
  ];

  // Task & ADL Tracking-specific data for modular components
  const taskTrackingProblemsData = [
    {
      problem: "Chasing down handwritten notes and missing documentation?",
      solution: "Real-time digital documentation that never gets lost",
      checks: [
        "Caregivers document tasks and ADLs during the visit in real-time",
        "All notes are automatically saved and synced to admin dashboard",
        "No more lost paperwork or illegible handwriting"
      ],
      imageKey: "handwrittenNotes",
      cta: "Go Digital"
    },
    {
      problem: "Struggling to ensure caregivers complete all required tasks?",
      solution: "Preloaded checklists ensure nothing is missed",
      checks: [
        "Customized ADL checklists assigned to each client's care plan",
        "Step-by-step task guidance ensures consistent care delivery",
        "Progress tracking shows completion rates and trends over time"
      ],
      imageKey: "missedTasks",
      cta: "Ensure Completion"
    },
    {
      problem: "Worried about audit readiness and compliance documentation?",
      solution: "HIPAA-secure records that are always audit-ready",
      checks: [
        "All documentation encrypted and stored securely",
        "Exportable visit records for audits or family sharing",
        "Complete audit trail with timestamps and caregiver signatures"
      ],
      imageKey: "auditReadiness",
      cta: "Book a Demo"
    }
  ];

  // Task & ADL Tracking "Why Use" data
  const taskTrackingWhyUseData = [
    {
      title: "Ensure complete, accurate documentation every time",
      subtitle: "Never miss a task or lose important care information again",
      benefits: [
        "Real-time logging prevents forgotten or missed documentation",
        "Standardized checklists ensure consistent care across all caregivers",
        "Digital records eliminate transcription errors and illegible notes",
        "Automatic timestamps provide precise visit documentation"
      ],
      imageKey: "completeDocumentation",
      cta: "Improve Documentation"
    },
    {
      title: "Stay audit-ready and build family trust",
      subtitle: "Transparent, detailed records that families and auditors love",
      benefits: [
        "HIPAA-compliant storage ensures regulatory compliance",
        "Detailed visit records build trust with families and care coordinators",
        "Exportable documentation ready for state audits or insurance reviews",
        "Progress tracking shows care improvements over time"
      ],
      imageKey: "auditReady",
      cta: "Build Trust"
    }
  ];

  // Text & In-App Communication-specific data for modular components
  const messagingProblemsData = [
    {
      problem: "Juggling multiple apps and missing important messages?",
      solution: "All communication in one secure, integrated platform",
      checks: [
        "Built-in SMS and in-app messaging from single dashboard",
        "Instant notifications ensure no message gets missed",
        "Complete message history logs for accountability"
      ],
      imageKey: "multipleApps",
      cta: "Unify Communication"
    },
    {
      problem: "Struggling to reach caregivers for urgent updates or changes?",
      solution: "Instant notifications that reach caregivers immediately",
      checks: [
        "Real-time alerts for open visits and last-minute changes",
        "Push notifications work whether app is open or closed",
        "One-to-one or group messaging for team-wide updates"
      ],
      imageKey: "urgentUpdates",
      cta: "Reach Instantly"
    },
    {
      problem: "Worried about communication security and compliance?",
      solution: "HIPAA-compliant messaging with complete audit trails",
      checks: [
        "All messages encrypted and securely stored",
        "Searchable message threads for easy review",
        "Compliance-ready communication logs for audits"
      ],
      imageKey: "secureMessaging",
      cta: "Book a Demo"
    }
  ];

  // Text & In-App Communication "Why Use" data
  const messagingWhyUseData = [
    {
      title: "Streamline communication and eliminate missed messages",
      subtitle: "Keep everyone connected with instant, reliable messaging",
      benefits: [
        "Unified platform eliminates need for multiple communication apps",
        "Instant notifications ensure urgent messages are seen immediately",
        "Group messaging capabilities improve team coordination",
        "Message history provides complete communication accountability"
      ],
      imageKey: "streamlineCommunication",
      cta: "Improve Communication"
    },
    {
      title: "Maintain security and compliance in all communications",
      subtitle: "Secure messaging that meets healthcare standards",
      benefits: [
        "HIPAA-compliant encryption protects sensitive information",
        "Audit-ready message logs support compliance requirements",
        "Secure platform eliminates risks of personal phone usage",
        "Centralized communication improves oversight and management"
      ],
      imageKey: "secureCommunication",
      cta: "Ensure Security"
    }
  ];

  // Background Screening-specific data for modular components
  const backgroundScreeningProblemsData = [
    {
      problem: "Spending hours manually running background checks and compliance screenings?",
      solution: "Automatic screenings that run without any manual intervention",
      checks: [
        "OIG, SAM, Sex Offender Registry, and Nursys checks run automatically",
        "Monthly OIG monitoring ensures ongoing compliance",
        "Detailed reports generated instantly for audits"
      ],
      imageKey: "manualScreening",
      cta: "Automate Screening"
    },
    {
      problem: "Worried about missing compliance deadlines or failed audits?",
      solution: "Always audit-ready with automated monitoring and reporting",
      checks: [
        "Monthly OIG rechecks prevent compliance lapses",
        "All screening records stored securely in caregiver profiles",
        "HIPAA and FCRA compliant to reduce legal risk"
      ],
      imageKey: "complianceDeadlines",
      cta: "Stay Compliant"
    },
    {
      problem: "Struggling to organize and access background check records?",
      solution: "Centralized storage with instant access to all screening data",
      checks: [
        "All background check results stored in one secure location",
        "Downloadable reports ready for credentialing or audits",
        "Complete screening history for every caregiver"
      ],
      imageKey: "organizeRecords",
      cta: "Book a Demo"
    }
  ];

  // Background Screening "Why Use" data
  const backgroundScreeningWhyUseData = [
    {
      title: "Eliminate compliance risks and save hours every week",
      subtitle: "Automated screening keeps you protected and audit-ready",
      benefits: [
        "Automatic screenings eliminate manual work and human error",
        "Monthly OIG monitoring prevents costly compliance violations",
        "Instant reports save hours during audit preparation",
        "Federal and state compliance reduces legal liability"
      ],
      imageKey: "eliminateRisks",
      cta: "Reduce Risk"
    },
    {
      title: "Hire with confidence knowing every caregiver is properly screened",
      subtitle: "Comprehensive background checks protect your clients and reputation",
      benefits: [
        "Multi-database screening ensures thorough vetting process",
        "Ongoing monitoring catches issues after initial hiring",
        "Secure storage protects sensitive screening information",
        "Audit-ready documentation supports credentialing requirements"
      ],
      imageKey: "hireConfidently",
      cta: "Hire Safely"
    }
  ];

  const currentFeature = features[activeTab];

  return (
    <div className="features-page">
      {/* Features Navigation */}
      <FeatureNavigation
        features={features}
        activeTab={activeTab}
        onTabChange={handleTabChange}
      />

      {/* Feature Hero Section */}
      <FeatureHero feature={currentFeature} />

      {/* Feature-specific sections for EVV */}
      {currentFeature.id === 'evv-gps' && (
        <>
          {/* Trusted By Section */}
          <FeatureTrustedBy />

          {/* Problem-Solution Zigzag Section */}
          <FeatureZigzag
            items={evvProblemsData}
            type="problems"
          />

          {/* Testimonial Section */}
          <FeatureTestimonial
            testimonial={currentFeature.testimonial}
            imageKey="maria"
          />

          {/* "We're not done yet" Parallax Section */}
          <FeatureParallax type="not-done" />

          {/* How It Works Section */}
          <FeatureHowItWorks
            title="How It Works"
            subtitle="Get started with EVV compliance in three simple steps"
            steps={currentFeature.howItWorks}
          />

          {/* Why Use Software Zigzag Section */}
          <FeatureZigzag
            items={evvWhyUseData}
            type="why-use"
          />

          {/* Manager Benefits Parallax Section */}
          <FeatureParallax
            type="manager"
            content={{
              title: "Managers Save 5+ hours a week on average with Telescope.",
              subtitle: "Ditch the sticky notes and spreadsheets.",
              cta: "Get started and thank us later"
            }}
          />

          {/* Ratings Parallax Section */}
          <FeatureParallax
            type="ratings"
            content={{
              title: "Trusted by thousands of home care agencies",
              subtitle: "Join the agencies that have transformed their EVV compliance with Telescope"
            }}
          />

          {/* Everything Just Works Section */}
          <FeatureEverythingWorks />

          {/* FAQ Section */}
          <FeatureFAQ
            title="Frequently Asked Questions"
            subtitle="Get answers to common questions about EVV compliance"
            faqs={[
              {
                question: "What is EVV and why do I need it?",
                answer: "Electronic Visit Verification (EVV) is a federally mandated system that tracks when caregivers arrive and leave client homes. It's required for Medicaid-funded personal care services to prevent fraud and ensure quality care."
              },
              {
                question: "How accurate is the GPS tracking?",
                answer: "Our GPS system is accurate within 3-5 meters and uses geofencing to ensure caregivers can only clock in when they're at the correct location. This prevents time theft and ensures compliance."
              },
              {
                question: "What happens if a caregiver's phone dies during a visit?",
                answer: "Our system includes backup options and manual override capabilities for administrators. We also provide detailed logs of all activities for audit purposes."
              },
              {
                question: "Is the system compliant with all state requirements?",
                answer: "Yes, Telescope is designed to meet federal EVV requirements and is compliant with state-specific regulations across all EVV-mandated states."
              },
              {
                question: "How long does implementation take?",
                answer: "Most agencies are up and running within 1-2 weeks. Our implementation team provides full training and support to ensure a smooth transition."
              }
            ]}
          />

          {/* Final CTA Section */}
          <FeatureFinalCTA
            title="Ready to Simplify EVV Compliance?"
            subtitle="Join thousands of agencies using Telescope to streamline operations and stay compliant."
            primaryButton={{
              text: "Book Your Demo",
              href: "#demo"
            }}
            secondaryButton={{
              text: "Start Free Trial",
              href: "#trial"
            }}
          />
        </>
      )}

      {/* Feature-specific sections for Caregiver App */}
      {currentFeature.id === 'caregiver-app' && (
        <>
          {/* Trusted By Section */}
          <FeatureTrustedBy />

          {/* Problem-Solution Zigzag Section */}
          <FeatureZigzag
            items={caregiverAppProblemsData}
            type="problems"
          />

          {/* Testimonial Section */}
          <FeatureTestimonial
            testimonial={{
              quote: "My caregivers love how simple and fast it is to log their care. They feel more connected and supported.",
              author: "Darnell P.",
              title: "Home Care Director",
              location: "Dallas, TX"
            }}
            imageKey="darnell"
          />

          {/* "We're not done yet" Parallax Section */}
          <FeatureParallax type="not-done" />

          {/* How It Works Section */}
          <FeatureHowItWorks
            title="How It Works"
            subtitle="Get your caregivers started with the mobile app in four simple steps"
            steps={[
              "Caregiver logs into the Telescope app on their smartphone",
              "They see their full schedule, client profile, and care plan for each visit",
              "During the visit, they track ADLs, complete forms, and leave care notes",
              "At the end of the visit, they clock out with GPS verification and submit all documentation"
            ]}
          />

          {/* Why Use Software Zigzag Section */}
          <FeatureZigzag
            items={caregiverAppWhyUseData}
            type="why-use"
          />

          {/* Manager Benefits Parallax Section */}
          <FeatureParallax
            type="manager"
            content={{
              title: "Caregivers save 30+ minutes per visit with the Telescope app.",
              subtitle: "Less paperwork, more time for care.",
              cta: "Empower your caregivers today"
            }}
          />

          {/* Ratings Parallax Section */}
          <FeatureParallax
            type="ratings"
            content={{
              title: "Loved by caregivers and administrators alike",
              subtitle: "Join the agencies whose caregivers love using Telescope every day"
            }}
          />

          {/* Everything Just Works Section */}
          <FeatureEverythingWorks />

          {/* FAQ Section */}
          <FeatureFAQ
            title="Frequently Asked Questions"
            subtitle="Get answers to common questions about the caregiver mobile app"
            faqs={[
              {
                question: "What devices does the caregiver app work on?",
                answer: "The Telescope caregiver app works on both iOS and Android devices. It's optimized for smartphones and tablets, ensuring your caregivers can use it on any device they prefer."
              },
              {
                question: "Can caregivers work offline?",
                answer: "Yes, the app has offline capabilities. Caregivers can complete documentation and forms even without internet connection, and everything syncs automatically when they're back online."
              },
              {
                question: "How secure is the caregiver app?",
                answer: "The app is fully HIPAA-compliant with end-to-end encryption. All data is securely transmitted and stored, ensuring client privacy and regulatory compliance."
              },
              {
                question: "Is training required for caregivers?",
                answer: "The app is designed to be intuitive and user-friendly. Most caregivers are up and running within minutes. We also provide training materials and support to ensure smooth adoption."
              },
              {
                question: "Can caregivers communicate with families through the app?",
                answer: "Yes, caregivers can send secure messages and updates to family members through the app, keeping everyone informed about care activities and client status."
              }
            ]}
          />

          {/* Final CTA Section */}
          <FeatureFinalCTA
            title="Ready to Empower Your Caregivers?"
            subtitle="Give your team the mobile tools they need to deliver exceptional care and stay connected."
            primaryButton={{
              text: "Book Your Demo",
              href: "#demo"
            }}
            secondaryButton={{
              text: "Download App Guide",
              href: "#guide"
            }}
          />
        </>
      )}

      {/* Feature-specific sections for Caregiver Training */}
      {currentFeature.id === 'training' && (
        <>
          {/* Trusted By Section */}
          <FeatureTrustedBy />

          {/* Problem-Solution Zigzag Section */}
          <FeatureZigzag
            items={caregiverTrainingProblemsData}
            type="problems"
          />

          {/* Testimonial Section */}
          <FeatureTestimonial
            testimonial={{
              quote: "Admins report saving 95% of their time on training tasks. The system is intuitive and the reminders keep everyone on track.",
              author: "Elizabeth S.",
              title: "Training Manager",
              location: "Phoenix, AZ"
            }}
            imageKey="elizabeth"
          />

          {/* "We're not done yet" Parallax Section */}
          <FeatureParallax type="not-done" />

          {/* How It Works Section */}
          <FeatureHowItWorks
            title="How It Works"
            subtitle="Get your training program up and running in four simple steps"
            steps={[
              "Assign training from the built-in course library or upload your own content",
              "Caregivers complete courses from any device — mobile, tablet, or desktop",
              "Automated reminders nudge caregivers to stay on schedule",
              "Admins view completion stats, print reports, and store certificates instantly"
            ]}
          />

          {/* Why Use Software Zigzag Section */}
          <FeatureZigzag
            items={caregiverTrainingWhyUseData}
            type="why-use"
          />

          {/* Manager Benefits Parallax Section */}
          <FeatureParallax
            type="manager"
            content={{
              title: "Training managers save 20+ hours per week with Telescope.",
              subtitle: "Automated assignments, tracking, and certificates.",
              cta: "Streamline training today"
            }}
          />

          {/* Ratings Parallax Section */}
          <FeatureParallax
            type="ratings"
            content={{
              title: "Trusted by training managers nationwide",
              subtitle: "Join the agencies that have transformed their training programs with Telescope"
            }}
          />

          {/* Everything Just Works Section */}
          <FeatureEverythingWorks />

          {/* FAQ Section */}
          <FeatureFAQ
            title="Frequently Asked Questions"
            subtitle="Get answers to common questions about caregiver training"
            faqs={[
              {
                question: "How many training courses are included?",
                answer: "Telescope includes over 150 state-compliant training courses covering HHA certification, dementia care, Alzheimer's, infection control, Spanish-language courses, and specialized certifications."
              },
              {
                question: "Can I upload my own training content?",
                answer: "Yes, you can upload custom orientation videos, policies, and training materials. These can be assigned alongside our built-in course library as part of your onboarding process."
              },
              {
                question: "How does automated training assignment work?",
                answer: "You can set training assignments based on job roles, hire dates, or specific client needs. The system automatically assigns relevant courses and sends reminders to ensure completion."
              },
              {
                question: "Are the certificates audit-ready?",
                answer: "Absolutely. All certificates are instantly generated and stored in the system. You can export completion reports and certificates at any time for state audits or compliance reviews."
              },
              {
                question: "Can caregivers complete training on mobile devices?",
                answer: "Yes, all training courses are mobile-optimized. Caregivers can complete training on smartphones, tablets, or desktop computers, making it convenient to learn anywhere."
              }
            ]}
          />

          {/* Final CTA Section */}
          <FeatureFinalCTA
            title="Ready to Transform Your Training Program?"
            subtitle="Ensure every caregiver is properly trained and compliant with automated training management."
            primaryButton={{
              text: "Book Your Demo",
              href: "#demo"
            }}
            secondaryButton={{
              text: "View Course Library",
              href: "#courses"
            }}
          />
        </>
      )}

      {/* Feature-specific sections for eSignature */}
      {currentFeature.id === 'esignature' && (
        <>
          {/* Trusted By Section */}
          <FeatureTrustedBy />

          {/* Problem-Solution Zigzag Section */}
          <FeatureZigzag
            items={eSignatureProblemsData}
            type="problems"
          />

          {/* Testimonial Section */}
          <FeatureTestimonial
            testimonial={{
              quote: "The eSignature feature alone saves us hours of admin work each week — and our clients love how easy it is.",
              author: "Bryan T.",
              title: "Agency Owner",
              location: "Tampa, FL"
            }}
            imageKey="bryan"
          />

          {/* "We're not done yet" Parallax Section */}
          <FeatureParallax type="not-done" />

          {/* How It Works Section */}
          <FeatureHowItWorks
            title="How It Works"
            subtitle="Get client signatures in three simple steps"
            steps={[
              "After completing a visit, the caregiver prompts the client to sign on their device",
              "The signature is saved and attached to the corresponding care record",
              "Admins can review, download, or print signed documents at any time"
            ]}
          />

          {/* Why Use Software Zigzag Section */}
          <FeatureZigzag
            items={eSignatureWhyUseData}
            type="why-use"
          />

          {/* Manager Benefits Parallax Section */}
          <FeatureParallax
            type="manager"
            content={{
              title: "Agencies save 10+ hours per week on paperwork with eSignatures.",
              subtitle: "No more printing, scanning, or chasing signatures.",
              cta: "Go paperless today"
            }}
          />

          {/* Ratings Parallax Section */}
          <FeatureParallax
            type="ratings"
            content={{
              title: "Trusted by agencies nationwide for secure signatures",
              subtitle: "Join the agencies that have eliminated paperwork with Telescope eSignatures"
            }}
          />

          {/* Everything Just Works Section */}
          <FeatureEverythingWorks />

          {/* FAQ Section */}
          <FeatureFAQ
            title="Frequently Asked Questions"
            subtitle="Get answers to common questions about digital signatures"
            faqs={[
              {
                question: "Are digital signatures legally binding?",
                answer: "Yes, Telescope eSignatures are fully compliant with electronic signature regulations including ESIGN Act and UETA. They hold the same legal weight as handwritten signatures."
              },
              {
                question: "How secure are the signatures?",
                answer: "All signatures are encrypted and stored securely with HIPAA compliance. Each signature includes a complete audit trail with timestamp, location, and device information."
              },
              {
                question: "Can clients sign on any device?",
                answer: "Yes, clients can sign on smartphones, tablets, or any touch-enabled device. The signature capture works seamlessly across all platforms."
              },
              {
                question: "What happens if a client refuses to sign digitally?",
                answer: "While most clients find digital signing convenient, caregivers can still note any refusal in the system and follow your agency's standard procedures for unsigned visits."
              },
              {
                question: "Can I export signed documents for billing?",
                answer: "Absolutely. All signed documents can be exported individually or in bulk for Medicaid billing, insurance claims, or audit purposes. Documents are available in PDF format."
              }
            ]}
          />

          {/* Final CTA Section */}
          <FeatureFinalCTA
            title="Ready to Go Paperless with Confidence?"
            subtitle="Eliminate paperwork chaos and capture secure, legally binding signatures at every visit."
            primaryButton={{
              text: "Book Your Demo",
              href: "#demo"
            }}
            secondaryButton={{
              text: "See Legal Compliance",
              href: "#compliance"
            }}
          />
        </>
      )}

      {/* Feature-specific sections for Client Portal */}
      {currentFeature.id === 'portals' && (
        <>
          {/* Trusted By Section */}
          <FeatureTrustedBy />

          {/* Problem-Solution Zigzag Section */}
          <FeatureZigzag
            items={clientPortalProblemsData}
            type="problems"
          />

          {/* Testimonial Section */}
          <FeatureTestimonial
            testimonial={{
              quote: "Families love being able to check in on their loved one's care. It gives them peace of mind and builds trust.",
              author: "Elaine K.",
              title: "Care Manager",
              location: "Houston, TX"
            }}
            imageKey="elaine"
          />

          {/* "We're not done yet" Parallax Section */}
          <FeatureParallax type="not-done" />

          {/* How It Works Section */}
          <FeatureHowItWorks
            title="How It Works"
            subtitle="Get families connected in three simple steps"
            steps={[
              "Admin enables the client portal and grants family access",
              "Families log in securely via their phone, tablet, or computer",
              "They can view schedules, notes, and care plan details in real time"
            ]}
          />

          {/* Why Use Software Zigzag Section */}
          <FeatureZigzag
            items={clientPortalWhyUseData}
            type="why-use"
          />

          {/* Manager Benefits Parallax Section */}
          <FeatureParallax
            type="manager"
            content={{
              title: "Agencies reduce family calls by 70% with client portals.",
              subtitle: "More time for care, less time on the phone.",
              cta: "Reduce calls today"
            }}
          />

          {/* Ratings Parallax Section */}
          <FeatureParallax
            type="ratings"
            content={{
              title: "Trusted by families nationwide for transparent care",
              subtitle: "Join the agencies building stronger family relationships with Telescope portals"
            }}
          />

          {/* Everything Just Works Section */}
          <FeatureEverythingWorks />

          {/* FAQ Section */}
          <FeatureFAQ
            title="Frequently Asked Questions"
            subtitle="Get answers to common questions about client and family portals"
            faqs={[
              {
                question: "How do families access the portal?",
                answer: "Families receive secure login credentials via email or text. They can access the portal from any device with internet connection - smartphone, tablet, or computer."
              },
              {
                question: "What information can families see?",
                answer: "Families can view care schedules, visit confirmations, caregiver notes, completed ADLs, medication reminders, and care plan updates. Admins control exactly what information is shared."
              },
              {
                question: "Is the portal HIPAA compliant?",
                answer: "Yes, all portal access is fully HIPAA compliant with secure login, encrypted data transmission, and role-based access controls to protect client privacy."
              },
              {
                question: "Can multiple family members have access?",
                answer: "Absolutely. You can grant portal access to multiple family members with different permission levels, ensuring the right people have access to appropriate information."
              },
              {
                question: "Do families get notifications about updates?",
                answer: "Yes, families can receive email or text notifications when new care notes are added, schedules change, or visits are completed, keeping them informed in real-time."
              }
            ]}
          />

          {/* Final CTA Section */}
          <FeatureFinalCTA
            title="Ready to Build Stronger Family Relationships?"
            subtitle="Give families the transparency and peace of mind they deserve with secure client portals."
            primaryButton={{
              text: "Book Your Demo",
              href: "#demo"
            }}
            secondaryButton={{
              text: "See Portal Demo",
              href: "#portal-demo"
            }}
          />
        </>
      )}

      {/* Feature-specific sections for Custom Forms */}
      {currentFeature.id === 'custom-forms' && (
        <>
          {/* Trusted By Section */}
          <FeatureTrustedBy />

          {/* Problem-Solution Zigzag Section */}
          <FeatureZigzag
            items={customFormsProblemsData}
            type="problems"
          />

          {/* Testimonial Section */}
          <FeatureTestimonial
            testimonial={{
              quote: "We needed a custom COVID screening form — and Telescope let us build it in under 10 minutes.",
              author: "Nancy M.",
              title: "Agency Compliance Lead",
              location: "Miami, FL"
            }}
            imageKey="nancy"
          />

          {/* "We're not done yet" Parallax Section */}
          <FeatureParallax type="not-done" />

          {/* How It Works Section */}
          <FeatureHowItWorks
            title="How It Works"
            subtitle="Create and deploy custom forms in four simple steps"
            steps={[
              "Choose a pre-built form template or start from scratch",
              "Customize fields using drag-and-drop form elements",
              "Assign forms to caregivers, visits, or clients",
              "Completed forms are automatically saved and accessible anytime"
            ]}
          />

          {/* Why Use Software Zigzag Section */}
          <FeatureZigzag
            items={customFormsWhyUseData}
            type="why-use"
          />

          {/* Manager Benefits Parallax Section */}
          <FeatureParallax
            type="manager"
            content={{
              title: "Agencies create custom forms 10x faster with Telescope.",
              subtitle: "No coding required, unlimited possibilities.",
              cta: "Start building today"
            }}
          />

          {/* Ratings Parallax Section */}
          <FeatureParallax
            type="ratings"
            content={{
              title: "Trusted by agencies for flexible form solutions",
              subtitle: "Join the agencies that have eliminated paperwork with custom digital forms"
            }}
          />

          {/* Everything Just Works Section */}
          <FeatureEverythingWorks />

          {/* FAQ Section */}
          <FeatureFAQ
            title="Frequently Asked Questions"
            subtitle="Get answers to common questions about custom forms"
            faqs={[
              {
                question: "Do I need coding skills to build forms?",
                answer: "Not at all! Our drag-and-drop form builder is designed for non-technical users. You can create professional forms with custom fields, logic, and validation without any coding knowledge."
              },
              {
                question: "What types of forms can I create?",
                answer: "You can create any type of form your agency needs - client intake forms, incident reports, COVID screenings, caregiver evaluations, family surveys, and more. The possibilities are unlimited."
              },
              {
                question: "Can forms be filled out offline?",
                answer: "Yes, caregivers can complete forms on their mobile devices even without internet connection. The forms will automatically sync when they're back online."
              },
              {
                question: "How do I share completed forms with families?",
                answer: "Completed forms can be exported as PDFs and shared via email, or families can view them directly through the client portal if they have access."
              },
              {
                question: "Can I use conditional logic in my forms?",
                answer: "Absolutely! You can create smart forms that show or hide fields based on previous answers, making forms more efficient and user-friendly for your caregivers."
              }
            ]}
          />

          {/* Final CTA Section */}
          <FeatureFinalCTA
            title="Ready to Build Forms That Actually Work?"
            subtitle="Stop settling for generic forms. Create exactly what your agency needs with our powerful form builder."
            primaryButton={{
              text: "Book Your Demo",
              href: "#demo"
            }}
            secondaryButton={{
              text: "Try Form Builder",
              href: "#form-builder"
            }}
          />
        </>
      )}

      {/* Feature-specific sections for Hiring & Onboarding */}
      {currentFeature.id === 'hiring' && (
        <>
          {/* Trusted By Section */}
          <FeatureTrustedBy />

          {/* Problem-Solution Zigzag Section */}
          <FeatureZigzag
            items={hiringProblemsData}
            type="problems"
          />

          {/* Testimonial Section */}
          <FeatureTestimonial
            testimonial={{
              quote: "Before Telescope, we lost great caregivers to slow follow-up. Now we engage instantly, and onboarding is seamless.",
              author: "Lauren M.",
              title: "Hiring Manager",
              location: "Austin, TX"
            }}
            imageKey="lauren"
          />

          {/* "We're not done yet" Parallax Section */}
          <FeatureParallax type="not-done" />

          {/* How It Works Section */}
          <FeatureHowItWorks
            title="How It Works"
            subtitle="Streamline hiring from posting to onboarding in four steps"
            steps={[
              "Create a job post and publish it to multiple boards in one click",
              "Applicants receive automated text follow-ups and a link to self-schedule interviews",
              "Once hired, candidates receive onboarding documents via e-signature",
              "Admins can track progress and status updates from a centralized dashboard"
            ]}
          />

          {/* Why Use Software Zigzag Section */}
          <FeatureZigzag
            items={hiringWhyUseData}
            type="why-use"
          />

          {/* Manager Benefits Parallax Section */}
          <FeatureParallax
            type="manager"
            content={{
              title: "Hiring managers save 15+ hours per hire with Telescope.",
              subtitle: "Automated workflows, instant engagement, seamless onboarding.",
              cta: "Streamline hiring today"
            }}
          />

          {/* Ratings Parallax Section */}
          <FeatureParallax
            type="ratings"
            content={{
              title: "Trusted by hiring managers nationwide",
              subtitle: "Join the agencies that have transformed their hiring process with Telescope"
            }}
          />

          {/* Everything Just Works Section */}
          <FeatureEverythingWorks />

          {/* FAQ Section */}
          <FeatureFAQ
            title="Frequently Asked Questions"
            subtitle="Get answers to common questions about hiring and onboarding"
            faqs={[
              {
                question: "Which job boards can I post to?",
                answer: "Telescope integrates with major job boards including Indeed, ZipRecruiter, and Glassdoor, plus creates a custom careers page for your agency website."
              },
              {
                question: "How does automated text engagement work?",
                answer: "When someone applies, they immediately receive a personalized text message with next steps, interview scheduling links, and important information about your agency."
              },
              {
                question: "Can candidates schedule interviews outside business hours?",
                answer: "Yes, candidates can self-schedule interviews 24/7 based on your available time slots. The system automatically sends confirmations and reminders to both parties."
              },
              {
                question: "What onboarding documents can be e-signed?",
                answer: "All standard onboarding documents including offer letters, W-4s, I-9s, policy acknowledgments, background check authorizations, and any custom forms your agency requires."
              },
              {
                question: "How do I track compliance for new hires?",
                answer: "The centralized dashboard shows exactly which documents each candidate has completed, what's pending, and any missing requirements before they can start working."
              }
            ]}
          />

          {/* Final CTA Section */}
          <FeatureFinalCTA
            title="Ready to Transform Your Hiring Process?"
            subtitle="Stop losing great caregivers to slow processes. Hire faster and onboard smarter with Telescope."
            primaryButton={{
              text: "Book Your Demo",
              href: "#demo"
            }}
            secondaryButton={{
              text: "See Hiring Dashboard",
              href: "#hiring-demo"
            }}
          />
        </>
      )}

      {/* Feature-specific sections for Invoicing */}
      {currentFeature.id === 'invoicing' && (
        <>
          {/* Trusted By Section */}
          <FeatureTrustedBy />

          {/* Problem-Solution Zigzag Section */}
          <FeatureZigzag
            items={invoicingProblemsData}
            type="problems"
          />

          {/* Testimonial Section */}
          <FeatureTestimonial
            testimonial={{
              quote: "We used to spend days creating invoices. Now it's automated, accurate, and we get paid faster.",
              author: "Harold L.",
              title: "Owner",
              location: "Dignity Home Care"
            }}
            imageKey="harold"
          />

          {/* "We're not done yet" Parallax Section */}
          <FeatureParallax type="not-done" />

          {/* How It Works Section */}
          <FeatureHowItWorks
            title="How It Works"
            subtitle="Streamline billing from visits to payment in three steps"
            steps={[
              "Approved caregiver shifts and visit records feed directly into the invoicing tool",
              "Telescope calculates invoice totals based on your agency's billing rules",
              "Admins can review, edit if needed, and export invoices for emailing or accounting systems"
            ]}
          />

          {/* Why Use Software Zigzag Section */}
          <FeatureZigzag
            items={invoicingWhyUseData}
            type="why-use"
          />

          {/* Manager Benefits Parallax Section */}
          <FeatureParallax
            type="manager"
            content={{
              title: "Agencies get paid 40% faster with automated invoicing.",
              subtitle: "Less time billing, more time growing.",
              cta: "Speed up payments today"
            }}
          />

          {/* Ratings Parallax Section */}
          <FeatureParallax
            type="ratings"
            content={{
              title: "Trusted by agencies for accurate, fast billing",
              subtitle: "Join the agencies that have transformed their invoicing with Telescope"
            }}
          />

          {/* Everything Just Works Section */}
          <FeatureEverythingWorks />

          {/* FAQ Section */}
          <FeatureFAQ
            title="Frequently Asked Questions"
            subtitle="Get answers to common questions about automated invoicing"
            faqs={[
              {
                question: "How does automatic invoice generation work?",
                answer: "Telescope automatically creates invoices based on approved caregiver visits, hours worked, and your custom billing rules. No manual data entry required - just review and send."
              },
              {
                question: "Can I set different rates for different services?",
                answer: "Yes, you can configure custom billing rules including hourly rates, flat fees, service-specific pricing, and client-specific terms. The system automatically applies the correct rates."
              },
              {
                question: "Which accounting software does Telescope integrate with?",
                answer: "Telescope integrates with QuickBooks, Xero, and other popular accounting platforms. You can also export invoices as PDFs or CSV files for any system."
              },
              {
                question: "Can I track payment status?",
                answer: "Absolutely. The real-time dashboard shows what's been billed, paid, and outstanding. You can track payment status and aging reports to manage cash flow effectively."
              },
              {
                question: "What if I need to make changes to an invoice?",
                answer: "You can review and edit invoices before sending them. The system also maintains an audit trail of any changes made for compliance and record-keeping purposes."
              }
            ]}
          />

          {/* Final CTA Section */}
          <FeatureFinalCTA
            title="Ready to Get Paid Faster?"
            subtitle="Stop spending days on manual invoicing. Automate your billing and improve cash flow with Telescope."
            primaryButton={{
              text: "Book Your Demo",
              href: "#demo"
            }}
            secondaryButton={{
              text: "See Billing Dashboard",
              href: "#billing-demo"
            }}
          />
        </>
      )}

      {/* Feature-specific sections for Client Surveys */}
      {currentFeature.id === 'surveys' && (
        <>
          {/* Trusted By Section */}
          <FeatureTrustedBy />

          {/* Problem-Solution Zigzag Section */}
          <FeatureZigzag
            items={surveysProblemsData}
            type="problems"
          />

          {/* Testimonial Section */}
          <FeatureTestimonial
            testimonial={{
              quote: "The automated surveys helped us catch small issues before they became big ones. Families noticed, and so did our team.",
              author: "Jeff R.",
              title: "Agency Owner",
              location: "Denver, CO"
            }}
            imageKey="jeff"
          />

          {/* "We're not done yet" Parallax Section */}
          <FeatureParallax type="not-done" />

          {/* How It Works Section */}
          <FeatureHowItWorks
            title="How It Works"
            subtitle="Transform feedback into actionable insights in three steps"
            steps={[
              "Telescope sends a brief satisfaction survey to clients and caregivers via SMS and email",
              "Responses are collected and stored securely in your dashboard",
              "Each month, the system generates a detailed report highlighting feedback trends and satisfaction scores"
            ]}
          />

          {/* Why Use Software Zigzag Section */}
          <FeatureZigzag
            items={surveysWhyUseData}
            type="why-use"
          />

          {/* Manager Benefits Parallax Section */}
          <FeatureParallax
            type="manager"
            content={{
              title: "Agencies using surveys retain 25% more clients annually.",
              subtitle: "Know what's working. Fix what's not.",
              cta: "Start improving today"
            }}
          />

          {/* Ratings Parallax Section */}
          <FeatureParallax
            type="ratings"
            content={{
              title: "Trusted by agencies for meaningful feedback collection",
              subtitle: "Join the agencies using data-driven insights to improve care quality"
            }}
          />

          {/* Everything Just Works Section */}
          <FeatureEverythingWorks />

          {/* FAQ Section */}
          <FeatureFAQ
            title="Frequently Asked Questions"
            subtitle="Get answers to common questions about satisfaction surveys"
            faqs={[
              {
                question: "How often are surveys sent out?",
                answer: "Surveys are automatically sent after key visits or milestones that you configure. You can set them to go out after first visits, monthly, quarterly, or based on specific triggers like care plan changes."
              },
              {
                question: "What types of questions can I include?",
                answer: "You can customize survey questions to focus on care quality, caregiver performance, communication, scheduling, and overall satisfaction. Both multiple choice and open-ended questions are supported."
              },
              {
                question: "How do clients receive and complete surveys?",
                answer: "Surveys are sent via SMS text and email with a simple link. Clients can complete them on any device - smartphone, tablet, or computer - in just a few minutes."
              },
              {
                question: "Is the survey data secure and HIPAA compliant?",
                answer: "Yes, all survey responses are collected and stored with full HIPAA compliance. Data is encrypted and access is controlled to protect client privacy and maintain trust."
              },
              {
                question: "Can I see individual responses or just aggregate data?",
                answer: "You can view both individual responses for specific follow-up and aggregate data in monthly reports. This allows you to address specific concerns while tracking overall trends."
              }
            ]}
          />

          {/* Final CTA Section */}
          <FeatureFinalCTA
            title="Ready to Turn Feedback Into Growth?"
            subtitle="Stop guessing what clients think. Get real insights and improve care quality with automated satisfaction surveys."
            primaryButton={{
              text: "Book Your Demo",
              href: "#demo"
            }}
            secondaryButton={{
              text: "See Sample Report",
              href: "#survey-demo"
            }}
          />
        </>
      )}

      {/* Feature-specific sections for Smart Scheduling */}
      {currentFeature.id === 'smart-scheduling' && (
        <>
          {/* Trusted By Section */}
          <FeatureTrustedBy />

          {/* Problem-Solution Zigzag Section */}
          <FeatureZigzag
            items={schedulingProblemsData}
            type="problems"
          />

          {/* Testimonial Section */}
          <FeatureTestimonial
            testimonial={{
              quote: "Smart scheduling cut our admin time in half and our caregivers love the optimized routes. It's like having a scheduling expert on staff 24/7.",
              author: "Patricia M.",
              title: "Operations Manager",
              location: "Seattle, WA"
            }}
            imageKey="patricia"
          />

          {/* "We're not done yet" Parallax Section */}
          <FeatureParallax type="not-done" />

          {/* How It Works Section */}
          <FeatureHowItWorks
            title="How It Works"
            subtitle="Transform your scheduling process with AI in three steps"
            steps={[
              "AI analyzes caregiver skills, client needs, and geographic locations to create optimal matches",
              "The system automatically generates schedules with optimized routes and minimal conflicts",
              "Real-time adjustments handle changes, cancellations, and emergency coverage seamlessly"
            ]}
          />

          {/* Why Use Software Zigzag Section */}
          <FeatureZigzag
            items={schedulingWhyUseData}
            type="why-use"
          />

          {/* Manager Benefits Parallax Section */}
          <FeatureParallax
            type="manager"
            content={{
              title: "Schedulers save 10+ hours per week with AI-powered scheduling.",
              subtitle: "Less time planning, more time growing.",
              cta: "Automate scheduling today"
            }}
          />

          {/* Ratings Parallax Section */}
          <FeatureParallax
            type="ratings"
            content={{
              title: "Trusted by agencies for intelligent scheduling solutions",
              subtitle: "Join the agencies that have eliminated scheduling headaches with Telescope"
            }}
          />

          {/* Everything Just Works Section */}
          <FeatureEverythingWorks />

          {/* FAQ Section */}
          <FeatureFAQ
            title="Frequently Asked Questions"
            subtitle="Get answers to common questions about smart scheduling"
            faqs={[
              {
                question: "How does AI-powered caregiver matching work?",
                answer: "The AI analyzes caregiver skills, certifications, client preferences, geographic location, and historical performance to create optimal matches that improve care quality and satisfaction."
              },
              {
                question: "Can the system handle last-minute schedule changes?",
                answer: "Yes, the system automatically adjusts schedules in real-time for cancellations, no-shows, or emergency coverage needs. It finds the best available caregiver and optimizes routes accordingly."
              },
              {
                question: "How much time does route optimization actually save?",
                answer: "Most agencies see a 30% reduction in travel time between visits. For a caregiver with 6 visits per day, this typically saves 45-60 minutes of drive time daily."
              },
              {
                question: "Does the scheduling system integrate with existing calendars?",
                answer: "Yes, schedules can sync with Google Calendar, Outlook, and other calendar systems. Caregivers can view their schedules on any device and receive automatic updates."
              },
              {
                question: "What happens if there are scheduling conflicts?",
                answer: "The system automatically detects potential conflicts before they occur and suggests alternative solutions. It prevents double-booking and ensures adequate coverage for all clients."
              }
            ]}
          />

          {/* Final CTA Section */}
          <FeatureFinalCTA
            title="Ready to Schedule Smarter, Not Harder?"
            subtitle="Stop spending hours on manual scheduling. Let AI optimize your operations and improve care quality."
            primaryButton={{
              text: "Book Your Demo",
              href: "#demo"
            }}
            secondaryButton={{
              text: "See AI in Action",
              href: "#scheduling-demo"
            }}
          />
        </>
      )}

      {/* Feature-specific sections for Task & ADL Tracking */}
      {currentFeature.id === 'task-tracking' && (
        <>
          {/* Trusted By Section */}
          <FeatureTrustedBy />

          {/* Problem-Solution Zigzag Section */}
          <FeatureZigzag
            items={taskTrackingProblemsData}
            type="problems"
          />

          {/* Testimonial Section */}
          <FeatureTestimonial
            testimonial={{
              quote: "We no longer chase down handwritten notes. Our caregivers document everything in real time, and we're always audit-ready.",
              author: "Lindsey H.",
              title: "Director of Nursing",
              location: "Phoenix, AZ"
            }}
            imageKey="lindsey"
          />

          {/* "We're not done yet" Parallax Section */}
          <FeatureParallax type="not-done" />

          {/* How It Works Section */}
          <FeatureHowItWorks
            title="How It Works"
            subtitle="Transform care documentation in three simple steps"
            steps={[
              "Admin assigns a care plan with customized ADL checklists to each client",
              "Caregiver opens the app, sees the tasks for the visit, and logs each one during the shift",
              "Notes and documentation are saved in real time and synced to the admin dashboard"
            ]}
          />

          {/* Why Use Software Zigzag Section */}
          <FeatureZigzag
            items={taskTrackingWhyUseData}
            type="why-use"
          />

          {/* Manager Benefits Parallax Section */}
          <FeatureParallax
            type="manager"
            content={{
              title: "Agencies improve documentation accuracy by 95% with digital ADL tracking.",
              subtitle: "Real-time logging, zero lost paperwork.",
              cta: "Improve documentation today"
            }}
          />

          {/* Ratings Parallax Section */}
          <FeatureParallax
            type="ratings"
            content={{
              title: "Trusted by nursing directors for comprehensive care documentation",
              subtitle: "Join the agencies that have eliminated paperwork chaos with Telescope"
            }}
          />

          {/* Everything Just Works Section */}
          <FeatureEverythingWorks />

          {/* FAQ Section */}
          <FeatureFAQ
            title="Frequently Asked Questions"
            subtitle="Get answers to common questions about task and ADL documentation"
            faqs={[
              {
                question: "What types of ADLs can be tracked?",
                answer: "Telescope tracks all standard ADLs including bathing, grooming, mobility assistance, medication reminders, meal preparation, toileting, and any custom tasks specific to your client's care plan."
              },
              {
                question: "Can caregivers add notes beyond the checklist items?",
                answer: "Yes, caregivers can add detailed notes, observations, and comments for each task or visit. They can also attach photos when appropriate and permitted by your agency policies."
              },
              {
                question: "How does real-time documentation work offline?",
                answer: "Caregivers can complete documentation even without internet connection. All data is stored locally on the device and automatically syncs when connectivity is restored."
              },
              {
                question: "Can families see the completed ADL documentation?",
                answer: "Yes, if enabled through the client portal, families can view completed tasks, care notes, and progress updates in real-time, building trust and transparency."
              },
              {
                question: "How is the documentation stored and secured?",
                answer: "All documentation is encrypted and stored with full HIPAA compliance. Data includes timestamps, caregiver signatures, and GPS verification for complete audit trails."
              }
            ]}
          />

          {/* Final CTA Section */}
          <FeatureFinalCTA
            title="Ready to Make Documentation Automatic?"
            subtitle="Stop chasing paperwork and ensure every detail is captured with real-time ADL tracking."
            primaryButton={{
              text: "Book Your Demo",
              href: "#demo"
            }}
            secondaryButton={{
              text: "See ADL Tracking",
              href: "#adl-demo"
            }}
          />
        </>
      )}

      {/* Feature-specific sections for Text & In-App Communication */}
      {currentFeature.id === 'messaging' && (
        <>
          {/* Trusted By Section */}
          <FeatureTrustedBy />

          {/* Problem-Solution Zigzag Section */}
          <FeatureZigzag
            items={messagingProblemsData}
            type="problems"
          />

          {/* Testimonial Section */}
          <FeatureTestimonial
            testimonial={{
              quote: "Now we don't have to call or text caregivers separately — everything goes through Telescope, and nothing gets missed.",
              author: "Diana H.",
              title: "Office Manager",
              location: "Raleigh, NC"
            }}
            imageKey="diana"
          />

          {/* "We're not done yet" Parallax Section */}
          <FeatureParallax type="not-done" />

          {/* How It Works Section */}
          <FeatureHowItWorks
            title="How It Works"
            subtitle="Simplify team communication in three steps"
            steps={[
              "Admins send a message via SMS or in-app from the Telescope dashboard",
              "Caregivers get instant notifications on their phone, whether logged into the app or not",
              "All communication is logged in a searchable message thread for future review"
            ]}
          />

          {/* Why Use Software Zigzag Section */}
          <FeatureZigzag
            items={messagingWhyUseData}
            type="why-use"
          />

          {/* Manager Benefits Parallax Section */}
          <FeatureParallax
            type="manager"
            content={{
              title: "Agencies reduce communication time by 60% with unified messaging.",
              subtitle: "One platform, instant connection, zero missed messages.",
              cta: "Streamline communication today"
            }}
          />

          {/* Ratings Parallax Section */}
          <FeatureParallax
            type="ratings"
            content={{
              title: "Trusted by agencies for secure, reliable communication",
              subtitle: "Join the agencies that have unified their team communication with Telescope"
            }}
          />

          {/* Everything Just Works Section */}
          <FeatureEverythingWorks />

          {/* FAQ Section */}
          <FeatureFAQ
            title="Frequently Asked Questions"
            subtitle="Get answers to common questions about messaging and communication"
            faqs={[
              {
                question: "Can I send messages to multiple caregivers at once?",
                answer: "Yes, you can send group messages to entire teams, specific shifts, or custom groups. You can also send individual messages for personalized communication."
              },
              {
                question: "Do caregivers need to have the app open to receive messages?",
                answer: "No, caregivers receive push notifications on their phones even when the app is closed. They can respond directly from the notification or open the app for full messaging features."
              },
              {
                question: "Is messaging included in SMS costs?",
                answer: "SMS messaging is included in your Telescope subscription with generous monthly allowances. Additional SMS usage is available at competitive rates if needed."
              },
              {
                question: "Can families receive messages through the system?",
                answer: "Yes, if enabled, families can receive important updates and communicate through the client portal or via SMS, maintaining HIPAA compliance throughout."
              },
              {
                question: "How long are message histories stored?",
                answer: "All message histories are stored securely and are searchable indefinitely. This provides complete communication accountability and supports audit requirements."
              }
            ]}
          />

          {/* Final CTA Section */}
          <FeatureFinalCTA
            title="Ready to Unify Your Team Communication?"
            subtitle="Stop juggling multiple apps. Keep everyone connected with secure, integrated messaging."
            primaryButton={{
              text: "Book Your Demo",
              href: "#demo"
            }}
            secondaryButton={{
              text: "See Messaging Demo",
              href: "#messaging-demo"
            }}
          />
        </>
      )}

      {/* Feature-specific sections for Background Screening */}
      {currentFeature.id === 'background-screening' && (
        <>
          {/* Trusted By Section */}
          <FeatureTrustedBy />

          {/* Problem-Solution Zigzag Section */}
          <FeatureZigzag
            items={backgroundScreeningProblemsData}
            type="problems"
          />

          {/* Testimonial Section */}
          <FeatureTestimonial
            testimonial={{
              quote: "Our compliance process used to take hours. With Telescope, background checks are automatic and we're always audit-ready.",
              author: "Tara B.",
              title: "HR Director",
              location: "San Antonio, TX"
            }}
            imageKey="tara"
          />

          {/* "We're not done yet" Parallax Section */}
          <FeatureParallax type="not-done" />

          {/* How It Works Section */}
          <FeatureHowItWorks
            title="How It Works"
            subtitle="Automate compliance screening in four simple steps"
            steps={[
              "Background checks are automatically triggered during the hiring process",
              "OIG, SAM, Sex Offender, and Nursys checks are completed in seconds — no manual input required",
              "Monthly OIG checks run on all active caregivers with updated logs and reports",
              "Admins can download comprehensive reports directly from the caregiver dashboard"
            ]}
          />

          {/* Why Use Software Zigzag Section */}
          <FeatureZigzag
            items={backgroundScreeningWhyUseData}
            type="why-use"
          />

          {/* Manager Benefits Parallax Section */}
          <FeatureParallax
            type="manager"
            content={{
              title: "HR teams save 20+ hours per month with automated background screening.",
              subtitle: "Automatic checks, ongoing monitoring, instant reports.",
              cta: "Automate compliance today"
            }}
          />

          {/* Ratings Parallax Section */}
          <FeatureParallax
            type="ratings"
            content={{
              title: "Trusted by agencies for comprehensive background screening",
              subtitle: "Join the agencies that have automated their compliance with Telescope"
            }}
          />

          {/* Everything Just Works Section */}
          <FeatureEverythingWorks />

          {/* FAQ Section */}
          <FeatureFAQ
            title="Frequently Asked Questions"
            subtitle="Get answers to common questions about background screening"
            faqs={[
              {
                question: "What types of background checks are included?",
                answer: "Telescope automatically runs OIG exclusion checks, SAM (System for Award Management) checks, Sex Offender Registry searches, and Nursys license verification for healthcare professionals."
              },
              {
                question: "How often are the background checks updated?",
                answer: "OIG exclusion checks are automatically re-run monthly for all active caregivers. Other checks can be configured to run at intervals you specify or triggered manually when needed."
              },
              {
                question: "Are the background checks legally compliant?",
                answer: "Yes, all background screening processes are HIPAA and FCRA (Fair Credit Reporting Act) compliant, reducing your legal risk and ensuring proper handling of sensitive information."
              },
              {
                question: "Can I download reports for audits or credentialing?",
                answer: "Absolutely. All screening results are stored securely and can be downloaded as comprehensive reports directly from each caregiver's profile, ready for audits or credentialing purposes."
              },
              {
                question: "What happens if a caregiver fails a background check?",
                answer: "The system immediately flags any issues and sends alerts to administrators. You can set up automated workflows to handle different types of screening results according to your agency policies."
              }
            ]}
          />

          {/* Final CTA Section */}
          <FeatureFinalCTA
            title="Ready to Automate Your Compliance Screening?"
            subtitle="Stop spending hours on manual background checks. Ensure every hire is properly screened and stay audit-ready."
            primaryButton={{
              text: "Book Your Demo",
              href: "#demo"
            }}
            secondaryButton={{
              text: "See Screening Reports",
              href: "#screening-demo"
            }}
          />
        </>
      )}

      {/* Default sections for other features */}
      {currentFeature.id !== 'evv-gps' && currentFeature.id !== 'caregiver-app' && currentFeature.id !== 'training' && currentFeature.id !== 'esignature' && currentFeature.id !== 'portals' && currentFeature.id !== 'custom-forms' && currentFeature.id !== 'hiring' && currentFeature.id !== 'invoicing' && currentFeature.id !== 'surveys' && currentFeature.id !== 'smart-scheduling' && currentFeature.id !== 'task-tracking' && currentFeature.id !== 'messaging' && currentFeature.id !== 'background-screening' && (
        <>
          <FeatureEverythingWorks />
          <FeatureFinalCTA />
        </>
      )}
    </div>
  );
};

export default Features;