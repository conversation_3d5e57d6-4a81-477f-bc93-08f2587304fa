@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap');

/* Trusted By Section */
.trusted-by {
  padding: 80px 0;
  background: linear-gradient(135deg, #fafbff 0%, #f8faff 100%);
  font-family: "Plus Jakarta Sans", sans-serif;
  overflow: hidden;
  position: relative;
}

.trusted-by__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Header */
.trusted-by__header {
  text-align: center;
  margin-bottom: 60px;
}

.trusted-by__title {
  font-size: clamp(1.75rem, 4vw, 2.5rem);
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  letter-spacing: -0.02em;
  line-height: 1.2;
}

/* Marquee Container */
.trusted-by__marquee {
  width: 100%;
  overflow: hidden;
  position: relative;
  mask: linear-gradient(
    90deg,
    transparent 0%,
    black 10%,
    black 90%,
    transparent 100%
  );
  -webkit-mask: linear-gradient(
    90deg,
    transparent 0%,
    black 10%,
    black 90%,
    transparent 100%
  );
}

/* Marquee Track */
.trusted-by__marquee-track {
  display: flex;
  align-items: center;
  gap: 60px;
  animation: marquee 40s linear infinite;
  width: fit-content;
}

/* Marquee Animation */
@keyframes marquee {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* Pause animation on hover */
.trusted-by__marquee:hover .trusted-by__marquee-track {
  animation-play-state: paused;
}

/* Company Items */
.trusted-by__company {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  min-width: 120px;
}

.trusted-by__company:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border-color: rgba(0, 106, 193, 0.1);
}

.trusted-by__company-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 96px;
  height: 96px;
  background: white;
  border-radius: 12px;
  border: 1px solid rgba(0, 106, 193, 0.1);
  overflow: hidden;
}

.trusted-by__company-logo-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  padding: 6px;
}

.trusted-by__company-name {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  letter-spacing: -0.01em;
}

/* Visibility Animation */
.trusted-by {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.trusted-by--visible {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .trusted-by {
    padding: 60px 0;
  }

  .trusted-by__container {
    padding: 0 16px;
  }

  .trusted-by__header {
    margin-bottom: 40px;
  }

  .trusted-by__marquee-track {
    gap: 40px;
    animation-duration: 30s;
  }

  .trusted-by__company {
    padding: 20px;
    min-width: 100px;
  }

  .trusted-by__company-logo {
    width: 80px;
    height: 80px;
  }

  .trusted-by__company-name {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .trusted-by {
    padding: 40px 0;
  }

  .trusted-by__marquee-track {
    gap: 30px;
    animation-duration: 25s;
  }

  .trusted-by__company {
    padding: 16px;
    min-width: 80px;
  }

  .trusted-by__company-logo {
    width: 72px;
    height: 72px;
  }

  .trusted-by__company-name {
    font-size: 12px;
  }
}
