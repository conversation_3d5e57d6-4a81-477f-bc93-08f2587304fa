import { getFeatureImage } from '../utils/featureIMG';
import './FeatureTestimonial.css';

const FeatureTestimonial = ({ testimonial, imageKey = 'maria' }) => {
  if (!testimonial || !testimonial.quote) {
    console.warn('FeatureTestimonial: No testimonial data provided');
    return null;
  }

  return (
    <section className="feature-testimonial">
      <div className="feature-testimonial__container">
        <div className="feature-testimonial__content">
          {/* Text Content */}
          <div className="feature-testimonial__text">
            <div className="feature-testimonial__quote-icon">
              <svg viewBox="0 0 24 24" fill="none" className="feature-testimonial__quote-svg">
                <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z" fill="#006ac1"/>
              </svg>
            </div>

            <blockquote className="feature-testimonial__quote">
              "{testimonial.quote}"
            </blockquote>

            <div className="feature-testimonial__author">
              <div className="feature-testimonial__author-info">
                <div className="feature-testimonial__author-name">
                  {testimonial.author}
                </div>
                <div className="feature-testimonial__author-title">
                  {testimonial.title}
                </div>
                <div className="feature-testimonial__author-location">
                  {testimonial.location}
                </div>
              </div>
            </div>
          </div>

          {/* Image */}
          <div className="feature-testimonial__image">
            <div className="feature-testimonial__image-container">
              <img
                src={(() => {
                  try {
                    return getFeatureImage('testimonials', 'clientPhotos', imageKey).src;
                  } catch (error) {
                    console.warn('FeatureTestimonial: Error loading image for', imageKey, error);
                    return '/images/placeholder.png';
                  }
                })()}
                alt={(() => {
                  try {
                    return getFeatureImage('testimonials', 'clientPhotos', imageKey).alt;
                  } catch (error) {
                    return `${testimonial.author || 'Client'} testimonial photo`;
                  }
                })()}
                className="feature-testimonial__img"
                onError={(e) => {
                  e.target.src = '/images/placeholder.png';
                }}
              />
              <div className="feature-testimonial__image-decoration"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeatureTestimonial;
