@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap');

/* Why Telescope Section - Clean & Minimal */
.why-telescope {
  position: relative;
  padding: 120px 0;
  background: #ffffff;
  font-family: "Plus Jakarta Sans", sans-serif;
  overflow: hidden;
}

.why-telescope__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.why-telescope__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease-out;
}

.why-telescope--visible .why-telescope__content {
  opacity: 1;
  transform: translateY(0);
}

/* Text Content */
.why-telescope__text {
  text-align: left;
}

/* Label */
.why-telescope__label {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: linear-gradient(135deg, rgba(0, 106, 193, 0.1) 0%, rgba(0, 106, 193, 0.05) 100%);
  color: #006ac1;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(0, 106, 193, 0.1);
}

/* Headline */
.why-telescope__headline {
  font-size: clamp(2.5rem, 5vw, 3.5rem);
  font-weight: 600;
  color: #201c44;
  line-height: 1.2;
  margin: 0 0 32px 0;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #201c44 0%, #2d2654 50%, #3a3366 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Body Text */
.why-telescope__body {
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
  font-weight: 400;
  letter-spacing: -0.005em;
  max-width: 500px;
}

/* Image Section */
.why-telescope__image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.why-telescope__image-container {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 106, 193, 0.1);
  background: white;
  transition: all 0.3s ease;
  max-width: 500px;
  width: 100%;
}

.why-telescope__image-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 106, 193, 0.15);
}

/* Mac-style Header */
.why-telescope__mac-header {
  position: relative;
  background: linear-gradient(180deg, #f6f6f6 0%, #e8e8e8 100%);
  border-bottom: 1px solid #d1d1d1;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-radius: 20px 20px 0 0;
}

.why-telescope__mac-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.why-telescope__mac-button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 0.5px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.why-telescope__mac-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.why-telescope__mac-button--close {
  background: linear-gradient(135deg, #ff5f57 0%, #ff4444 100%);
}

.why-telescope__mac-button--close:hover::before {
  content: '×';
  color: #8b0000;
  font-size: 10px;
  font-weight: bold;
  opacity: 1;
}

.why-telescope__mac-button--minimize {
  background: linear-gradient(135deg, #ffbd2e 0%, #ffaa00 100%);
}

.why-telescope__mac-button--minimize:hover::before {
  content: '−';
  color: #8b4500;
  font-size: 10px;
  font-weight: bold;
  opacity: 1;
}

.why-telescope__mac-button--maximize {
  background: linear-gradient(135deg, #28ca42 0%, #20a034 100%);
}

.why-telescope__mac-button--maximize:hover::before {
  content: '+';
  color: #0d4f1c;
  font-size: 10px;
  font-weight: bold;
  opacity: 1;
}

.why-telescope__mac-button:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.why-telescope__main-image {
  width: 100%;
  height: auto;
  object-fit: contain;
  border-radius: 0 0 16px 16px;
  transition: all 0.3s ease;
  display: block;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .why-telescope__content {
    gap: 60px;
  }
}

@media (max-width: 768px) {
  .why-telescope {
    padding: 80px 0;
  }

  .why-telescope__container {
    padding: 0 16px;
  }

  .why-telescope__content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .why-telescope__text {
    text-align: center;
  }

  .why-telescope__body {
    max-width: 600px;
    margin: 0 auto;
  }

  .why-telescope__label {
    font-size: 13px;
    padding: 6px 14px;
    margin-bottom: 20px;
  }

  .why-telescope__headline {
    margin-bottom: 24px;
  }
}

@media (max-width: 480px) {
  .why-telescope {
    padding: 60px 0;
  }

  .why-telescope__container {
    padding: 0 12px;
  }

  .why-telescope__content {
    gap: 32px;
  }

  .why-telescope__label {
    font-size: 12px;
    padding: 6px 12px;
    margin-bottom: 16px;
  }

  .why-telescope__headline {
    margin-bottom: 20px;
  }

  .why-telescope__image-container {
    border-radius: 16px;
  }

  .why-telescope__mac-header {
    padding: 10px 12px;
    border-radius: 16px 16px 0 0;
  }

  .why-telescope__mac-button {
    width: 10px;
    height: 10px;
  }

  .why-telescope__mac-buttons {
    gap: 6px;
  }

  .why-telescope__main-image {
    border-radius: 0 0 12px 12px;
  }
}
