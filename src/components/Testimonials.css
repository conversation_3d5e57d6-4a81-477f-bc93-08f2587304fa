@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap');

/* Testimonials Section */
.testimonials {
  padding: 120px 0;
  background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
  font-family: "Plus Jakarta Sans", sans-serif;
  position: relative;
}

.testimonials__container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

.testimonials__content {
  border-radius: 32px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.1), 0 10px 20px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
  position: relative;
  display: grid;
  grid-template-columns: 1fr 1fr;
  min-height: 600px;
}

/* Diagonal Split Background */
.testimonials__content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    #f8faff 0%,
    #f8faff 49.5%,
    #006ac1 49.5%,
    #006ac1 50.5%,
    white 50.5%,
    white 100%
  );
  z-index: 1;
}

/* Left Side: Header Section */
.testimonials__header {
  display: flex;
  flex-direction: column;
  gap: 3rem;
  padding: 4rem;
  position: relative;
  z-index: 2;
  height: 100%;
  justify-content: center;
  background: transparent;
}

.testimonials__header-content {
  padding: 0;
}

.testimonials__title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 400;
  color: #1a1a1a;
  margin: 0 0 1rem 0;
  line-height: 1.2;
  letter-spacing: -0.02em;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease-out;
}

.testimonials--visible .testimonials__title {
  opacity: 1;
  transform: translateY(0);
}

.testimonials__subtitle {
  font-size: 1.25rem;
  color: #4b5563;
  margin: 0;
  line-height: 1.6;
}

.testimonials__header-image {
  height: 280px;
  border-radius: 20px;
  overflow: hidden;
}

.testimonials__main-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
  object-position: top;
}

.testimonials__main-image:hover {
  transform: scale(1.05);
}

/* Right Side: Testimonial Carousel */
.testimonials__carousel {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4rem;
  position: relative;
  z-index: 2;
  height: 100%;
  background: transparent;
  opacity: 0;
  transform: translateX(30px);
  transition: all 0.8s ease-out 0.3s;
}

.testimonials--visible .testimonials__carousel {
  opacity: 1;
  transform: translateX(0);
}

.testimonials__carousel-container {
  background: transparent;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
  border: none;
  max-width: 100%;
  width: 100%;
  text-align: center;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

/* Carousel Content Transitions */
.testimonials__carousel-container {
  transition: all 0.3s ease;
}

.testimonials__quote-icon {
  width: 48px;
  height: 48px;
  color: #006ac1;
  margin: 0 auto 32px;
  opacity: 0.8;
}

.testimonials__quote-icon svg {
  width: 100%;
  height: 100%;
}

.testimonials__review {
  margin-bottom: 40px;
}

.testimonials__quote {
  font-size: 1.5rem;
  color: #1a1a1a;
  line-height: 1.6;
  margin: 0;
  font-weight: 400;
  font-style: italic;
}

.testimonials__client {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;
}

.testimonials__client-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.testimonials__client-image {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  overflow: hidden;
}

.testimonials__client-avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.testimonials__client-details {
  text-align: left;
}

.testimonials__client-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 4px 0;
}

.testimonials__client-title {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0 0 2px 0;
}

.testimonials__company-name {
  font-size: 0.875rem;
  color: #006ac1;
  margin: 0;
  font-weight: 500;
}

.testimonials__company-logo {
  font-size: 2.5rem;
  opacity: 0.8;
}

/* Navigation */
.testimonials__navigation {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 24px;
}

.testimonials__nav-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 1px solid rgba(0, 106, 193, 0.2);
  background: white;
  color: #006ac1;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: scale(1);
}

.testimonials__nav-btn:hover {
  background: #006ac1;
  color: white;
  border-color: #006ac1;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 106, 193, 0.3);
}

.testimonials__nav-btn:active {
  transform: scale(0.95);
}

.testimonials__nav-btn svg {
  width: 20px;
  height: 20px;
}

.testimonials__dots {
  display: flex;
  gap: 8px;
}

.testimonials__dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: rgba(32, 28, 68, 0.2);
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: scale(1);
}

.testimonials__dot:hover {
  background: rgba(0, 106, 193, 0.4);
  transform: scale(1.2);
}

.testimonials__dot--active {
  background: #006ac1;
  transform: scale(1.3);
  box-shadow: 0 2px 8px rgba(0, 106, 193, 0.4);
}

/* Visibility Animation */
.testimonials {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.testimonials--visible {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .testimonials {
    padding: 80px 0;
  }

  .testimonials__container {
    padding: 0 16px;
  }

  .testimonials__content {
    grid-template-columns: 1fr;
    min-height: auto;
    border-radius: 24px;
  }

  .testimonials__content::before {
    background: linear-gradient(
      180deg,
      #f8faff 0%,
      #f8faff 49.5%,
      #006ac1 49.5%,
      #006ac1 50.5%,
      white 50.5%,
      white 100%
    );
  }

  .testimonials__header {
    gap: 2rem;
    text-align: center;
    padding: 3rem 2rem 2rem;
  }

  .testimonials__header-image {
    height: 200px;
  }

  .testimonials__carousel {
    padding: 2rem 2rem 3rem;
  }

  .testimonials__quote {
    font-size: 1.25rem;
  }

  .testimonials__client {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .testimonials__client-details {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .testimonials {
    padding: 60px 0;
  }

  .testimonials__content {
    border-radius: 20px;
  }

  .testimonials__header {
    gap: 1.5rem;
    padding: 2rem 1.5rem 1.5rem;
  }

  .testimonials__header-image {
    height: 180px;
  }

  .testimonials__carousel {
    padding: 1.5rem 1.5rem 2rem;
  }

  .testimonials__quote {
    font-size: 1.125rem;
  }

  .testimonials__navigation {
    gap: 16px;
  }

  .testimonials__nav-btn {
    width: 36px;
    height: 36px;
  }
}
