import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import './Hero.css';


const Hero = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  return (
    <section className="hero">
      {/* Minimal Background Elements */}
      <div className="hero__bg-elements">
        <div className="hero__bg-circle hero__bg-circle--1"></div>
        <div className="hero__bg-circle hero__bg-circle--2"></div>
        <div className="hero__bg-gradient"></div>
      </div>

      <div className="hero__container">
        {/* Hero Content */}
        <div className={`hero__content ${isLoaded ? 'hero__content--loaded' : ''}`}>


          <div className="hero__headline-wrapper">
            <h1 className="hero__headline">
              <span className="hero__headline-word hero__headline-word--1">The</span>
              <span className="hero__headline-word hero__headline-word--2">everything</span>
              <span className="hero__headline-word hero__headline-word--3">app</span>
              <span className="hero__headline-word hero__headline-word--4">for</span>
              <span className="hero__headline-word hero__headline-word--5">home</span>
              <span className="hero__headline-word hero__headline-word--6">care</span>
            </h1>
            <div className="hero__headline-underline"></div>

          </div>

          <div className="hero__subheadline">
            <p className="hero__tagline">
              <span className="hero__tagline-part hero__tagline-part--1">1. Hire more caregivers.</span>
              <span className="hero__tagline-part hero__tagline-part--2">Win more clients.</span>
              <span className="hero__tagline-part hero__tagline-part--3">Save more time.</span>
            </p>
            <p className="hero__description">
              Run your agency with ease — all from one powerful platform.
            </p>
          </div>

          <div className="hero__cta">
            <button
              className="hero__cta-btn"
              onClick={() => navigate('/contact')}
            >
              <span className="hero__cta-text">2. Start 14 day trial</span>
              <div className="hero__cta-icon-wrapper">
                <svg className="hero__cta-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="hero__cta-ripple"></div>
            </button>


          </div>
        </div>

        {/* Enhanced Software Image */}
        <div className={`hero__image ${isLoaded ? 'hero__image--loaded' : ''}`}>
          <div className="hero__image-container">
            {/* Mac-style Header */}
            <div className="hero__mac-header">
              <div className="hero__mac-buttons">
                <div className="hero__mac-button hero__mac-button--close"></div>
                <div className="hero__mac-button hero__mac-button--minimize"></div>
                <div className="hero__mac-button hero__mac-button--maximize"></div>
              </div>
            </div>

            <div className="hero__image-glow"></div>
            <div className="hero__image-reflection"></div>
            <img
              src={'/images/app.svg'}
              alt="Home Care Management Software Dashboard"
              className="hero__software-img"
              loading="eager"
            />
            <div className="hero__image-overlay"></div>
            <div className="hero__image-border"></div>




          </div>
        </div>
      </div>


    </section>
  );
};

export default Hero;
