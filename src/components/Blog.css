/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

/* Blog Page */
.blog {
  font-family: "Plus Jakarta Sans", sans-serif;
}

/* Hero Section */
.blog-hero {
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
  padding: 120px 0 80px 0;
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.blog-hero--visible {
  opacity: 1;
  transform: translateY(0);
}

.blog-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(0,106,193,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.5;
}

.blog-hero__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 1;
}

.blog-hero__content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.blog-hero__title {
  font-size: clamp(2.5rem, 4vw, 3.5rem);
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 24px 0;
  letter-spacing: -0.02em;
  line-height: 1.1;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s;
}

.blog-hero--visible .blog-hero__title {
  opacity: 1;
  transform: translateY(0);
}

.blog-hero__subtitle {
  font-size: 1.25rem;
  color: #6b7280;
  margin: 0 0 48px 0;
  line-height: 1.6;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
}

.blog-hero--visible .blog-hero__subtitle {
  opacity: 1;
  transform: translateY(0);
}

.blog-hero__stats {
  display: flex;
  justify-content: center;
  gap: 48px;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.6s;
}

.blog-hero--visible .blog-hero__stats {
  opacity: 1;
  transform: translateY(0);
}

.blog-hero__stat {
  text-align: center;
}

.blog-hero__stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 700;
  color: #006ac1;
  line-height: 1;
  margin-bottom: 8px;
}

.blog-hero__stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

/* Content Section */
.blog-content {
  padding: 100px 0;
  background: white;
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);
}

.blog-content--visible {
  opacity: 1;
  transform: translateY(0);
}

.blog-content__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Featured Posts Section */
.blog-featured {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 48px;
  margin-bottom: 80px;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s;
}

.blog-content--visible .blog-featured {
  opacity: 1;
  transform: translateY(0);
}

.blog-featured__post {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 106, 193, 0.08);
  border: 1px solid rgba(0, 106, 193, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.blog-featured__post:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 106, 193, 0.15);
}

.blog-featured__image {
  width: 100%;
  height: 300px;
  overflow: hidden;
}

.blog-featured__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.blog-featured__post:hover .blog-featured__image img {
  transform: scale(1.05);
}

.blog-featured__content {
  padding: 32px;
}

.blog-featured__title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 16px 0;
  line-height: 1.4;
}

.blog-featured__description {
  font-size: 1rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0 0 24px 0;
}

.blog-featured__meta {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 0.875rem;
  color: #9ca3af;
}

.blog-featured__author {
  font-weight: 500;
}

/* Featured Sidebar */
.blog-featured__sidebar {
  background: #f8faff;
  border-radius: 16px;
  padding: 32px;
  border: 1px solid rgba(0, 106, 193, 0.1);
}

.blog-featured__sidebar-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 24px 0;
}

.blog-featured__sidebar-post {
  cursor: pointer;
  padding: 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.blog-featured__sidebar-post:hover {
  background: rgba(0, 106, 193, 0.06);
}

.blog-featured__sidebar-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.blog-featured__sidebar-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.875rem;
  color: #9ca3af;
}

/* Blog Sections */
.blog-section {
  margin-bottom: 80px;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
}

.blog-content--visible .blog-section {
  opacity: 1;
  transform: translateY(0);
}

.blog-section__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.blog-section__title {
  font-size: 2rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.blog-section__see-more {
  color: #006ac1;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: color 0.3s ease;
}

.blog-section__see-more:hover {
  color: #1a7dd1;
}

.blog-section__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
}

.blog-section__grid--single {
  grid-template-columns: repeat(auto-fit, minmax(350px, 400px));
  justify-content: start;
}

/* Blog Cards */
.blog-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 106, 193, 0.08);
  border: 1px solid rgba(0, 106, 193, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease forwards;
}

.blog-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 106, 193, 0.15);
}

.blog-card__image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.blog-card__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.blog-card:hover .blog-card__image img {
  transform: scale(1.05);
}

.blog-card__content {
  padding: 24px;
}

.blog-card__title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 12px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.blog-card__description {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.5;
  margin: 0 0 16px 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.blog-card__meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.75rem;
  color: #9ca3af;
}

.blog-card__author {
  font-weight: 500;
}

/* CTA Section */
.blog-cta {
  background: linear-gradient(135deg, #006ac1 0%, #1a7dd1 100%);
  border-radius: 24px;
  padding: 60px 40px;
  text-align: center;
  color: white;
  margin-top: 80px;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.6s;
}

.blog-content--visible .blog-cta {
  opacity: 1;
  transform: translateY(0);
}

.blog-cta__title {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 16px 0;
  letter-spacing: -0.02em;
}

.blog-cta__description {
  font-size: 1.125rem;
  margin: 0 0 32px 0;
  line-height: 1.6;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.blog-cta__buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
}

.blog-cta__button {
  padding: 14px 28px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.blog-cta__button--primary {
  background: white;
  color: #006ac1;
}

.blog-cta__button--primary:hover {
  background: #f8faff;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.blog-cta__button--secondary {
  background: transparent;
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

.blog-cta__button--secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: white;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .blog-hero {
    padding: 100px 0 60px 0;
  }

  .blog-hero__stats {
    gap: 32px;
  }

  .blog-hero__stat-number {
    font-size: 2rem;
  }

  .blog-content {
    padding: 80px 0;
  }

  .blog-featured {
    grid-template-columns: 1fr;
    gap: 32px;
  }

  .blog-section__header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .blog-section__title {
    font-size: 1.75rem;
  }

  .blog-section__grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .blog-featured__image {
    height: 250px;
  }

  .blog-card__image {
    height: 180px;
  }

  .blog-cta {
    padding: 40px 24px;
    margin-top: 60px;
  }

  .blog-cta__title {
    font-size: 1.75rem;
  }

  .blog-cta__buttons {
    flex-direction: column;
    align-items: center;
  }

  .blog-cta__button {
    width: 100%;
    max-width: 280px;
  }
}

@media (max-width: 480px) {
  .blog-hero {
    padding: 80px 0 40px 0;
  }

  .blog-hero__container,
  .blog-content__container {
    padding: 0 16px;
  }

  .blog-content {
    padding: 60px 0;
  }

  .blog-hero__stats {
    flex-direction: column;
    gap: 24px;
  }

  .blog-featured__content {
    padding: 24px;
  }

  .blog-featured__sidebar {
    padding: 24px;
  }

  .blog-card__content {
    padding: 20px;
  }

  .blog-section__title {
    font-size: 1.5rem;
  }

  .blog-featured__title {
    font-size: 1.25rem;
  }

  .blog-cta {
    padding: 32px 20px;
    margin-top: 40px;
  }

  .blog-cta__title {
    font-size: 1.5rem;
  }

  .blog-cta__description {
    font-size: 1rem;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
