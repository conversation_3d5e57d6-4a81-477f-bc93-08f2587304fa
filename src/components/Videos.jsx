import { useState, useEffect, useRef } from 'react';
import './Videos.css';

const Videos = () => {
  // Animation states
  const [isHeroVisible, setIsHeroVisible] = useState(false);
  const [isContentVisible, setIsContentVisible] = useState(false);

  // Expanded descriptions state
  const [expandedDescriptions, setExpandedDescriptions] = useState({});

  // Refs for intersection observer
  const heroRef = useRef(null);

  // Initial animations on page load
  useEffect(() => {
    // Hero animation
    setTimeout(() => setIsHeroVisible(true), 100);

    // Content animation
    setTimeout(() => setIsContentVisible(true), 600);
  }, []);

  const videos = [
    {
      id: 1,
      title: "As a Home Care Agency can you Hire Caregivers as 1099 Contractors?",
      description: "In this video, <PERSON>hae covers the ins and outs of why you'll want to pay your caregivers as W-2 employees as opposed to hiring them on as 1099 Contractors. This video also covers the effects of miscl...",
      youtubeId: "6NgwIZ9tnI4",
      channel: "123 Consulting Solutions",
      category: "HR & Compliance",
      duration: "12:45",
      views: "2.3K",
      publishedDate: "2 weeks ago"
    },
    {
      id: 2,
      title: "How to Start a PAS Home Care Agency in Texas",
      description: "If you have a dream of starting your own PAS company, this video is for you. In this video, Shae from 123 Consulting Solutions covers the 13 steps that anyone who wants to start a PAS Agency in Texas can easily follow to find success.",
      youtubeId: "8UGGsQIW914",
      channel: "123 Consulting Solutions",
      category: "Business Setup",
      duration: "45:30",
      views: "5.2K",
      publishedDate: "1 month ago"
    },
    {
      id: 3,
      title: "16 Quick and Easy Tips for Recruiting Caregivers",
      description: "The topics of the day is 16 Quick and Easy Tips for Recruiting Caregivers. Agencies have all been through it the past few years with difficulty hiring and keeping good caregivers. Don't fret. Here are some tips that are sure to help you with the hiring process.",
      youtubeId: "qJ1yCWhqF9I",
      channel: "123 Consulting Solutions",
      category: "Recruitment",
      duration: "18:45",
      views: "3.8K",
      publishedDate: "3 weeks ago"
    },
    {
      id: 4,
      title: "How to Get your First Private Pay Home Care Client in less than 30 Days",
      description: "This video covers everything you need to do to get your first private pay client as a home care agency owner. 123 Consulting Solutions owner, Shae, covers all the steps you need to follow from who to talk to, how to talk to them, what rates to quote, and how to keep up with leads to convert them into paying clients.",
      youtubeId: "bm0rtNxag-g",
      channel: "123 Consulting Solutions",
      category: "Marketing & Sales",
      duration: "22:15",
      views: "4.1K",
      publishedDate: "2 months ago"
    }
  ];

  const categories = ["All", "HR & Compliance", "Business Setup", "Recruitment", "Marketing & Sales"];

  const [selectedCategory, setSelectedCategory] = useState("All");
  const [filteredVideos, setFilteredVideos] = useState(videos);

  useEffect(() => {
    if (selectedCategory === "All") {
      setFilteredVideos(videos);
    } else {
      setFilteredVideos(videos.filter(video => video.category === selectedCategory));
    }
  }, [selectedCategory]);

  const getYouTubeThumbnail = (videoId) => {
    return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
  };

  const openVideo = (youtubeId) => {
    window.open(`https://www.youtube.com/watch?v=${youtubeId}`, '_blank');
  };

  const toggleDescription = (videoId, e) => {
    e.stopPropagation(); // Prevent opening video when clicking eye icon
    setExpandedDescriptions(prev => ({
      ...prev,
      [videoId]: !prev[videoId]
    }));
  };

  return (
    <div className="videos">
      {/* Hero Section */}
      <section className={`videos-hero ${isHeroVisible ? 'videos-hero--visible' : ''}`} ref={heroRef}>
        <div className="videos-hero__container">
          <div className="videos-hero__content">
            <h1 className="videos-hero__title">Educational Videos</h1>
            <p className="videos-hero__subtitle">
              Learn from industry experts with our comprehensive video library covering
              agency startup, compliance, recruitment, marketing, and business growth strategies.
            </p>
            <div className="videos-hero__stats">
              <div className="videos-hero__stat">
                <span className="videos-hero__stat-number">4</span>
                <span className="videos-hero__stat-label">Expert Videos</span>
              </div>
              <div className="videos-hero__stat">
                <span className="videos-hero__stat-number">15K+</span>
                <span className="videos-hero__stat-label">Views</span>
              </div>
              <div className="videos-hero__stat">
                <span className="videos-hero__stat-number">4</span>
                <span className="videos-hero__stat-label">Categories</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Content Section */}
      <section className={`videos-content ${isContentVisible ? 'videos-content--visible' : ''}`}>
        <div className="videos-content__container">
          {/* Category Filter */}
          <div className="videos-filter">
            <h3 className="videos-filter__title">Filter by Category</h3>
            <div className="videos-filter__buttons">
              {categories.map((category) => (
                <button
                  key={category}
                  className={`videos-filter__button ${selectedCategory === category ? 'videos-filter__button--active' : ''}`}
                  onClick={() => setSelectedCategory(category)}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>

          {/* Videos Grid */}
          <div className="videos-grid">
            {filteredVideos.map((video, index) => (
              <div
                key={video.id}
                className="video-card"
                onClick={() => openVideo(video.youtubeId)}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="video-card__thumbnail">
                  <img
                    src={getYouTubeThumbnail(video.youtubeId)}
                    alt={video.title}
                    className="video-card__image"
                  />
                  <div className="video-card__overlay">
                    <div className="video-card__play-button">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M8 5v14l11-7z" fill="currentColor"/>
                      </svg>
                    </div>
                  </div>
                  <div className="video-card__duration">{video.duration}</div>
                </div>

                <div className="video-card__content">
                  <div className="video-card__category">{video.category}</div>
                  <div className="video-card__title-row">
                    <h3 className="video-card__title">{video.title}</h3>
                    <button
                      className="video-card__expand-btn"
                      onClick={(e) => toggleDescription(video.id, e)}
                      title={expandedDescriptions[video.id] ? "Show less" : "Show full description"}
                    >
                      <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" strokeWidth="2"/>
                        <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="2"/>
                      </svg>
                    </button>
                  </div>
                  <p className={`video-card__description ${expandedDescriptions[video.id] ? 'video-card__description--expanded' : ''}`}>
                    {video.description}
                  </p>

                  <div className="video-card__meta">
                    <div className="video-card__channel">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                        <path d="M8 14s1.5 2 4 2 4-2 4-2" stroke="currentColor" strokeWidth="2"/>
                        <path d="M9 9h.01" stroke="currentColor" strokeWidth="2"/>
                        <path d="M15 9h.01" stroke="currentColor" strokeWidth="2"/>
                      </svg>
                      {video.channel}
                    </div>
                    <div className="video-card__stats">
                      <span>{video.views} views</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Call to Action */}
          <div className="videos-cta">
            <div className="videos-cta__content">
              <h2 className="videos-cta__title">Want More Expert Content?</h2>
              <p className="videos-cta__description">
                Subscribe to our YouTube channel for the latest videos on home care agency management,
                compliance updates, and industry best practices.
              </p>
              <div className="videos-cta__buttons">
                <button
                  className="videos-cta__button videos-cta__button--primary"
                  onClick={() => window.open('https://www.youtube.com/@123consultingsolutions', '_blank')}
                >
                  Subscribe to Channel
                </button>
                <button
                  className="videos-cta__button videos-cta__button--secondary"
                  onClick={() => window.open('/contact', '_self')}
                >
                  Request Custom Training
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Videos;
