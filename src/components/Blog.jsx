import { useState, useEffect } from 'react';
import './Blog.css';

const Blog = () => {
  // Animation states
  const [isHeroVisible, setIsHeroVisible] = useState(false);
  const [isContentVisible, setIsContentVisible] = useState(false);

  // Initial animations on page load
  useEffect(() => {
    // Hero animation
    setTimeout(() => setIsHeroVisible(true), 100);

    // Content animation
    setTimeout(() => setIsContentVisible(true), 600);
  }, []);

  const featuredPost = {
    title: "How to Design A Client Referral Program for Your Home Care Agency",
    description: "The key to growing your business is to maximize your referral sources. An underutilized referral source is word-of-mouth referrals f...",
    image: "https://telescopehr.s3.us-east-2.amazonaws.com/live/images/blog/1696962548_1693997052_How%20to%20Design%20A%20Client%20Referral%20Program%20for%20Your%20Home%20Care%20Agency.jpg",
    author: "<PERSON>gun <PERSON>",
    date: "10/10/2023",
    url: "https://telescopehr.com/blog/how-to-design-a-client-referral-program-for-your-home-care-agency"
  };

  const featuredSidePost = {
    title: "PAYCHECK PROTECTION PROGRAM (PPP) INFORMATION SHEET: BORROWERS",
    author: "Phoebe Gregory",
    date: "09/14/2022",
    url: "https://telescopehr.com/blog/paycheck-protection-program-ppp-information-sheet-borrowers"
  };

  const caregiverRecruitmentPosts = [
    {
      title: "13 Smart Interview Questions to Ask Millennial and Gen Z Caregivers Today",
      description: "Hiring caregivers has never been easy. But the expectations of today's workforce make it even more i...",
      image: "https://telescopehr.s3.us-east-2.amazonaws.com/live/images/blog/1747006397_Smart%20Interview%20Questions.png",
      author: "Segun Ogungbemi",
      date: "04/28/2025",
      url: "https://telescopehr.com/blog/13-smart-interview-questions-to-ask-millennial-and-gen-z-caregivers-today"
    },
    {
      title: "Top 15 Experts Approved Caregiver Recruitment Strategies",
      description: "If you're running a home care agency, you've probably faced the challenge of caregiver r...",
      image: "https://telescopehr.s3.us-east-2.amazonaws.com/live/images/blog/1744027516_Caregiver%20Recruitment%20Strategies.png",
      author: "Segun Ogungbemi",
      date: "03/31/2025",
      url: "https://telescopehr.com/blog/top-15-experts-approved-caregiver-recruitment-strategies"
    },
    {
      title: "10 Proven Strategies to Recruit and Retain Best Caregivers in Home-Based Care",
      description: "High caregiver turnover is one of the biggest challenges facing home-based care agencies today. With...",
      image: "https://telescopehr.s3.us-east-2.amazonaws.com/live/images/blog/1744024732_Proven%20Strategies.png",
      author: "Segun Ogungbemi",
      date: "03/29/2025",
      url: "https://telescopehr.com/blog/10-proven-strategies-to-recruit-and-retain-best-caregivers-in-home-based-care"
    },
    {
      title: "Why Automating Your Recruitment Process is a Game-Changer for Home Care Agencies",
      description: "Home care agencies face unique challenges when it comes to the recruitment of the right caregivers....",
      image: "https://telescopehr.s3.us-east-2.amazonaws.com/live/images/blog/1728585480_businessman-using-application-contact-with-his-colleagues.jpg",
      author: "Segun Ogungbemi",
      date: "10/10/2024",
      url: "https://telescopehr.com/blog/why-automating-your-recruitment-process-is-a-game-changer-for-home-care-agencies"
    }
  ];

  const scaleAgencyPosts = [
    {
      title: "Losing Caregivers to Private Clients? Here's What Your Agency Can Do About It",
      description: "You finally hire a dependable caregiver. They show up, do excellent work, and earn trust with client...",
      image: "https://telescopehr.s3.us-east-2.amazonaws.com/live/images/blog/1747009431_%20Caregivers%20to%20Private%20Clients.png",
      author: "Segun Ogungbemi",
      date: "04/30/2025",
      url: "https://telescopehr.com/blog/losing-caregivers-to-private-clients-heres-what-your-agency-can-do-about-it"
    },
    {
      title: "Caregiver Burnout Quiz: Find Out If You're at Risk and What to Do Next",
      description: "Caring for others can be incredibly meaningful. But it can also become exhausting, emotionally drain...",
      image: "https://telescopehr.s3.us-east-2.amazonaws.com/live/images/blog/1747008187_Caregiver%20Burnout%20Quiz.png",
      author: "Segun Ogungbemi",
      date: "04/29/2025",
      url: "https://telescopehr.com/blog/caregiver-burnout-quiz-find-out-if-youre-at-risk-and-what-to-do-next"
    },
    {
      title: "How to Start A PAS Agency in Texas 2025",
      description: "Thinking about launching your own PAS agency in Texas? You're in the right place. This guide will wa...",
      image: "https://telescopehr.s3.us-east-2.amazonaws.com/live/images/blog/1745199269_T%20HTS%20PAS.png",
      author: "Segun Ogungbemi",
      date: "04/20/2025",
      url: "https://telescopehr.com/blog/how-to-start-a-pas-agency-in-texas-2025"
    },
    {
      title: "Professional Boundaries for Caregivers: How to Build Trust & Prevent Burnout",
      description: "Caregiving isn't just about assisting with tasks. It's about building trust and creating...",
      image: "https://telescopehr.s3.us-east-2.amazonaws.com/live/images/blog/1744026039_Professional%20Boundaries.png",
      author: "Segun Ogungbemi",
      date: "03/29/2025",
      url: "https://telescopehr.com/blog/professional-boundaries-for-caregivers-how-to-build-trust-prevent-burnout"
    }
  ];

  const salesMarketingPosts = [
    {
      title: "How Much Should You Pay a Marketer?",
      description: "Many home care agency owners are unsure how to structure the salary, commission, or bonuses of marke...",
      image: "https://telescopehr.s3.us-east-2.amazonaws.com/live/images/blog/1700000601_Home%20care%20marketer%20salary.jpg",
      date: "11/14/2023",
      url: "https://telescopehr.com/blog/how-much-should-you-pay-a-marketer"
    }
  ];

  const openExternalLink = (url) => {
    window.open(url, '_blank');
  };

  return (
    <div className="blog">
      {/* Hero Section */}
      <section className={`blog-hero ${isHeroVisible ? 'blog-hero--visible' : ''}`}>
        <div className="blog-hero__container">
          <div className="blog-hero__content">
            <h1 className="blog-hero__title">Expert Insights & Resources</h1>
            <p className="blog-hero__subtitle">
              Stay ahead with the latest strategies, tips, and industry insights for
              home care agency success and growth.
            </p>
            <div className="blog-hero__stats">
              <div className="blog-hero__stat">
                <span className="blog-hero__stat-number">50+</span>
                <span className="blog-hero__stat-label">Expert Articles</span>
              </div>
              <div className="blog-hero__stat">
                <span className="blog-hero__stat-number">3</span>
                <span className="blog-hero__stat-label">Categories</span>
              </div>
              <div className="blog-hero__stat">
                <span className="blog-hero__stat-number">Weekly</span>
                <span className="blog-hero__stat-label">Updates</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Content Section */}
      <section className={`blog-content ${isContentVisible ? 'blog-content--visible' : ''}`}>
        <div className="blog-content__container">
          {/* Featured Posts Section */}
          <div className="blog-featured">
            <div className="blog-featured__main">
              <div className="blog-featured__post" onClick={() => openExternalLink(featuredPost.url)}>
                <div className="blog-featured__image">
                  <img src={featuredPost.image} alt={featuredPost.title} />
                </div>
                <div className="blog-featured__content">
                  <h3 className="blog-featured__title">{featuredPost.title}</h3>
                  <p className="blog-featured__description">{featuredPost.description}</p>
                  <div className="blog-featured__meta">
                    <span className="blog-featured__author">{featuredPost.author}</span>
                    <span className="blog-featured__date">{featuredPost.date}</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="blog-featured__sidebar">
              <div className="blog-featured__sidebar-header">
                <h2>Featured Posts</h2>
              </div>
              <div className="blog-featured__sidebar-post" onClick={() => openExternalLink(featuredSidePost.url)}>
                <h3 className="blog-featured__sidebar-title">{featuredSidePost.title}</h3>
                <div className="blog-featured__sidebar-meta">
                  <span className="blog-featured__sidebar-author">{featuredSidePost.author}</span>
                  <span className="blog-featured__sidebar-date">{featuredSidePost.date}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Caregiver Recruitment Section */}
          <div className="blog-section">
            <div className="blog-section__header">
              <h2 className="blog-section__title">Caregiver Recruitment and Retention</h2>
              <a
                href="https://telescopehr.com/blog-category/61"
                className="blog-section__see-more"
                target="_blank"
                rel="noopener noreferrer"
              >
                See more caregiver recruitment and retention articles
              </a>
            </div>
            <div className="blog-section__grid">
              {caregiverRecruitmentPosts.map((post, index) => (
                <div
                  key={index}
                  className="blog-card"
                  onClick={() => openExternalLink(post.url)}
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="blog-card__image">
                    <img src={post.image} alt={post.title} />
                  </div>
                  <div className="blog-card__content">
                    <h3 className="blog-card__title">{post.title}</h3>
                    <p className="blog-card__description">{post.description}</p>
                    <div className="blog-card__meta">
                      <span className="blog-card__author">{post.author}</span>
                      <span className="blog-card__date">{post.date}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Scale Your Agency Section */}
          <div className="blog-section">
            <div className="blog-section__header">
              <h2 className="blog-section__title">Scale Your Agency</h2>
              <a
                href="https://telescopehr.com/blog-category/63"
                className="blog-section__see-more"
                target="_blank"
                rel="noopener noreferrer"
              >
                See more scale your agency articles
              </a>
            </div>
            <div className="blog-section__grid">
              {scaleAgencyPosts.map((post, index) => (
                <div
                  key={index}
                  className="blog-card"
                  onClick={() => openExternalLink(post.url)}
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="blog-card__image">
                    <img src={post.image} alt={post.title} />
                  </div>
                  <div className="blog-card__content">
                    <h3 className="blog-card__title">{post.title}</h3>
                    <p className="blog-card__description">{post.description}</p>
                    <div className="blog-card__meta">
                      <span className="blog-card__author">{post.author}</span>
                      <span className="blog-card__date">{post.date}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Sales and Marketing Section */}
          <div className="blog-section">
            <div className="blog-section__header">
              <h2 className="blog-section__title">Sales and Marketing</h2>
            </div>
            <div className="blog-section__grid blog-section__grid--single">
              {salesMarketingPosts.map((post, index) => (
                <div
                  key={index}
                  className="blog-card"
                  onClick={() => openExternalLink(post.url)}
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="blog-card__image">
                    <img src={post.image} alt={post.title} />
                  </div>
                  <div className="blog-card__content">
                    <h3 className="blog-card__title">{post.title}</h3>
                    <p className="blog-card__description">{post.description}</p>
                    <div className="blog-card__meta">
                      <span className="blog-card__date">{post.date}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Call to Action */}
          <div className="blog-cta">
            <div className="blog-cta__content">
              <h2 className="blog-cta__title">Ready to Transform Your Home Care Agency?</h2>
              <p className="blog-cta__description">
                Get expert insights delivered to your inbox and discover how TelescopeHR can
                streamline your operations, improve caregiver retention, and scale your business.
              </p>
              <div className="blog-cta__buttons">
                <button
                  className="blog-cta__button blog-cta__button--primary"
                  onClick={() => window.open('/contact', '_self')}
                >
                  Schedule a Demo
                </button>
                <button
                  className="blog-cta__button blog-cta__button--secondary"
                  onClick={() => window.open('https://telescopehr.com/blog', '_blank')}
                >
                  Visit Full Blog
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Blog;
