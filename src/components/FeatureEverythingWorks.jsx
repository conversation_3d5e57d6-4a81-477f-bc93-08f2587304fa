import './FeatureEverythingWorks.css';

const FeatureEverythingWorks = ({ 
  title = "Everything Just Works",
  subtitle = "Telescope is the complete home care solution with all the features you need in one platform",
  features = [
    {
      icon: "📍",
      title: "EVV & GPS Verification",
      description: "Automatic location tracking and electronic visit verification for compliance"
    },
    {
      icon: "📱",
      title: "Caregiver Mobile App",
      description: "Easy-to-use mobile app for caregivers to clock in, document care, and communicate"
    },
    {
      icon: "📅",
      title: "Smart Scheduling",
      description: "Intelligent scheduling system that optimizes routes and prevents conflicts"
    },
    {
      icon: "📝",
      title: "Task & ADL Tracking",
      description: "Comprehensive documentation of activities of daily living and care tasks"
    },
    {
      icon: "✍️",
      title: "Client eSignature",
      description: "Digital signatures for care plans, agreements, and documentation"
    },
    {
      icon: "👥",
      title: "Client & Family Portals",
      description: "Secure portals for clients and families to access care information"
    },
    {
      icon: "📋",
      title: "Custom Forms",
      description: "Create and manage custom forms for assessments and documentation"
    },
    {
      icon: "📊",
      title: "Surveys & Feedback",
      description: "Collect feedback from clients and families to improve care quality"
    },
    {
      icon: "💬",
      title: "Communication Hub",
      description: "Centralized communication between caregivers, clients, and families"
    },
    {
      icon: "🎯",
      title: "Hiring & Onboarding",
      description: "Streamlined recruitment and onboarding process for new caregivers"
    },
    {
      icon: "🛡️",
      title: "Background Screening",
      description: "Comprehensive background checks and ongoing monitoring"
    },
    {
      icon: "🎓",
      title: "Training & Certification",
      description: "Training modules and certification tracking for caregivers"
    },
    {
      icon: "💰",
      title: "Invoicing & Billing",
      description: "Automated billing and payment processing for your agency"
    }
  ]
}) => {
  return (
    <section className="feature-everything-works">
      <div className="feature-everything-works__container">
        <div className="feature-everything-works__header">
          <h2 className="feature-everything-works__title">{title}</h2>
          <p className="feature-everything-works__subtitle">{subtitle}</p>
        </div>

        <div className="feature-everything-works__grid">
          {features.map((feature, index) => (
            <div key={index} className="feature-everything-works__item">
              <div className="feature-everything-works__icon">
                {feature.icon}
              </div>
              <h3 className="feature-everything-works__item-title">
                {feature.title}
              </h3>
              <p className="feature-everything-works__item-description">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeatureEverythingWorks;
