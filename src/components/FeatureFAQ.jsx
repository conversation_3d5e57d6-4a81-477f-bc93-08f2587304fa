import { useState } from 'react';
import './FeatureFAQ.css';

const FeatureFAQ = ({ 
  title = "Frequently Asked Questions",
  subtitle = "Get answers to common questions about our features",
  faqs = []
}) => {
  const [openIndex, setOpenIndex] = useState(null);

  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  if (!faqs || faqs.length === 0) return null;

  return (
    <section className="feature-faq">
      <div className="feature-faq__container">
        <div className="feature-faq__header">
          <h2 className="feature-faq__title">{title}</h2>
          <p className="feature-faq__subtitle">{subtitle}</p>
        </div>

        <div className="feature-faq__list">
          {faqs.map((faq, index) => (
            <div key={index} className="feature-faq__item">
              <button
                className={`feature-faq__question ${openIndex === index ? 'feature-faq__question--open' : ''}`}
                onClick={() => toggleFAQ(index)}
              >
                <span className="feature-faq__question-text">{faq.question}</span>
                <svg 
                  className="feature-faq__question-icon" 
                  viewBox="0 0 24 24" 
                  fill="none"
                >
                  <path 
                    d="M6 9l6 6 6-6" 
                    stroke="currentColor" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round"
                  />
                </svg>
              </button>
              <div className={`feature-faq__answer ${openIndex === index ? 'feature-faq__answer--open' : ''}`}>
                <div className="feature-faq__answer-content">
                  <p className="feature-faq__answer-text">{faq.answer}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeatureFAQ;
