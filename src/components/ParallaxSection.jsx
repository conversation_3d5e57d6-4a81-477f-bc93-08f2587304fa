import { useEffect, useRef } from 'react';
import { getImage } from '../utils/images';
import './ParallaxSection.css';

const ParallaxSection = () => {
  const sectionRef = useRef(null);
  const backgroundRef = useRef(null);
  const backgroundImage = getImage('parallax', 'backgroundImage');

  useEffect(() => {
    const handleScroll = () => {
      if (!sectionRef.current || !backgroundRef.current) return;

      const rect = sectionRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;

      // Only apply parallax when section is in viewport
      if (rect.top < windowHeight && rect.bottom > 0) {
        // Calculate parallax offset based on scroll position
        const scrolled = windowHeight - rect.top;
        const parallax = scrolled * 0.5; // Parallax speed factor

        // Apply transform to background
        backgroundRef.current.style.transform = `translate3d(0, ${parallax}px, 0)`;
      }
    };

    // Throttle scroll events for performance
    let ticking = false;
    const throttledScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', throttledScroll, { passive: true });
    handleScroll(); // Initial call

    return () => {
      window.removeEventListener('scroll', throttledScroll);
    };
  }, []);

  return (
    <div className="parallax-container">
      {/* Parallax Background Section */}
      <section className="parallax-section" ref={sectionRef}>
        <div className="parallax-section__background" ref={backgroundRef}>
          {/* Background Image Placeholder */}
          <div className="parallax-section__image-bg">
            <div className="parallax-section__image-container">
              {/* Mac-style Header */}
              <div className="parallax-section__mac-header">
                <div className="parallax-section__mac-buttons">
                  <div className="parallax-section__mac-button parallax-section__mac-button--close"></div>
                  <div className="parallax-section__mac-button parallax-section__mac-button--minimize"></div>
                  <div className="parallax-section__mac-button parallax-section__mac-button--maximize"></div>
                </div>
              </div>

              <img
                src={backgroundImage.src}
                alt={backgroundImage.alt}
                className="parallax-section__main-image"
                // onLoad={(e) => {
                //   // Replace with actual image when available
                //   if (backgroundImage.src !== backgroundImage.placeholder) {
                //     e.target.src = backgroundImage.src;
                //   }
                // }}
                // onError={(e) => {
                //   // Fallback to placeholder if actual image fails
                //   e.target.src = backgroundImage.placeholder;
                // }}
              />
            </div>
          </div>

          {/* Gradient Overlay for better text readability */}
          <div className="parallax-section__overlay-gradient"></div>
        </div>
      </section>

      {/* Content Section that slides over */}
      <section className="parallax-content">
        <div className="parallax-content__container">
          <div className="parallax-content__text">
            <h2 className="parallax-content__title">
              See Your Agency's Success in Real-Time
            </h2>
            <p className="parallax-content__description">
              Get instant insights into your operations with our comprehensive dashboard.
              Track performance, manage schedules, and grow your business with data-driven decisions.
            </p>
            <div className="parallax-content__features">
              <div className="feature-item">
                <div className="feature-item__icon">📊</div>
                <span>Real-time Analytics</span>
              </div>
              <div className="feature-item">
                <div className="feature-item__icon">⚡</div>
                <span>Instant Updates</span>
              </div>
              <div className="feature-item">
                <div className="feature-item__icon">📱</div>
                <span>Mobile Ready</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ParallaxSection;
