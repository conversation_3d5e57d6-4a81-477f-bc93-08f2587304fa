// Central image management for Telescope website
// This file contains all image placeholders and will be updated with actual images

const images = {
  // Hero Section
  hero: {
    appScreenshot: {
      src: '/images/hero/app-screenshot.png',
      alt: 'Telescope home care app screenshot',
      placeholder: '📱',
      description: 'Main app interface showing dashboard and key features'
    },
    backgroundPattern: {
      src: '/images/hero/background-pattern.svg',
      alt: 'Hero background pattern',
      placeholder: '🌟',
      description: 'Decorative background pattern for hero section'
    }
  },

  // Review Badges Section
  reviewBadges: {
    trustpilot: {
      src: '/images/badges/trustpilot-logo.png',
      alt: 'Trustpilot 5-star rating',
      placeholder: '⭐',
      description: 'Trustpilot review platform logo'
    },
    appStore: {
      src: '/images/badges/app-store-logo.png',
      alt: 'App Store 5-star rating',
      placeholder: '📱',
      description: 'Apple App Store logo and rating'
    },
    capterra: {
      src: '/images/badges/capterra-logo.png',
      alt: 'Capterra 5-star rating',
      placeholder: '🏆',
      description: 'Capterra software review platform logo'
    }
  },

  // Trusted By Section
  trustedBy: {
    companies: [
      {
        name: 'CareFirst',
        src: '/images/logos/carefirst-logo.png',
        alt: 'CareFirst home care agency logo',
        placeholder: '🏥'
      },
      {
        name: 'Compassionate Care',
        src: '/images/logos/compassionate-care-logo.png',
        alt: 'Compassionate Care agency logo',
        placeholder: '❤️'
      },
      {
        name: 'Guardian Angels',
        src: '/images/logos/guardian-angels-logo.png',
        alt: 'Guardian Angels care services logo',
        placeholder: '👼'
      },
      {
        name: 'Premier Health',
        src: '/images/logos/premier-health-logo.png',
        alt: 'Premier Health services logo',
        placeholder: '🏥'
      },
      {
        name: 'Caring Hands',
        src: '/images/logos/caring-hands-logo.png',
        alt: 'Caring Hands home care logo',
        placeholder: '🤝'
      }
    ]
  },

  // Why Telescope Section
  whyTelescope: {
    mainImage: {
      src: '/images/schedule.svg',
      alt: 'Professional home care team using Telescope app dashboard',
      placeholder: 'https://via.placeholder.com/600x400/f8faff/201c44?text=Care+Team+Dashboard',
      description: 'Care team collaborating with Telescope platform dashboard'
    }
  },

  // Parallax Section
  parallax: {
    backgroundImage: {
      src: '/images/para.svg',
      alt: 'Home care professional with client',
      placeholder: 'https://via.placeholder.com/1200x600/201c44/ffffff?text=Home+Care+Excellence',
      description: 'Professional home care scene for parallax background'
    }
  },

  // Core Features Section
  coreFeatures: {
    evvGps: {
      src: '/images/evv.svg',
      alt: 'EVV and GPS verification interface screenshot',
      placeholder: '/images/evv.svg',
      description: 'Electronic visit verification with GPS tracking interface'
    },
    caregiverApp: {
      src: '/images/mobile.svg',
      alt: 'Caregiver mobile app interface screenshot',
      placeholder: '/images/mobile.svg',
      description: 'Mobile app interface for caregivers with task management'
    },
    scheduling: {
      src: '/images/schedule.svg',
      alt: 'Smart scheduling dashboard screenshot',
      placeholder: '/images/schedule.svg',
      description: 'Intelligent scheduling system dashboard interface'
    },
    taskTracking: {
      src: '/images/task.svg',
      alt: 'Task and ADL tracking interface screenshot',
      placeholder: '/images/task.svg',
      description: 'Task documentation and ADL tracking system interface'
    },
    eSignature: {
      src: '/client.png',
      alt: 'Client eSignature feature screenshot',
      placeholder: '/client.png',
      description: 'Digital signature collection interface'
    }
  },

  // Grouped Features (Bento Grid)
  groupedFeatures: {
    // Core Features
    evvGps: {
      src: '/images/features/evv-gps-verification.jpg',
      alt: 'EVV and GPS verification interface',
      placeholder: 'https://via.placeholder.com/600x400/f8faff/006ac1?text=EVV+%26+GPS+Verification',
      description: 'Electronic visit verification with GPS tracking interface'
    },
    caregiverApp: {
      src: '/images/features/caregiver-mobile-app.jpg',
      alt: 'Caregiver mobile app interface',
      placeholder: 'https://via.placeholder.com/600x400/f8faff/006ac1?text=Caregiver+Mobile+App',
      description: 'Mobile app interface for caregivers'
    },
    scheduling: {
      src: '/images/features/smart-scheduling.jpg',
      alt: 'Smart scheduling dashboard',
      placeholder: 'https://via.placeholder.com/600x400/f8faff/006ac1?text=Smart+Scheduling',
      description: 'Intelligent scheduling system dashboard'
    },
    taskTracking: {
      src: '/images/features/task-adl-tracking.jpg',
      alt: 'Task and ADL tracking interface',
      placeholder: 'https://via.placeholder.com/600x400/f8faff/006ac1?text=Task+%26+ADL+Tracking',
      description: 'Task documentation and ADL tracking system'
    },
    eSignature: {
      src: '/images/features/client-esignature.jpg',
      alt: 'Client eSignature feature',
      placeholder: 'https://via.placeholder.com/600x400/f8faff/006ac1?text=Client+eSignature',
      description: 'Digital signature collection interface'
    },
    // Additional Features
    customForms: {
      src: '/images/features/custom-forms.jpg',
      alt: 'Custom forms builder interface',
      placeholder: 'https://via.placeholder.com/600x400/f8faff/006ac1?text=Custom+Forms',
      description: 'Form builder and management system'
    },
    textMessaging: {
      src: '/images/features/text-messaging.jpg',
      alt: 'Text messaging communication',
      placeholder: 'https://via.placeholder.com/600x400/f8faff/006ac1?text=Text+Messaging',
      description: 'HIPAA-compliant messaging platform'
    },
    surveys: {
      src: '/images/features/client-surveys.jpg',
      alt: 'Client and caregiver surveys',
      placeholder: 'https://via.placeholder.com/600x400/f8faff/006ac1?text=Client+Surveys',
      description: 'Survey and feedback collection system'
    },
    portals: {
      src: '/images/features/client-portals.jpg',
      alt: 'Client and family portals',
      placeholder: 'https://via.placeholder.com/600x400/f8faff/006ac1?text=Client+Portals',
      description: 'Client and family access portals'
    },
    hiring: {
      src: '/images/features/hiring-onboarding.jpg',
      alt: 'Hiring and onboarding system',
      placeholder: 'https://via.placeholder.com/600x400/f8faff/006ac1?text=Hiring+%26+Onboarding',
      description: 'Recruitment and onboarding platform'
    },
    training: {
      src: '/images/features/caregiver-training.jpg',
      alt: 'Caregiver training modules',
      placeholder: 'https://via.placeholder.com/600x400/f8faff/006ac1?text=Caregiver+Training',
      description: 'Training and certification system'
    },
    backgroundScreening: {
      src: '/images/features/background-screening.jpg',
      alt: 'Background screening interface',
      placeholder: 'https://via.placeholder.com/600x400/f8faff/006ac1?text=Background+Screening',
      description: 'Background check and verification system'
    },
    invoicing: {
      src: '/images/features/invoicing-system.jpg',
      alt: 'Invoicing and billing system',
      placeholder: 'https://via.placeholder.com/600x400/f8faff/006ac1?text=Invoicing+%26+Billing',
      description: 'Billing and payment processing interface'
    }
  },

  // Testimonials Section
  testimonials: {
    mainImage: {
      src: '/succ.jpg',
      alt: 'Happy clients and caregivers using Telescope',
      placeholder: '/succ.jpg',
      description: 'Visual representation of client testimonials and success stories'
    },
    clientPhotos: {
      sarahJohnson: {
        src: '/sarah.jpg',
        alt: 'Sarah Johnson, CareFirst Director',
        placeholder: '/sarah.jpg',
        description: 'Professional headshot of Sarah Johnson'
      },
      michaelChen: {
        src: '/chen.jpg',
        alt: 'Michael Chen, Compassionate Care CEO',
        placeholder: '/chen.jpg',
        description: 'Professional headshot of Michael Chen'
      },
      lisaRodriguez: {
        src: '/lisa.jpg',
        alt: 'Lisa Rodriguez, Guardian Angels Manager',
        placeholder: '/lisa.jpg',
        description: 'Professional headshot of Lisa Rodriguez'
      },
      davidThompson: {
        src: '/david.jpg',
        alt: 'David Thompson, Premier Health Director',
        placeholder: 'david.jpg',
        description: 'Professional headshot of David Thompson'
      },
      maria: {
        src: '/images/testimonials/maria-j.jpg',
        alt: 'Maria J., Agency Administrator',
        placeholder: 'https://via.placeholder.com/400x400/f8faff/006ac1?text=Maria+J.',
        description: 'Professional headshot of Maria J., Agency Administrator from Austin, TX'
      }
    }
  },

  // Full Support Section
  fullSupport: {
    supportTeam: {
      src: '/trusted.png',
      alt: 'Telescope support team providing excellent service',
      placeholder: '/trusted.png',
      description: 'Professional support team photo showing dedication to customer service'
    },
    supportFeatures: {
      fastResponse: {
        src: '/support.png',
        alt: 'Fast response time demonstration',
        placeholder: '/support.png',
        description: 'Visual showing quick response time capabilities'
      },
      expertise: {
        src: '/sec.png',
        alt: 'Industry expertise demonstration',
        placeholder: '/sec.png',
        description: 'Visual showing deep industry knowledge and expertise'
      },
      availability: {
        src: '/night.png',
        alt: '24/7 availability demonstration',
        placeholder: '/night.png',
        description: 'Visual showing round-the-clock support availability'
      }
    }
  },

  // ROI Section
  roi: {
    dashboard: {
      src: '/images/roi/roi-dashboard.png',
      alt: 'ROI analytics dashboard',
      placeholder: '📊',
      description: 'Return on investment tracking dashboard'
    },
    charts: {
      timeSavings: {
        src: '/images/roi/time-savings-chart.png',
        alt: 'Time savings analytics chart',
        placeholder: '⏰'
      },
      efficiency: {
        src: '/images/roi/efficiency-metrics.png',
        alt: 'Efficiency improvement metrics',
        placeholder: '📈'
      }
    }
  },

  // General UI Elements
  ui: {
    placeholderAvatar: {
      src: '/images/ui/placeholder-avatar.svg',
      alt: 'User avatar placeholder',
      placeholder: '👤',
      description: 'Default user profile image'
    },
    loadingSpinner: {
      src: '/images/ui/loading-spinner.svg',
      alt: 'Loading animation',
      placeholder: '⏳',
      description: 'Loading state indicator'
    },
    errorImage: {
      src: '/images/ui/error-placeholder.svg',
      alt: 'Image failed to load',
      placeholder: '🖼️',
      description: 'Fallback for broken images'
    }
  }
};

// Helper functions for image management
export const getImage = (category, name, subName = null) => {
  try {
    if (subName) {
      return images[category]?.[name]?.[subName] || images.ui.errorImage;
    }
    return images[category]?.[name] || images.ui.errorImage;
  } catch (error) {
    console.warn(`Image not found: ${category}.${name}${subName ? `.${subName}` : ''}`);
    return images.ui.errorImage;
  }
};

export const getPlaceholder = (category, name, subName = null) => {
  const image = getImage(category, name, subName);
  return image.placeholder || '🖼️';
};

export const getAllImages = () => images;

export const getImagesByCategory = (category) => {
  return images[category] || {};
};

// Image preloading utility
export const preloadImages = (imageList) => {
  imageList.forEach(imagePath => {
    const img = new Image();
    img.src = imagePath;
  });
};

// Get all image sources for preloading
export const getAllImageSources = () => {
  const sources = [];
  
  const extractSources = (obj) => {
    Object.values(obj).forEach(item => {
      if (typeof item === 'object') {
        if (item.src) {
          sources.push(item.src);
        } else {
          extractSources(item);
        }
      }
    });
  };
  
  extractSources(images);
  return sources;
};

export default images;
