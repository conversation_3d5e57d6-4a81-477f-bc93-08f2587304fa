// Central image management for Features page
// This file contains all image placeholders and paths for the features section

const featureImages = {
  // Feature Hero Images - Web and Mobile Interfaces
  heroInterfaces: {
    'evv-gps': {
      web: {
        src: '/images/evv.svg',
        alt: 'EVV & GPS Verification web dashboard',
        placeholder: '📍',
        description: 'Web dashboard showing GPS tracking and EVV compliance'
      },
      // mobile: {
      //   src: '/images/features/evv-gps/mobile-interface.png',
      //   alt: 'EVV & GPS Verification mobile app',
      //   placeholder: '📱',
      //   description: 'Mobile app interface for caregiver check-in/out'
      // }
    },
    'caregiver-app': {
      web: {
        src: '/images/features/caregiver-app/web-interface.png',
        alt: 'Caregiver management web dashboard',
        placeholder: '👥',
        description: 'Web dashboard for managing caregiver activities'
      },
      mobile: {
        src: '/images/features/caregiver-app/mobile-interface.png',
        alt: 'Caregiver mobile app interface',
        placeholder: '📱',
        description: 'Mobile app for caregivers to manage tasks and communication'
      }
    },
    'smart-scheduling': {
      web: {
        src: '/images/features/scheduling/web-interface.png',
        alt: 'Smart scheduling web dashboard',
        placeholder: '📅',
        description: 'Web dashboard showing intelligent scheduling system'
      },
      mobile: {
        src: '/images/features/scheduling/mobile-interface.png',
        alt: 'Scheduling mobile app',
        placeholder: '📱',
        description: 'Mobile app for viewing and managing schedules'
      }
    },
    'task-tracking': {
      web: {
        src: '/images/features/task-tracking/web-interface.png',
        alt: 'Task & ADL tracking web dashboard',
        placeholder: '📝',
        description: 'Web dashboard for tracking activities of daily living'
      },
      mobile: {
        src: '/images/features/task-tracking/mobile-interface.png',
        alt: 'Task tracking mobile app',
        placeholder: '📱',
        description: 'Mobile app for documenting care tasks and ADLs'
      }
    },
    'esignature': {
      web: {
        src: '/images/features/esignature/web-interface.png',
        alt: 'eSignature web dashboard',
        placeholder: '✍️',
        description: 'Web dashboard for managing digital signatures'
      },
      mobile: {
        src: '/images/features/esignature/mobile-interface.png',
        alt: 'eSignature mobile app',
        placeholder: '📱',
        description: 'Mobile app for collecting digital signatures'
      }
    }
  },

  // Problem-Solution Section Images
  problemSolutions: {
    complianceViolations: {
      src: '/images/comp.svg',
      alt: 'EVV compliance violations illustration',
      placeholder: '⚠️',
      description: 'Illustration showing compliance violation risks'
    },
    manualPaperwork: {
      src: '/images/visit.svg',
      alt: 'Manual paperwork burden illustration',
      placeholder: '�',
      description: 'Illustration showing manual paperwork challenges'
    },
    timeTracking: {
      src: '/images/fr.svg',
      alt: 'Time tracking accuracy illustration',
      placeholder: '⏰',
      description: 'Illustration showing time tracking accuracy issues'
    }
  },

  // Why Use Software Section Images
  whyUseSoftware: {
    protectAgency: {
      src: '/images/evv.svg',
      alt: 'Agency protection through compliance',
      placeholder: '🛡️',
      description: 'Illustration showing agency protection benefits'
    },
    saveTime: {
      src: '/images/work.svg',
      alt: 'Time savings through automation',
      placeholder: '⚡',
      description: 'Illustration showing time savings through automation'
    }
  },

  // How It Works Section Images
  howItWorks: {
    step1: {
      src: '/images/features/how-it-works/step-1.png',
      alt: 'Step 1: Caregiver arrives and checks in',
      placeholder: '1️⃣',
      description: 'Caregiver using mobile app to check in at client location'
    },
    step2: {
      src: '/images/features/how-it-works/step-2.png',
      alt: 'Step 2: GPS and time automatically recorded',
      placeholder: '2️⃣',
      description: 'Automatic GPS and time recording during visit'
    },
    step3: {
      src: '/images/features/how-it-works/step-3.png',
      alt: 'Step 3: Care tasks documented in real-time',
      placeholder: '3️⃣',
      description: 'Real-time documentation of care tasks and activities'
    },
    step4: {
      src: '/images/features/how-it-works/step-4.png',
      alt: 'Step 4: Compliance reports generated automatically',
      placeholder: '4️⃣',
      description: 'Automatic generation of compliance reports'
    }
  },

  // Testimonial Images
  testimonials: {
    clientPhotos: {
      maria: {
        src: '/agency.jpg',
        alt: 'Maria J., Agency Administrator',
        placeholder: '👩‍💼',
        description: 'Professional headshot of Maria Johnson, Agency Administrator'
      },
      david: {
        src: '/images/testimonials/david-smith.jpg',
        alt: 'David S., Operations Manager',
        placeholder: '👨‍💼',
        description: 'Professional headshot of David Smith, Operations Manager'
      },
      sarah: {
        src: '/images/testimonials/sarah-wilson.jpg',
        alt: 'Sarah W., Compliance Director',
        placeholder: '�‍💼',
        description: 'Professional headshot of Sarah Wilson, Compliance Director'
      }
    }
  },

  // Manager Parallax Section Images
  managerBenefits: {
    timesSavings: {
      src: '/images/features/manager/time-savings.png',
      alt: 'Manager time savings illustration',
      placeholder: '⏱️',
      description: 'Illustration showing manager productivity improvements'
    },
    efficiency: {
      src: '/images/features/manager/efficiency.png',
      alt: 'Operational efficiency illustration',
      placeholder: '�',
      description: 'Illustration showing operational efficiency gains'
    }
  },

  // Everything Just Works Section Icons
  allFeatures: {
    evvGps: {
      icon: '📍',
      title: 'EVV & GPS Verification'
    },
    caregiverApp: {
      icon: '📱',
      title: 'Caregiver Mobile App'
    },
    scheduling: {
      icon: '📅',
      title: 'Smart Scheduling'
    },
    taskTracking: {
      icon: '📝',
      title: 'Task & ADL Tracking'
    },
    eSignature: {
      icon: '✍️',
      title: 'Client eSignature'
    },
    portals: {
      icon: '👥',
      title: 'Client & Family Portals'
    },
    forms: {
      icon: '📋',
      title: 'Custom Forms'
    },
    surveys: {
      icon: '📊',
      title: 'Surveys & Feedback'
    },
    communication: {
      icon: '💬',
      title: 'Communication Hub'
    },
    hiring: {
      icon: '🎯',
      title: 'Hiring & Recruitment'
    },
    backgroundScreening: {
      icon: '🔍',
      title: 'Background Screening'
    },
    training: {
      icon: '🎓',
      title: 'Training Management'
    },
    invoicing: {
      icon: '💰',
      title: 'Invoicing & Billing'
    }
  }
};

// Helper function to get feature images
export const getFeatureImage = (section, subsection, key = null) => {
  try {
    if (key) {
      const imageData = featureImages[section]?.[subsection]?.[key];
      // If the specific key doesn't exist, return placeholder
      if (!imageData) {
        return {
          src: '/images/placeholder.png',
          alt: 'Image placeholder',
          placeholder: '🖼️',
          description: 'Placeholder image'
        };
      }
      return imageData;
    } else if (subsection) {
      return featureImages[section]?.[subsection] || {
        src: '/images/placeholder.png',
        alt: 'Image placeholder',
        placeholder: '🖼️',
        description: 'Placeholder image'
      };
    } else {
      return featureImages[section] || {};
    }
  } catch (error) {
    console.warn('Feature image not found:', { section, subsection, key });
    return {
      src: '/images/placeholder.png',
      alt: 'Image placeholder',
      placeholder: '🖼️',
      description: 'Placeholder image'
    };
  }
};

// Helper function to check if a feature image exists
export const hasFeatureImage = (section, subsection, key = null) => {
  try {
    if (key) {
      return !!(featureImages[section]?.[subsection]?.[key]);
    } else if (subsection) {
      return !!(featureImages[section]?.[subsection]);
    } else {
      return !!(featureImages[section]);
    }
  } catch (error) {
    return false;
  }
};

export default featureImages;
