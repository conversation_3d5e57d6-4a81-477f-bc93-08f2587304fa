/* Europa Grotesk SH Font Family CSS */

/* Extra Light (XLig) */
@font-face {
  font-family: 'Europa Grotesk SH';
  src: url('./EuropaGroteskSH-XLig.otf') format('opentype');
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Europa Grotesk SH';
  src: url('./EuropaGroteskSH-XLigIta.otf') format('opentype');
  font-weight: 200;
  font-style: italic;
  font-display: swap;
}

/* Light (Lig) */
@font-face {
  font-family: 'Europa Grotesk SH';
  src: url('./EuropaGroteskSH-Lig.otf') format('opentype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Europa Grotesk SH';
  src: url('./EuropaGroteskSH-LigIta.otf') format('opentype');
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Europa Grotesk SH';
  src: url('./EuropaGroteskSH-LigCon.otf') format('opentype');
  font-weight: 300;
  font-style: normal;
  font-stretch: condensed;
  font-display: swap;
}

@font-face {
  font-family: 'Europa Grotesk SH';
  src: url('./EuropaGroteskSH-LigConIta.otf') format('opentype');
  font-weight: 300;
  font-style: italic;
  font-stretch: condensed;
  font-display: swap;
}

@font-face {
  font-family: 'Europa Grotesk SH';
  src: url('./EuropaGroteskSH-LigEx.otf') format('opentype');
  font-weight: 300;
  font-style: normal;
  font-stretch: expanded;
  font-display: swap;
}

/* Regular (Reg) */
@font-face {
  font-family: 'Europa Grotesk SH';
  src: url('./EuropaGroteskSH-Reg.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Europa Grotesk SH';
  src: url('./EuropaGroteskSH-Ita.otf') format('opentype');
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Europa Grotesk SH';
  src: url('./EuropaGroteskSH-Con.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-stretch: condensed;
  font-display: swap;
}

@font-face {
  font-family: 'Europa Grotesk SH';
  src: url('./EuropaGroteskSH-ConIta.otf') format('opentype');
  font-weight: 400;
  font-style: italic;
  font-stretch: condensed;
  font-display: swap;
}

@font-face {
  font-family: 'Europa Grotesk SH';
  src: url('./EuropaGroteskSH-Ex.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-stretch: expanded;
  font-display: swap;
}

/* Medium (Med) */
@font-face {
  font-family: 'Europa Grotesk SH';
  src: url('./EuropaGroteskSH-Med.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Europa Grotesk SH';
  src: url('./EuropaGroteskSH-MedIta.otf') format('opentype');
  font-weight: 500;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Europa Grotesk SH';
  src: url('./EuropaGroteskSH-MedCon.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
  font-stretch: condensed;
  font-display: swap;
}

@font-face {
  font-family: 'Europa Grotesk SH';
  src: url('./EuropaGroteskSH-MedEx.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
  font-stretch: expanded;
  font-display: swap;
}

/* Demi Bold (DemBol) */
@font-face {
  font-family: 'Europa Grotesk SH';
  src: url('./EuropaGroteskSH-DemBol.otf') format('opentype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Europa Grotesk SH';
  src: url('./EuropaGroteskSH-DemBolIta.otf') format('opentype');
  font-weight: 600;
  font-style: italic;
  font-display: swap;
}

/* Bold (Bol) */
@font-face {
  font-family: 'Europa Grotesk SH';
  src: url('./EuropaGroteskSH-Bol.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Europa Grotesk SH';
  src: url('./EuropaGroteskSH-BolIta.otf') format('opentype');
  font-weight: 700;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Europa Grotesk SH';
  src: url('./EuropaGroteskSH-BolCon.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
  font-stretch: condensed;
  font-display: swap;
}

@font-face {
  font-family: 'Europa Grotesk SH';
  src: url('./EuropaGroteskSH-BolEx.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
  font-stretch: expanded;
  font-display: swap;
}

/* Additional TTF Medium variant */
@font-face {
  font-family: 'Europa Grotesk Medium';
  src: url('./Grotesk Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

/* CSS Custom Properties for easy usage */
:root {
  --font-europa-grotesk: 'Europa Grotesk SH', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-europa-grotesk-medium: 'Europa Grotesk Medium', 'Europa Grotesk SH', sans-serif;
}

/* Utility classes for common font weights and styles */
.europa-grotesk {
  font-family: var(--font-europa-grotesk);
}

.europa-grotesk-extra-light {
  font-family: var(--font-europa-grotesk);
  font-weight: 200;
}

.europa-grotesk-light {
  font-family: var(--font-europa-grotesk);
  font-weight: 300;
}

.europa-grotesk-regular {
  font-family: var(--font-europa-grotesk);
  font-weight: 400;
}

.europa-grotesk-medium {
  font-family: var(--font-europa-grotesk);
  font-weight: 500;
}

.europa-grotesk-demi-bold {
  font-family: var(--font-europa-grotesk);
  font-weight: 600;
}

.europa-grotesk-bold {
  font-family: var(--font-europa-grotesk);
  font-weight: 700;
}

.europa-grotesk-condensed {
  font-family: var(--font-europa-grotesk);
  font-stretch: condensed;
}

.europa-grotesk-expanded {
  font-family: var(--font-europa-grotesk);
  font-stretch: expanded;
}
