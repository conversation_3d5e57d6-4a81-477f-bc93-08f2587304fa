
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import './App.css'
import Header from './components/Header'
import Hero from './components/Hero'

import WhyTelescope from './components/WhyTelescope'
import CoreFeatures from './components/CoreFeatures'
import GroupedFeatures from './components/GroupedFeatures'
import Testimonials from './components/Testimonials'
import FullSupport from './components/FullSupport'
import ReviewBadges from './components/ReviewBadges'
import TrustedBy from './components/TrustedBy'
import ROISection from './components/ROISection'
import Footer from './components/Footer'
import Features from './components/Features'
import ContactUs from './components/ContactUs'
import Pricing from './components/Pricing'
import TermsOfService from './components/TermsOfService'
import PrivacyPolicy from './components/PrivacyPolicy'
import Videos from './components/Videos'
import Blog from './components/Blog'
import ScrollToTop from './components/ScrollToTop'

// Home Page Component
const HomePage = () => (
  <>
    <Hero />
    <ReviewBadges />
    <TrustedBy />
    <WhyTelescope />
    <CoreFeatures />
    <GroupedFeatures />
    <Testimonials />
    <FullSupport />
    <ROISection />
  </>
)

function App() {
  return (
    <Router>
      <div className="App">
        <ScrollToTop />
        <Header />
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/features" element={<Features />} />
          <Route path="/features/:featureId" element={<Features />} />
          <Route path="/pricing" element={<Pricing />} />
          <Route path="/videos" element={<Videos />} />
          <Route path="/blog" element={<Blog />} />
          <Route path="/terms" element={<TermsOfService />} />
          <Route path="/privacy" element={<PrivacyPolicy />} />
          <Route path="/contact" element={<ContactUs />} />
        </Routes>
        <Footer />
      </div>
    </Router>
  )
}

export default App
