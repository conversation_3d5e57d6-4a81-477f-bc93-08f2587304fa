# TelescopeHR Email Templates

This directory contains HTML email templates for TelescopeHR's form submissions. All templates are designed to be compatible with major email clients including Gmail, Outlook, Apple Mail, and others.

## Templates Overview

### Quote Form Templates
- **`quote-form-admin.html`** - Admin notification for new quote requests
- **`quote-form-user.html`** - User confirmation for quote submissions

### Contact Form Templates
- **`contact-form-admin.html`** - Admin notification for new contact submissions
- **`contact-form-user.html`** - User confirmation for contact form submissions

### Subscription Templates
- **`subscription-admin.html`** - Admin notification for new newsletter subscriptions
- **`subscription-welcome.html`** - Welcome email for new subscribers

## Template Variables

Each template uses placeholder variables that should be replaced with actual data when sending emails:

### Quote Form Variables
- `{{CUSTOMER_NAME}}` - Customer's full name
- `{{CUSTOMER_EMAIL}}` - Customer's email address
- `{{CUSTOMER_PHONE}}` - Customer's phone number
- `{{INDUSTRY}}` - Selected industry type
- `{{EMPLOYEE_COUNT}}` - Number of employees
- `{{TIMESTAMP}}` - Form submission timestamp

### Contact Form Variables
- `{{CUSTOMER_NAME}}` - Customer's full name
- `{{CUSTOMER_EMAIL}}` - Customer's email address
- `{{CUSTOMER_PHONE}}` - Customer's phone number
- `{{COMPANY_NAME}}` - Customer's company name
- `{{MESSAGE}}` - Customer's message content
- `{{TIMESTAMP}}` - Form submission timestamp

### Subscription Variables
- `{{SUBSCRIBER_EMAIL}}` - Subscriber's email address
- `{{SUBSCRIPTION_SOURCE}}` - Source of subscription (e.g., "Website Footer")
- `{{SUBSCRIPTION_DATE}}` - Date of subscription
- `{{TIMESTAMP}}` - Subscription timestamp
- `{{UNSUBSCRIBE_LINK}}` - Unsubscribe link for the subscriber

## Email Client Compatibility

These templates are designed with email client compatibility in mind:

### Features Used
- **Table-based layout** - Maximum compatibility across all email clients
- **Inline CSS** - Ensures styling works in clients that strip `<style>` tags
- **MSO conditionals** - Special handling for Microsoft Outlook
- **Web-safe fonts** - Arial fallback for universal support
- **Responsive design** - Adapts to mobile devices

### Tested Clients
- Gmail (Web, iOS, Android)
- Outlook (2016, 2019, 365, Web)
- Apple Mail (macOS, iOS)
- Yahoo Mail
- Thunderbird
- Mobile email clients

## Implementation Guide

### 1. Server-Side Integration

```javascript
// Example Node.js implementation
const fs = require('fs');
const nodemailer = require('nodemailer');

function sendQuoteConfirmation(formData) {
  // Load template
  let template = fs.readFileSync('./email-templates/quote-form-user.html', 'utf8');
  
  // Replace variables
  template = template.replace('{{CUSTOMER_NAME}}', formData.name);
  template = template.replace('{{CUSTOMER_EMAIL}}', formData.email);
  template = template.replace('{{INDUSTRY}}', formData.industry);
  template = template.replace('{{EMPLOYEE_COUNT}}', formData.employeeCount);
  
  // Send email
  const mailOptions = {
    from: '<EMAIL>',
    to: formData.email,
    subject: 'Quote Request Received - TelescopeHR',
    html: template
  };
  
  transporter.sendMail(mailOptions);
}
```

### 2. Variable Replacement

Always replace ALL template variables before sending emails:

```javascript
// Helper function for variable replacement
function replaceTemplateVariables(template, data) {
  return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
    return data[key] || match; // Keep placeholder if no data found
  });
}
```

### 3. Email Headers

Set appropriate email headers for better deliverability:

```javascript
const mailOptions = {
  from: 'TelescopeHR <<EMAIL>>',
  to: recipient,
  subject: subject,
  html: template,
  headers: {
    'X-Mailer': 'TelescopeHR',
    'X-Priority': '3',
    'List-Unsubscribe': '<mailto:<EMAIL>>'
  }
};
```

## Design Features

### Brand Consistency
- **TelescopeHR blue gradient** (#006ac1 to #1a7dd1)
- **Professional typography** with proper hierarchy
- **Consistent spacing** and padding throughout
- **Brand colors** and styling elements

### User Experience
- **Clear call-to-action buttons** with hover effects
- **Responsive design** for mobile devices
- **Professional layout** with proper information hierarchy
- **Easy-to-read typography** with optimal line spacing

### Compliance
- **Google API compliance** footer text included
- **Unsubscribe links** in subscription emails
- **Professional disclaimers** and contact information
- **GDPR-friendly** language and structure

## Testing

Before deploying, test templates in multiple email clients:

1. **Litmus** or **Email on Acid** for comprehensive testing
2. **Gmail** web interface and mobile app
3. **Outlook** desktop and web versions
4. **Apple Mail** on macOS and iOS
5. **Mobile devices** with various screen sizes

## Maintenance

### Regular Updates
- Update copyright year annually
- Review and update contact information
- Test templates with new email client versions
- Update compliance text as needed

### Performance Monitoring
- Track email open rates
- Monitor click-through rates on CTAs
- Test deliverability across different providers
- Gather user feedback on email experience

## Support

For questions about these email templates or implementation assistance, contact the development team or refer to the main TelescopeHR documentation.
